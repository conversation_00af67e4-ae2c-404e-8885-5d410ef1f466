## 版本

- node版本需为16

## 说明文档
项目开发、部署等说明都在[wiki](https://github.com/renrenio/renren-fast-vue/wiki)中。


## 更新日志
每个版本的详细更改都记录在[release notes](https://github.com/renrenio/renren-fast-vue/releases)中。

## 下载
npm i --registry=https://registry.npm.taobao.org

## 分支
- dev(本地开发)
- test(测试环境)
- master

## 打补丁
```shell
yarn patch-package [包名]
# or
npx patch-package [包名]
```



## 注意
patches文件不要删除，不要修改

vxe-table 的 vex-select 组件在清除[clearable]选项的时候会被值赋值为null,axios发请求的时候会过滤掉值为null的字段,导致接口报错，建议使用 el-select
