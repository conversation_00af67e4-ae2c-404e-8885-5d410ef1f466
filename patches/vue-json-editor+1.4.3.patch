diff --git a/node_modules/vue-json-editor/vue-json-editor.vue b/node_modules/vue-json-editor/vue-json-editor.vue
index ced9ece..650477b 100644
--- a/node_modules/vue-json-editor/vue-json-editor.vue
+++ b/node_modules/vue-json-editor/vue-json-editor.vue
@@ -127,30 +127,44 @@ export default {
 };
 </script>
 
-<style scoped>
-  .ace_line_group {
-    text-align: left;
-  }
-  .json-editor-container {
-    display: flex;
-    width: 100%;
-  }
-  .json-editor-container .tree-mode {
-    width: 50%;
-  }
-  .json-editor-container .code-mode {
-    flex-grow: 1;
+<style scoped lang='scss'>
+
+  .jsoneditor-vue {
+    min-height: 600px;
+
+    &::v-deep {
+
+      .jsoneditor-mode-code {
+        position: absolute;
+        top: 0;
+        bottom: 0;
+      }
+
+      .ace_line_group {
+        text-align: left;
+      }
+      .json-editor-container {
+        display: flex;
+        width: 100%;
+      }
+      .json-editor-container .tree-mode {
+        width: 50%;
+      }
+      .json-editor-container .code-mode {
+        flex-grow: 1;
+      }
+
+      div.jsoneditor-tree{
+        min-height: 350px;
+      }
+    }
   }
+
   .jsoneditor-btns{
     text-align: center;
     margin-top:10px;
   }
-  .jsoneditor-vue .jsoneditor-outer{
-    min-height:150px;
-  }
-  .jsoneditor-vue div.jsoneditor-tree{
-    min-height: 350px;
-  }
+
   .json-save-btn{
     background-color: #20A0FF;
     border: none;
