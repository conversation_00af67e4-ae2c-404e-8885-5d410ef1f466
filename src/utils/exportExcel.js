import { utils, writeFile } from 'xlsx'

export function generateDateTimeString() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`
}

/**
 *
 * @param {*} data  表格数据源
 * @param {*} columns 表格列配置
 */

export const exportExcel = (data, columns) => {
  const res = data.map(item => {
    return columns.map(column => {
      if (typeof column.dataKey === 'function') {
        return column.dataKey(item)
      }
      return item[column.dataKey]
    })
  })

  const titleList = columns.map(column => column.title)

  res.unshift(titleList)

  const workSheet = utils.aoa_to_sheet(res)
  // const colWidths = columns.map((_, colIndex) => {
  //   // 获取该列所有单元格内容的最大长度
  //   const maxWidth = res.reduce((acc, row) => {
  //     const cellValue = row[colIndex] || ''
  //     const length = cellValue.toString().length
  //     return Math.max(acc, length)
  //   }, columns[colIndex].title.length) // 初始值为标题长度

  //   // 根据字符长度设置列宽（系数可以调整）
  //   return { wch: maxWidth * 2 } // 2倍字符长度作为列宽
  // })
  const colWidths = columns.map(column => ({ wch: column.title.length * 2 }))
  workSheet['!cols'] = colWidths

  const workBook = utils.book_new()
  const time = generateDateTimeString()
  const xlxsName = '快应用数据报表' + time
  utils.book_append_sheet(workBook, workSheet, '数据报表')
  writeFile(workBook, `${xlxsName}.xlsx`)
}
