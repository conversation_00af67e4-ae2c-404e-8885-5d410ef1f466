/**
 * 邮箱
 * @param {*} s
 */
export function isEmail(s) {
  return /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(
    s
  )
}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile(s) {
  return /^1[0-9]{10}$/.test(s)
}

/**
 * 电话号码
 * @param {*} s
 */
export function isPhone(s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s)
}

export function isNaN(s) {
  const n = Number(s)
  return n !== n
}

export function isNumber(obj) {
  return typeof obj === 'number' && !isNaN(obj)
}

/**
 * 是否是字符串类型的数字
 * @param s
 * @returns {boolean}
 */
export function isNumberOfStr(s) {
  // /^[0-9]+.?[0-9]*$/
  return !isNaN(String(s).replaceAll(',', ''))
}

/**
 * 是否有汉字
 * @param s
 * @returns {boolean}
 */
export function checkChinese(s) {
  return /[\u4E00-\u9FFF]+/g.test(s)
}

export function isInt(num) {
  return num % 1 === 0
}
