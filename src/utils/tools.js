// 补齐数据
import dayjs from 'dayjs'

export function makeUpData(arr, max = 31) {
  let len = arr.length
  if (len < max) {
    for (let i = 0; i < max - len; i++) {
      const item = { ...arr[i] }
      item.day = Number(
        dayjs(arr[len - 1].day.toString())
          .subtract(i + 1, 'day')
          .format('YYYYMMDD')
      )

      for (const itemKey in item) {
        if (itemKey !== 'day') {
          item[itemKey] = null
        }
      }
      arr.push(item)
    }
  }

  return arr
}

// 移除首位为0的数字
export function removeZero(arr) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] !== 0) break
    arr[i] = null
  }

  for (let i = arr.length - 1; i > 0; i--) {
    if (arr[i] !== 0) break
    arr[i] = null
  }

  return arr
}

/**
 * 毫秒转天时分秒
 * @param time  毫秒
 * @returns {{hours: number, seconds: number, minutes: number, days: number}}
 */
export function formatDuring(time) {
  const days = parseInt((time / (1000 * 60 * 60 * 24)).toString())
  const hours = parseInt(
    ((time % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)).toString()
  )
  const minutes = parseInt(((time % (1000 * 60 * 60)) / (1000 * 60)).toString())
  const seconds = Math.floor((time % (1000 * 60)) / 1000)

  return {
    days,
    hours,
    minutes,
    seconds,
  }
}

/**
 * new Mpa([[1, '上线']]) 结构转换为 [{ value: 1, label: '上线' }]
 *
 * @param mapData
 * @returns {*[]}
 */
export function mapToSelectData(mapData = new Map()) {
  const result = []

  mapData.forEach((v, k) => {
    result.push({ value: k, label: v })
  })

  return result
}

export function unique(arr) {
  if (!Array.isArray(arr)) {
    throw 'arr 必须是数组'
  }

  const result = []

  arr.forEach(it => {
    if (!result.includes(it)) {
      result.push(it)
    }
  })

  return result
}

/**
 * 防抖
 * @param fn
 * @param delay
 */
export function debounce(fn, delay) {
  let timer = null
  return function() {
    clearTimeout(timer)
    const args = arguments
    const that = this
    timer = setTimeout(function() {
      fn.apply(that, args)
    }, delay)
  }
}

/**
 * 节流
 * @param fn
 * @param delay
 */
export function throttle(fn, delay) {
  let timer = null
  let flag = true

  return function(...args) {
    if (!flag) {
      return
    }
    flag = false
    timer = setTimeout(() => {
      fn.apply(null, args)
      flag = true
    }, delay)
  }
}
