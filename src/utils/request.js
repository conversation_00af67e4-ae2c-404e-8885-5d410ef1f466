import httpRequest from '@/utils/httpRequest'
import { Message } from 'element-ui'

/**
 * this.$http 的替代品，返回数据不需要data.data的处理
 *
 * @param url
 * @param method
 * @param params
 * @param data
 * @param headers
 * @param isHandleError 是否处理错误
 * @param resAdapter  返回值适配器，把值转换一下
 * @param openDefaultData 是否开启默认数据
 * @param openDefaultParams  是否开启默认参数?
 * @param isHandleAdornData data参数是否调用 adornData
 */
export function request({
  url,
  method,
  params,
  data,
  headers = {
    'Content-Type': 'application/json; charset=utf-8',
  },
  isHandleError = true,
  resAdapter = data => data,
  openDefaultData = false,
  openDefaultParams = false,
  isHandleAdornData = true,
}) {
  const errorHandler = (msg = '服务器错误') => {
    Message.error(msg)
  }

  return new Promise((resolve, reject) => {
    const contentType = headers['Content-Type'].includes('json')
      ? 'json'
      : 'form'

    const requestParams = httpRequest.adornParams(params, openDefaultParams)
    const requestData = isHandleAdornData
      ? httpRequest.adornData(data, openDefaultData, contentType)
      : data

    httpRequest({
      url: httpRequest.adornUrl(url),
      method,
      params: requestParams,
      data: requestData,
      headers,
    })
      .then(({ data }) => {
        if (data && data.code === 0) {
          resolve(resAdapter(data))
          // resolve(data)
        } else {
          if (isHandleError) {
            errorHandler(data.msg)
          }
          reject(data.msg)
        }
      })
      .catch(e => {
        if (isHandleError) {
          errorHandler(e.message)
        }
        reject(e)
      })
  })
}

/**
 * 根据 url 生成通用 api，避免写大量的 api 接口
 * @param url
 * @param selectItemResCopyField  接口返回值的需要复制的字段，强制生成一个 __data__ 字段，selectItem这个接口不同的url返回的字段不一样
 * @param selectItemResTargetField  转换的目标字段
 */
export function genRequestAPI(
  url,
  { selectItemResCopyField = null, selectItemResTargetField = '__data__' }
) {
  if (!selectItemResCopyField) {
    console.error('必须要传selectItemResCopyField')
    throw '必须要传selectItemResCopyField'
  }

  /**
   * 获取所有
   */
  const selectAll = ({ currentPage, pageSize, ...rest }) => {
    return request({
      url: `${url}/list`,
      method: 'get',
      params: {
        page: currentPage,
        limit: pageSize,
        ...rest,
      },
    })
  }

  /**
   * 获取id
   */
  const getId = ({ accountId, ...rest }) => {
    return request({
      url: `${url}/get_id`,
      method: 'get',
      params: {
        accountId: accountId,
        ...rest,
      },
    })
  }

  /**
   * 获取某个数据
   * @param id
   */
  const selectItem = id => {
    return request({
      url: `${url}/info/${id}`,
      method: 'get',
      resAdapter: res => {
        if (selectItemResCopyField && res[selectItemResCopyField]) {
          return {
            ...res,
            [selectItemResTargetField]: res[selectItemResCopyField],
          }
        }
        return res
      },
    })
  }

  /**
   * 更新
   * @param data
   */
  const update = data => {
    return request({
      url: `${url}/update`,
      method: 'post',
      data,
    })
  }

  /**
   * 新增
   * @param data
   */
  const insert = data => {
    return request({
      url: `${url}/save`,
      method: 'post',
      data,
    })
  }

  /**
   * 删除
   * @param ids 数组
   */
  const remove = ids => {
    return request({
      url: `${url}/delete`,
      method: 'post',
      data: ids,
      openDefaultData: false,
    })
  }

  /**
   * 根据id，判断更新还是新增
   * @param id
   * @param data
   */
  const insertOrUpdate = (id, data) => {
    return id ? update(data) : insert(data)
  }

  /**
   * 批量修改
   * @param data
   */
  const batchUpdate = data => {
    return request({
      url: `${url}/batch_update`,
      method: 'post',
      data: data,
      openDefaultData: false,
    })
  }

  /**
   * 批量上线
   * @param ids 数组
   */
  const batchOnline = ids => {
    return request({
      url: `${url}/batch_online`,
      method: 'post',
      data: ids,
      openDefaultData: false,
    })
  }

  /**
   * 批量下线
   * @param ids 数组
   */
  const batchOffline = ids => {
    return request({
      url: `${url}/batch_offline`,
      method: 'post',
      data: ids,
      openDefaultData: false,
    })
  }

  return {
    insert,
    remove,
    update,
    selectAll,
    selectItem,
    insertOrUpdate,
    batchOnline,
    batchOffline,
    getId,
    batchUpdate
  }
}
