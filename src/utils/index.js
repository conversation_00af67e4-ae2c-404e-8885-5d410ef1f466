import Vue from 'vue'
import router from '@/router'
import store from '@/store'
import { isNumberOfStr } from '@/utils/validate'

/**
 * 获取uuid
 */
export function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    return (c === 'x' ? (Math.random() * 16) | 0 : 'r&0x3' | '0x8').toString(16)
  })
}

/**
 * 是否有权限
 * @param {*} key
 */
export function isAuth(key) {
  return (
    JSON.parse(sessionStorage.getItem('permissions') || '[]').indexOf(key) !==
      -1 || false
  )
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = 'id', pid = 'parentId') {
  var res = []
  var temp = {}
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i]
  }
  for (var k = 0; k < data.length; k++) {
    if (temp[data[k][pid]] && data[k][id] !== data[k][pid]) {
      if (!temp[data[k][pid]]['children']) {
        temp[data[k][pid]]['children'] = []
      }
      if (!temp[data[k][pid]]['_level']) {
        temp[data[k][pid]]['_level'] = 1
      }
      data[k]['_level'] = temp[data[k][pid]]._level + 1
      temp[data[k][pid]]['children'].push(data[k])
    } else {
      res.push(data[k])
    }
  }
  return res
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
  Vue.cookie.delete('token')
  store.commit('resetStore')
  router.options.isAddDynamicMenuRoutes = false
}

// 下划线转换驼峰
export function toHump(name) {
  return name.replace(/_(\w)/g, function(all, letter) {
    return letter.toUpperCase()
  })
}

// 驼峰转换下划线
export function toLine(name) {
  return name.replace(/([A-Z])/g, '_$1').toLowerCase()
}

/**
 * 下载表格成excel
 */
export function downloadTableToExcel(
  isRemoveLast = true,
  config = {
    filename: router.currentRoute.meta.title
      ? router.currentRoute.meta.title
      : 'excel-table',
    autoWidth: true,
    bookType: 'csv',
  },
  isTranToNumber = false
) {
  import('@/vendor/Export2Excel').then(excel => {
    let header = []
    const cells = document.querySelectorAll(
      '.el-table__header-wrapper .el-table__header thead .cell'
    )
    cells.forEach((el, index) => {
      if (isRemoveLast) {
        if (index < cells.length - 1) {
          header.push(el.textContent)
        }
      } else {
        header.push(el.textContent)
      }
    })
    let data = []
    const trRows = document.querySelectorAll(
      '.el-table__body-wrapper .el-table__body .el-table__row'
    )
    trRows.forEach((row, index) => {
      data.push([])
      const cells = row.querySelectorAll('td')
      cells.forEach((cell, cI) => {
        const textContent =
          isTranToNumber && isNumberOfStr(cell.textContent)
            ? Number(String(cell.textContent).replaceAll(',', ''))
            : cell.textContent

        if (isRemoveLast) {
          if (cI < cells.length - 1) {
            data[index].push(textContent)
          }
        } else {
          data[index].push(textContent)
        }
      })
    })

    excel.export_json_to_excel({
      header,
      data,
      filename: config.filename,
      autoWidth: config.autoWidth,
      bookType: config.bookType,
    })
  })
}

/**
 * 判断 json 是否为空
 * @param json
 * @returns {boolean}
 */
export function jsonIsEmpty(json) {
  if (!json) return true

  let len = 0
  for (const key in json) {
    if (json[key] !== undefined) {
      len++
      break
    }
  }

  return !len
}

/**
 * 生成随机颜色
 */
export function randomColor() {
  const r = Math.floor(Math.random() * 255)
  const g = Math.floor(Math.random() * 255)
  const b = Math.floor(Math.random() * 255)
  return 'rgba(' + r + ',' + g + ',' + b + ',1)'
}
