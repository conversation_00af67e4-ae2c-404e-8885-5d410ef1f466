import domtoimage from 'dom-to-image'
export function generateDateTimeString() {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`
}

export const DomToImageJpeg = (node, option) => {
  const options = {
    bgcolor: '#fff',
    style: {
      'border-collapse': 'collapse',
      width: '100%',
    },
    ...option,
  }
  return domtoimage
    .toJpeg(node, options)
    .then(dataUrl => {
      return dataUrl
    })
    .catch(error => {
      console.error('导出失败:', error)
    })
}
export const loadImage = src => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    img.src = src
    img.onload = () => resolve(img)
    img.onerror = err => reject(err)
  })
}

export const downloadImage = dataURL => {
  const a = document.createElement('a')
  a.href = dataURL
  a.download = `快应用实时报表-${generateDateTimeString()}`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

export const DownloadImg = (canvas, img1, img2) => {
  const ctx = canvas.getContext('2d')
  try {
    const image1 = loadImage(img1)
    const image2 = loadImage(img2)
    const width = Math.max(image1.width, image2.width)
    const height = image1.height + image2.height

    canvas.width = width
    canvas.height = height

    ctx.drawImage(image1, 0, 0)
    ctx.drawImage(image2, 0, image1.height)

    const dataURL = canvas.toDataURL('image/jpeg')
    downloadImage(dataURL)
  } catch (error) {
    console.error('图片加载失败:', error)
  }
}
