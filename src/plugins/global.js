// 挂载全局
import Vue from 'vue'
import httpRequest from '@/utils/httpRequest'
import { downloadTableToExcel, isAuth } from '@/utils'
import dayjs from 'dayjs'
import PageTable from '@/components/page-table'
import ColorTag from '@/components/color-tag'
import MapRadio from '@/components/map-redio'
import RadioStatus from '@/components/radio-status'
import TagStatus from '@/components/tag-status'
import MapSelect from '@/components/map-select'
import ElSelectExtend from '@/components/el-select-extend'
import ArrSelect from '@/components/arr-select'
import AppVersionSelect from '@/components/app-version-select'
import AppSelectComponent from '@/components/app-select-component'
import AdPlatformSelect from '@/components/ad-platform-select'
// import ConfigJsonEditor from '@/components/config-json-editor'

// 全局组件
Vue.component('PageTable', PageTable)
Vue.component('ColorTag', ColorTag)
Vue.component('MapRadio', MapRadio)
Vue.component('RadioStatus', RadioStatus)
Vue.component('TagStatus', TagStatus)
Vue.component('MapSelect', MapSelect)
Vue.component('ElSelectExtend', ElSelectExtend)
Vue.component('ArrSelect', ArrSelect)
Vue.component('AppVersionSelect', AppVersionSelect)
Vue.component('AppSelectComponent', AppSelectComponent)
Vue.component('ad-platform-select', AdPlatformSelect)
// Vue.component('ConfigJsonEditor', ConfigJsonEditor)

// 全局方法
Vue.prototype.$http = httpRequest // ajax请求方法
Vue.prototype.isAuth = isAuth // 权限方法
Vue.prototype.$dayjs = dayjs
Vue.prototype.$downloadTableToExcel = downloadTableToExcel
Vue.prototype.$ENV =
  window.SITE_CONFIG && window.SITE_CONFIG.env
    ? window.SITE_CONFIG.env
    : 'local'
Vue.prototype.$ENV_TEXT =
  window.SITE_CONFIG && window.SITE_CONFIG.env_text
    ? window.SITE_CONFIG.env_text
    : '本地'
