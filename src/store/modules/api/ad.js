import httpRequest from '@/utils/httpRequest'

export default {
  namespaced: true,
  actions: {
    //------------------------ | 广告位-start | ------------------------
    /**
     * 获取广告位列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAdPositionList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adposition/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 删除广告位
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    deleteAdPosition(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adposition/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 添加或更新广告位
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    addOrUpdateAdPosition(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl(
          `/ad/adposition/${!data.id ? 'save' : 'update'}`
        ),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 获取广告位信息
     * @param _
     * @param id
     * @returns {AxiosPromise}
     */
    getAdPositionInfo(_, id) {
      return httpRequest({
        url: httpRequest.adornUrl(`/ad/adposition/info/${id}`),
        method: 'get',
        params: httpRequest.adornParams(),
      })
    },

    //------------------------ | 广告位-end | ------------------------

    //------------------------ | 代码位-start | ------------------------
    /**
     * 获取代码位列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAdSlotList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 删除
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    deleteAdSlot(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 添加或更新
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    addOrUpdateAdSlot(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl(`/ad/adslot/${!data.id ? 'save' : 'update'}`),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 获取信息
     * @param _
     * @param id
     * @returns {AxiosPromise}
     */
    getAdSlotInfo(_, id) {
      return httpRequest({
        url: httpRequest.adornUrl(`/ad/adslot/info/${id}`),
        method: 'get',
        params: httpRequest.adornParams(),
      })
    },
    /**
     * 查看某广告位已绑定的广告代码位列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAdPositionBindList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/list/bind_by_ap'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 查看某策略已绑定的广告代码位列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getStrategyBindList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/list/by_strategy'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 解绑广告位的广告代码
     * @param _
     * @param data
     * @returns {AxiosPromise}
     * @private
     */
    unBindAdPositionAp(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/unbind_ap'),
        method: 'post',
        data,
      })
    },
    /**
     * 广告位绑定代码位
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    bindAdPositionAp(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/bind_ap'),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 查看广告位可绑定广告代码位列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     * @private
     */
    getAdPositionAvailableBindList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adslot/list/available_bind'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    //------------------------ | 代码位-end | ------------------------

    //------------------------ | 广告策略管理-start | ------------------------
    // 有空再加
    //------------------------ | 广告策略管理-end | ------------------------

    //------------------------ | 策略下代码位管理-start | ------------------------
    /**
     * 解绑策略的广告代码
     * @param _
     * @param data [id, ....] （"策略下代码位管理模块"的自增ID）
     * @returns {AxiosPromise}
     */
    unBindStrategyAp(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/ad/adstrategyslot/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 策略绑定代码位
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    bindStrategyAp(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl(`/ad/adstrategyslot/save`),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    //------------------------ | 策略下代码位管理-end | ------------------------
  },
}
