/**
 * 市场回传相关
 */

import httpRequest from '@/utils/httpRequest'

export default {
  namespaced: true,
  actions: {
    /**
     * 获取回传策略
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getActivateStrategyList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/activate/activatestrategy/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
  },
}
