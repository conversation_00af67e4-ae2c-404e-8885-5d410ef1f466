import httpRequest from '@/utils/httpRequest'

export default {
  namespaced: true,
  actions: {
    /**
     * 实时留存查询接口
     * @param _
     * @param payload
     */
    getBriefingInTimeKeepList(
      _,
      payload = {
        params: {},
        config: {},
      }
    ) {
      return httpRequest({
        url: httpRequest.adornUrl(
          '/stat/statbusinesssummarydaily/briefing_in_time_keep_list'
        ),
        method: 'get',
        params: httpRequest.adornParams(payload.params),
        ...payload.config,
      })
    },
    /**
     * 实时新增
     * @param _
     * @param payload
     */
    getBriefingInTimeNewlyAddList(
      _,
      payload = {
        params: {},
        config: {},
      }
    ) {
      return httpRequest({
        url: httpRequest.adornUrl(
          '/stat/statbusinesssummarydaily/briefing_in_time_newly_add_list'
        ),
        method: 'get',
        params: httpRequest.adornParams(payload.params),
        ...payload.config,
      })
    },
    /**
     * ARPU
     * @param _
     * @param payload
     */
    getARPUList(
      _,
      payload = {
        params: {},
        config: {},
      }
    ) {
      return httpRequest({
        url: httpRequest.adornUrl('/stat/statrealtimeindicator/arpu'),
        method: 'get',
        params: httpRequest.adornParams(payload.params),
        ...payload.config,
      })
    },
    /**
     * ECPM数据
     * @param _
     * @param payload
     */
    getECPMList(
      _,
      payload = {
        params: {},
        config: {},
      }
    ) {
      return httpRequest({
        url: httpRequest.adornUrl('/stat/statrealtimeindicator/ecpm'),
        method: 'get',
        params: httpRequest.adornParams(payload.params),
        ...payload.config,
      })
    },
    /**
     * AIPU数据
     * @param _
     * @param payload
     */
    getAIPUList(
      _,
      payload = {
        params: {},
        config: {},
      }
    ) {
      return httpRequest({
        url: httpRequest.adornUrl('/stat/statrealtimeindicator/aipu'),
        method: 'get',
        params: httpRequest.adornParams(payload.params),
        ...payload.config,
      })
    },
    /**
     * imp_rate数据
     * @param _
     * @param payload
     */
    getImpRateList(
      _,
      payload = {
        params: {},
        config: {},
      }
    ) {
      return httpRequest({
        url: httpRequest.adornUrl('/stat/statrealtimeindicator/imp_rate'),
        method: 'get',
        params: httpRequest.adornParams(payload.params),
        ...payload.config,
      })
    },
  },
}
