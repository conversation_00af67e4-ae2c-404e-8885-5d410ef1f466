import httpRequest from '@/utils/httpRequest'

export default {
  namespaced: true,
  actions: {
    //------------------------ | app版本-start | ------------------------
    /**
     * 获取APP版本列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getVersionList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/appversion/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 删除版本号
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    deleteVersion(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/appversion/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 更新或添加接口
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    addOrUpdateVersion(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl(
          `/apps/appversion/${!data.id ? 'save' : 'update'}`
        ),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 获取版本信息
     * @param _
     * @param id
     * @returns {AxiosPromise}
     */
    getVersionInfo(_, id) {
      return httpRequest({
        url: httpRequest.adornUrl(`/apps/appversion/info/${id}`),
        method: 'get',
        params: httpRequest.adornParams(),
      })
    },
    //------------------------ | app版本-end | ------------------------

    //------------------------ | app管理-start | ------------------------
    /**
     * 获取app列表-数据仓库专用
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAppListWarehouse(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/app/list/warehouse'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 获取app列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAppList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/app/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 获取投放账户列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAccountIdList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/stat/ctrcallbackrules/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 根据角色获取APP列表,暂无分页
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getAppListWithRole(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/app/list/all'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 删除app
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    deleteApp(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/app/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 添加或更新app
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    addOrUpdateApp(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl(`/apps/app/${!data.id ? 'save' : 'update'}`),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 获取app信息
     * @param _
     * @param id
     * @returns {AxiosPromise}
     */
    getAppInfo(_, id) {
      return httpRequest({
        url: httpRequest.adornUrl(`/apps/app/info/${id}`),
        method: 'get',
        params: httpRequest.adornParams(),
      })
    },
    //------------------------ | app管理-end | ------------------------

    //------------------------ | app渠道-start | ------------------------
    /**
     * 获取渠道列表
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getChannelList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/appchannel/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 删除渠道
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    deleteChannel(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/apps/appchannel/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 更新或删除渠道
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    addOrUpdateChannel(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl(
          `/apps/appchannel/${!data.id ? 'save' : 'update'}`
        ),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 获取渠道信息
     * @param _
     * @param id
     * @returns {AxiosPromise}
     */
    getChannelInfo(_, id) {
      return httpRequest({
        url: httpRequest.adornUrl(`/apps/appchannel/info/${id}`),
        method: 'get',
        params: httpRequest.adornParams(),
      })
    },
    //------------------------ | app渠道-end | ------------------------
  },
}
