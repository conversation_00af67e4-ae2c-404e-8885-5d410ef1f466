import httpRequest from '@/utils/httpRequest'

export default {
  namespaced: true,
  actions: {
    /**
     * 获取列表数据
     * @param _
     * @param params
     * @returns {AxiosPromise}
     */
    getConfigList(_, params) {
      return httpRequest({
        url: httpRequest.adornUrl('/conf/config/list'),
        method: 'get',
        params: httpRequest.adornParams(params),
      })
    },
    /**
     * 删除配置
     * @param _
     * @param data
     * @returns {*}
     */
    deleteConfig(_, data = {}) {
      return httpRequest({
        url: httpRequest.adornUrl('/conf/config/delete'),
        method: 'post',
        data: httpRequest.adornData(data, false),
      })
    },
    /**
     * 更新或添加
     * @param _
     * @param data
     * @returns {AxiosPromise}
     */
    addOrUpdateConf(_, data) {
      return httpRequest({
        url: httpRequest.adornUrl(
          `/conf/config/${!data.id ? 'save' : 'update'}`
        ),
        method: 'post',
        data: httpRequest.adornData(data),
      })
    },
    /**
     * 获取单个信息
     * @param _
     * @param id
     * @returns {AxiosPromise}
     */
    getConfInfo(_, id) {
      return httpRequest({
        url: httpRequest.adornUrl(`/conf/config/info/${id}`),
        method: 'get',
        params: httpRequest.adornParams(),
      })
    },
  },
}
