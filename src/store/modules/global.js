export default {
  namespaced: true,
  state: {
    appList: null,
    outUserNameList: ['waibu1', 'waibu2'], //外部账号
  },
  mutations: {
    updateAppList(state, list) {
      state.appList = list
    },
  },
  actions: {
    /**
     * 获取并设置appList
     * @param dispatch
     * @param commit
     * @param showAll
     */
    getAppListWithRole({ dispatch, commit }, { showAll } = { showAll: true }) {
      let dataList = []

      dispatch('api/app/getAppListWithRole', null, { root: true })
        .then(({ data }) => {
          if (data && data.code === 0) {
            dataList = data.apps
            if (showAll) {
              dataList.unshift({
                id: null,
                name: '全部应用',
                code: 0,
              })
            }
          }
        })
        .finally(() => {
          commit('updateAppList', dataList)
        })
    },
  },
}
