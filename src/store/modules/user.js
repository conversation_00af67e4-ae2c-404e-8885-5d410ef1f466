export default {
  namespaced: true,
  state: {
    id: 0,
    name: '',
    groupIdList: [],
    appIdList: [],
  },
  mutations: {
    updateId(state, id) {
      state.id = id
    },
    updateName(state, name) {
      state.name = name
    },
    updateGroupIdList(state, list) {
      state.groupIdList = list.sort((a, b) => a - b)
    },
    updateAppIdList(state, list) {
      state.appIdList = list
    },
  },
}
