import store from '@/store'
import dayjs from '@/dayjs'

/**
 * 根据key和value找到数组中的某一项
 * @param value 要找的value
 * @param list  数组
 * @param key   key值
 * @param returnAttr  要返回的的那个字段
 * @returns {*|string}
 */
export const getListLabel = (value, list, key, returnAttr) => {
  const res = list.find(it => it[key] === value)

  if (returnAttr) {
    return res ? res[returnAttr] : ''
  }

  return res
}

export const getAppName = (value, key = 'code') => {
  return getListLabel(value, store.state.global.appList, key, 'name')
}

export default {
  getListLabel,
  getAppName,
  getAppId(value, key = 'code') {
    return getListLabel(value, store.state.global.appList, key, 'id')
  },
  getVersionName(value, versionList = [], key = 'versionCode') {
    return getListLabel(value, versionList, key, 'versionName')
  },
  getVersionNameList(value, versionList = [], key = 'versionCode') {
    return value.map(it => getListLabel(it, versionList, key, 'versionName'))
  },
  formatDate(value, format = 'YYYY-MM-DD') {
    return dayjs(value.toString()).format(format)
  },
}
