import Vue from 'vue'
import App from '@/App'
import router from '@/router' // api: https://github.com/vuejs/vue-router
import store from '@/store' // api: https://github.com/vuejs/vuex
import { cloneDeep } from 'lodash'
import '@/plugins'
import '@/plugins/global'
import '@/icons' // api: http://www.iconfont.cn/
import '@/element-ui-theme'
import '@/assets/scss/index.scss'
import '@/assets/json-editor/jsoneditor.css'

Vue.config.productionTip = false

// 非生产环境, 适配mockjs模拟数据                 // api: https://github.com/nuysoft/Mock
if (process.env.NODE_ENV !== 'production') {
  require('@/mock')
}

// 保存整站vuex本地储存初始状态
if (window.SITE_CONFIG && window.SITE_CONFIG['storeState']) {
  window.SITE_CONFIG['storeState'] = cloneDeep(store.state)
}

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount('#app')
