<template>
  <div class="site-sidebar" :class="'site-sidebar--' + sidebarLayoutSkin">
    <div class="logo">
      <h2>守耘科技</h2>
      <div v-if="$ENV_TEXT !== '生产'" class="env-text">
        <span>{{ $ENV_TEXT }}</span>
      </div>
    </div>
    <!-- <div class="site-sidebar__inner">
      <el-menu
        :default-active="menuActiveName || 'home'"
        :collapse="sidebarFold"
        :collapseTransition="false"
        class="site-sidebar__menu"
        mode="horizontal"
      >
        <template v-for="menu in menuList">
          <sub-menu
            v-if="menu.name !== '隐藏'"
            :key="menu.menuId"
            :menu="menu"
            :dynamicMenuRoutes="dynamicMenuRoutes"
          />
        </template>
      </el-menu>`
    </div> -->
    <MainNavbar />
  </div>
</template>

<script>
// import SubMenu from './main-sidebar-sub-menu'
import MainNavbar from './main-navbar'
import { isURL } from '@/utils/validate'

export default {
  data() {
    return {
      dynamicMenuRoutes: [],
    }
  },
  components: {
    // SubMenu,
    MainNavbar,
  },
  computed: {
    sidebarLayoutSkin: {
      get() {
        return this.$store.state.common.sidebarLayoutSkin
      },
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold
      },
    },
    menuList: {
      get() {
        return this.$store.state.common.menuList
      },
      set(val) {
        this.$store.commit('common/updateMenuList', val)
      },
    },
    menuActiveName: {
      get() {
        return this.$store.state.common.menuActiveName
      },
      set(val) {
        this.$store.commit('common/updateMenuActiveName', val)
      },
    },
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs
      },
      set(val) {
        this.$store.commit('common/updateMainTabs', val)
      },
    },
    mainTabsActiveName: {
      get() {
        return this.$store.state.common.mainTabsActiveName
      },
      set(val) {
        this.$store.commit('common/updateMainTabsActiveName', val)
      },
    },
  },
  watch: {
    $route: 'routeHandle',
  },
  created() {
    this.menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]')
    this.dynamicMenuRoutes = JSON.parse(
      sessionStorage.getItem('dynamicMenuRoutes') || '[]'
    )
    this.routeHandle(this.$route)
  },
  methods: {
    // 路由操作
    routeHandle(route) {
      if (route.meta.isTab) {
        // tab选中, 不存在先添加
        let tab = this.mainTabs.filter(item => item.name === route.name)[0]
        if (!tab) {
          if (route.meta.isDynamic) {
            route = this.dynamicMenuRoutes.filter(
              item => item.name === route.name
            )[0]
            if (!route) {
              return console.error('未能找到可用标签页!')
            }
          }
          tab = {
            menuId: route.meta.menuId || route.name,
            name: route.name,
            title: route.meta.title,
            type: isURL(route.meta.iframeUrl) ? 'iframe' : 'module',
            iframeUrl: route.meta.iframeUrl || '',
            params: route.params,
            query: route.query,
          }
          this.mainTabs = this.mainTabs.concat(tab)
        }
        this.menuActiveName = tab.menuId + ''
        this.mainTabsActiveName = tab.name
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/_variables';

.site-sidebar {
  align-items: center;
  justify-content: space-between;
  background: $navbar--background;
  height: $navbar--height;
  padding: 0;
}

.logo {
  display: flex;
  color: #fff;
  width: 160px;
  padding-left: 20px;
  align-items: baseline;

  .env-text {
    margin-left: 5px;
    font-size: 14px;
    color: white;
  }
}
</style>
