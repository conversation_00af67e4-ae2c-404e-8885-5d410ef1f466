<template>
  <div
    class="site-wrapper page-enter"
    :class="{ 'site-sidebar--fold': sidebarFold }"
    v-loading.fullscreen.lock="loading"
    element-loading-text="拼命加载中"
  >
    <template v-if="!loading">
      <el-container style="height: 100vh; flex-direction: column">
        <el-header style="height: 60px; flex-shrink: 0" class="sidebar-enter">
          <MainSidebar />
        </el-header>
        <el-container style="flex: 1; min-height: 0">
          <el-aside
            :width="isCollapse ? '64px' : '240px'"
            class="sidebar-container"
            style="height: 100%; overflow: auto"
          >
            <main-menu
              @collapse-change="handleCollapseChange"
              :is-collapse="isCollapse"
            />
          </el-aside>
          <el-main style="padding: 0; overflow: auto">
            <div
              class="site-content__wrapper card-enter"
              :style="{
                'min-height': documentClientHeight + 'px',
              }"
            >
              <main-content v-if="!$store.state.common.contentIsNeedRefresh" />
            </div>
          </el-main>
        </el-container>
      </el-container>
    </template>
  </div>
</template>
<script>
import MainSidebar from './main-sidebar'
import MainContent from './main-content'
import MainMenu from './main-menu'

export default {
  provide() {
    return {
      // 刷新
      refresh() {
        this.$store.commit('common/updateContentIsNeedRefresh', true)
        this.$nextTick(() => {
          this.$store.commit('common/updateContentIsNeedRefresh', false)
        })
      },
    }
  },
  data() {
    return {
      loading: true,
      isCollapse: true,
    }
  },
  components: {
    MainSidebar,
    MainContent,
    MainMenu,
  },
  computed: {
    sidebarLayoutSkin: {
      get() {
        return this.$store.state.common.sidebarLayoutSkin
      },
    },
    documentClientHeight: {
      get() {
        return this.$store.state.common.documentClientHeight
      },
      set(val) {
        this.$store.commit('common/updateDocumentClientHeight', val)
      },
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold
      },
    },
    userId: {
      get() {
        return this.$store.state.user.id
      },
      set(val) {
        this.$store.commit('user/updateId', val)
      },
    },
    userName: {
      get() {
        return this.$store.state.user.name
      },
      set(val) {
        this.$store.commit('user/updateName', val)
      },
    },
    groupIdList: {
      get() {
        return this.$store.state.user.groupIdList
      },
      set(val) {
        this.$store.commit('user/updateGroupIdList', val)
      },
    },
  },
  created() {
    this.getUserInfo()
  },
  mounted() {
    this.resetDocumentClientHeight()
    // this.handleResize()
    //   window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleCollapseChange(newVal) {
      this.isCollapse = newVal
    },
    // 重置窗口可视高度
    resetDocumentClientHeight() {
      this.documentClientHeight = document.documentElement['clientHeight']
      window.onresize = () => {
        this.documentClientHeight = document.documentElement['clientHeight']
      }
    },
    // 获取当前管理员信息
    getUserInfo() {
      this.$http({
        url: this.$http.adornUrl('/sys/user/info'),
        method: 'get',
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        console.log('管理员信息...', data)
        if (data && data.code === 0) {
          this.loading = false
          this.userId = data.user.userId
          this.userName = data.user.username
          this.groupIdList = data.user.groupIdList
          this.$store.commit('user/updateAppIdList', data.user.appIdList)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '@/assets/scss/_variables';

html,
body,
#app {
  height: 100%;
  margin: 0;
}
.site-sidebar {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  color: #fff;
  width: 160px;
  padding-left: 20px;
  align-items: baseline;

  .env-text {
    margin-left: 5px;
    font-size: 14px;
    color: #f56c6c;
  }
}
.site-wrapper {
  height: 100%;
}

.sidebar-container {
  background: $sidebar--background;
  transition: width 0.5s ease;
  overflow-y: auto;
  height: calc(100vh - 60px);
}
</style>
