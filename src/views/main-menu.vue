<template>
  <div class="site-sidebar__inner" style="height: 100%; overflow: auto">
    <el-menu
      :collapse="isCollapse"
      :default-active="menuActiveName || 'home'"
      background-color="#00162a"
      :collapseTransition="false"
      text-color="#fff"
      class="sidebar-container"
      style="height: 100%"
    >
      <div
        class="toggle-button"
        :style="{ width: isCollapse ? '64px' : '240px' }"
        @click="toggleCollapse"
      >
        <i class="el-icon-menu" />
      </div>
      <template v-for="menu in menuList">
        <sub-menu
          v-if="menu.name !== '隐藏'"
          :key="menu.menuId"
          :menu="menu"
          :dynamicMenuRoutes="dynamicMenuRoutes"
        />
      </template>
    </el-menu>
  </div>
</template>

<script>
import SubMenu from './main-sidebar-sub-menu'
// import MainNavbar from './main-navbar'
import { isURL } from '@/utils/validate'

export default {
  props: ['isCollapse'],
  data() {
    return {
      dynamicMenuRoutes: [],
    }
  },
  components: {
    SubMenu,
    // MainNavbar,
  },
  computed: {
    sidebarLayoutSkin: {
      get() {
        return this.$store.state.common.sidebarLayoutSkin
      },
    },
    // sidebarFold: {
    //   get() {
    //     return this.$store.state.common.sidebarFold
    //   },
    // },
    menuList: {
      get() {
        return this.$store.state.common.menuList
      },
      set(val) {
        this.$store.commit('common/updateMenuList', val)
      },
    },
    menuActiveName: {
      get() {
        return this.$store.state.common.menuActiveName
      },
      set(val) {
        this.$store.commit('common/updateMenuActiveName', val)
      },
    },
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs
      },
      set(val) {
        this.$store.commit('common/updateMainTabs', val)
      },
    },
    mainTabsActiveName: {
      get() {
        return this.$store.state.common.mainTabsActiveName
      },
      set(val) {
        this.$store.commit('common/updateMainTabsActiveName', val)
      },
    },
  },
  watch: {
    $route: 'routeHandle',
    isCollapse(newVal) {
      this.$emit('collapse-change', newVal)
    },
  },
  created() {
    this.menuList = JSON.parse(sessionStorage.getItem('menuList') || '[]')
    this.dynamicMenuRoutes = JSON.parse(
      sessionStorage.getItem('dynamicMenuRoutes') || '[]'
    )
    this.routeHandle(this.$route)
  },

  mounted() {
    // this.handleResize()
    // window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    toggleCollapse() {
      this.$emit('collapse-change', !this.isCollapse)
    },
    // handleResize() {
    //   if (window.innerWidth <= 990) {
    //     this.isCollapse = true
    //   } else {
    //     this.isCollapse = false
    //   }
    // },
    // 路由操作
    routeHandle(route) {
      if (route.meta.isTab) {
        // tab选中, 不存在先添加
        let tab = this.mainTabs.filter(item => item.name === route.name)[0]
        if (!tab) {
          if (route.meta.isDynamic) {
            route = this.dynamicMenuRoutes.filter(
              item => item.name === route.name
            )[0]
            if (!route) {
              return console.error('未能找到可用标签页!')
            }
          }
          tab = {
            menuId: route.meta.menuId || route.name,
            name: route.name,
            title: route.meta.title,
            type: isURL(route.meta.iframeUrl) ? 'iframe' : 'module',
            iframeUrl: route.meta.iframeUrl || '',
            params: route.params,
            query: route.query,
          }
          this.mainTabs = this.mainTabs.concat(tab)
        }
        this.menuActiveName = tab.menuId + ''
        this.mainTabsActiveName = tab.name
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.site-sidebar {
  display: flex;
  align-items: center;
}

.site-sidebar__inner {
  // flex: 1;
  height: 100%;
}
.toggle-button {
  font-size: 15px;
  line-height: 24px;
  //color: #fff;
  // 设置内容|||之间的距离
  letter-spacing: 0.2em;
  cursor: pointer;
  // padding-left: 19px;
  text-align: center;
  transition: all 0.5s ease;
}
.el-menu {
  border-right: none;
  overflow-y: auto;
  max-height: 100%;
  transition: all 0.5s ease;
}
</style>
