<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="500px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-form-item label="应用" prop="appCode">
        <app-select-component
          v-model="dataForm.appCode"
          placeholder="应用id"
          :is-show-all="false"
          width="100%"
        />
      </el-form-item>
      <el-form-item label="提现所需积分" prop="integral">
        <el-input
          type="number"
          v-model.number="dataForm.integral"
          placeholder="提现所需积分"
          clearable
        />
      </el-form-item>
      <el-form-item label="提现金额" prop="amount">
        <el-input
          type="number"
          v-model.number="dataForm.amount"
          placeholder="提现金额"
          clearable
        />
      </el-form-item>
      <el-form-item label="排序值" prop="sortValue">
        <el-input
          type="number"
          v-model.number="dataForm.sortValue"
          placeholder="排序值"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否需要审核" prop="needAudit">
        <map-select
          v-model="dataForm.needAudit"
          :list="needAuditList"
          style="width: 100%"
          clearable
        />
      </el-form-item>
      <el-form-item label="提现说明" prop="description">
        <el-input
          type="textarea"
          v-model="dataForm.description"
          placeholder="提现说明"
          clearable
        />
      </el-form-item>
      <el-form-item label="额外信息" prop="extra">
        <el-input
          type="textarea"
          v-model="dataForm.extra"
          placeholder="额外信息"
          clearable
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <map-select
          v-model="dataForm.status"
          :list="integralWithdrawGrantRuleStatus"
          style="width: 100%"
          clearable
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="loading">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import AppSelectComponent from '@/components/app-select-component'
import { integralWithdrawGrantRuleStatus, needAuditList } from '@/map/lzhbq'
import MapSelect from '@/components/map-select'
export default {
  components: { MapSelect, AppSelectComponent },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        integral: '',
        appCode: '',
        amount: '',
        sortValue: '',
        needAudit: '',
        description: '',
        extra: '',
        status: '',
      },
      dataRule: {
        integral: [
          { required: true, message: '提现所需积分不能为空', trigger: 'blur' },
        ],
        appCode: [{ required: true, message: '应用不能为空', trigger: 'blur' }],
        amount: [
          { required: true, message: '提现金额不能为空', trigger: 'blur' },
        ],
        sortValue: [
          { required: true, message: '排序值不能为空', trigger: 'blur' },
        ],
        needAudit: [
          {
            required: true,
            message: '是否需要审核',
            trigger: 'blur',
          },
        ],
        description: [
          { required: true, message: '提现说明不能为空', trigger: 'blur' },
        ],
        // extra: [
        //   { required: true, message: '额外信息不能为空', trigger: 'blur' },
        // ],
        status: [
          {
            required: true,
            message: '状态',
            trigger: 'blur',
          },
        ],
      },
      needAuditList,
      integralWithdrawGrantRuleStatus,
      loading: false,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2integralwithdrawalconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.integral =
                data.hong2IntegralWithdrawalConfig.integral
              this.dataForm.appCode = data.hong2IntegralWithdrawalConfig.appCode
              this.dataForm.amount = data.hong2IntegralWithdrawalConfig.amount
              this.dataForm.sortValue =
                data.hong2IntegralWithdrawalConfig.sortValue
              this.dataForm.needAudit =
                data.hong2IntegralWithdrawalConfig.needAudit
              this.dataForm.description =
                data.hong2IntegralWithdrawalConfig.description
              this.dataForm.extra = data.hong2IntegralWithdrawalConfig.extra
              this.dataForm.status = data.hong2IntegralWithdrawalConfig.status
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.loading = true
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2integralwithdrawalconfig/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              integral: this.dataForm.integral,
              appCode: this.dataForm.appCode,
              amount: this.dataForm.amount,
              sortValue: this.dataForm.sortValue,
              needAudit: this.dataForm.needAudit,
              description: this.dataForm.description,
              extra: this.dataForm.extra,
              status: this.dataForm.status,
            }),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                })
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
  },
}
</script>
