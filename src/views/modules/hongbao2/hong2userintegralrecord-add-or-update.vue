<template>
  <el-dialog
    :title="!dataForm.coinSerialNo ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="500px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
      label-position="top"
    >
      <el-form-item label="应用" prop="appCode">
        <app-select-component
          v-model="dataForm.appCode"
          placeholder="应用"
          :is-show-all="false"
          width="100%"
        />
      </el-form-item>
      <el-form-item label="奖励关联的积分流水编号" prop="relatedCoinSerialNo">
        <el-input
          v-model="dataForm.relatedCoinSerialNo"
          placeholder="奖励关联的积分流水编号"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户表id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户表id"></el-input>
      </el-form-item>
      <el-form-item label="获得金积分，负数为扣除积分" prop="coins">
        <el-input
          v-model="dataForm.coins"
          placeholder="获得金积分，负数为扣除积分"
        ></el-input>
      </el-form-item>
      <el-form-item label="积分操作类型" prop="operatorType">
        <map-select
          v-model="dataForm.operatorType"
          :list="operatorTypeList"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="广告返回的ecpm" prop="ecpm">
        <el-input
          v-model="dataForm.ecpm"
          placeholder="广告返回的ecpm"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { operatorTypeList } from '@/map/lzhbq'
import MapSelect from '@/components/map-select'
import AppSelectComponent from '@/components/app-select-component'

export default {
  components: { AppSelectComponent, MapSelect },
  data() {
    return {
      visible: false,
      operatorTypeList,

      dataForm: {
        coinSerialNo: 0,
        relatedCoinSerialNo: '',
        userId: '',
        appCode: '',
        coins: '',
        operatorType: '',
        ecpm: '',
      },
      dataRule: {
        relatedCoinSerialNo: [
          {
            required: true,
            message: '奖励关联的积分流水编号不能为空',
            trigger: 'blur',
          },
        ],
        userId: [
          { required: true, message: '用户表id不能为空', trigger: 'blur' },
        ],
        appCode: [
          { required: true, message: '应用 code不能为空', trigger: 'blur' },
        ],
        coins: [
          {
            required: true,
            message: '获得金积分，负数为扣除积分不能为空',
            trigger: 'blur',
          },
        ],
        operatorType: [
          {
            required: true,
            message:
              '积分操作类型：1：首次奖励，2：第二次奖励，3：普通奖励不能为空',
            trigger: 'blur',
          },
        ],
        ecpm: [
          {
            required: true,
            message: '广告返回的ecpm不能为空',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.coinSerialNo = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.coinSerialNo) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2userintegralrecord/info/${this.dataForm.coinSerialNo}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.relatedCoinSerialNo =
                data.hong2UserIntegralRecord.relatedCoinSerialNo
              this.dataForm.userId = data.hong2UserIntegralRecord.userId
              this.dataForm.appCode = data.hong2UserIntegralRecord.appCode
              this.dataForm.coins = data.hong2UserIntegralRecord.coins
              this.dataForm.operatorType =
                data.hong2UserIntegralRecord.operatorType
              this.dataForm.ecpm = data.hong2UserIntegralRecord.ecpm
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2userintegralrecord/${
                !this.dataForm.coinSerialNo ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              coinSerialNo: this.dataForm.coinSerialNo || undefined,
              relatedCoinSerialNo: this.dataForm.relatedCoinSerialNo,
              userId: this.dataForm.userId,
              appCode: this.dataForm.appCode,
              coins: this.dataForm.coins,
              operatorType: this.dataForm.operatorType,
              ecpm: this.dataForm.ecpm,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
