<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="400px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="110px"
    >
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model="dataForm.appId"
          :is-show-all="false"
          placeholder="应用id"
        />
        <!--<el-input v-model="dataForm.appId" placeholder="应用id"></el-input>-->
      </el-form-item>
      <el-form-item label="用户等级名称" prop="levelTitle">
        <el-input v-model="dataForm.levelTitle" placeholder="用户等级名称" />
      </el-form-item>
      <el-form-item label="等级下限值" prop="lowLimit">
        <el-input
          type="number"
          v-model.number="dataForm.lowLimit"
          placeholder="等级下限值"
        />
      </el-form-item>
      <el-form-item label="等级上限值" prop="highLimit">
        <el-input
          type="number"
          v-model.number="dataForm.highLimit"
          placeholder="等级上限值"
        />
      </el-form-item>
      <el-form-item label="排序值" prop="sortValue">
        <el-input
          type="number"
          v-model.number="dataForm.sortValue"
          placeholder="排序值"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-select v-model="dataForm.enabled">
          <el-option
            v-for="[key, label] in userLevelConfigStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import AppSelectComponent from '@/components/app-select-component'
import { userLevelConfigStatus } from '@/map/lzhbq'
export default {
  components: {
    AppSelectComponent,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appId: '',
        levelTitle: '',
        lowLimit: '',
        highLimit: '',
        sortValue: '',
        enabled: '',
      },
      dataRule: {
        appId: [{ required: true, message: '应用id不能为空', trigger: 'blur' }],
        levelTitle: [
          { required: true, message: '用户等级名称不能为空', trigger: 'blur' },
        ],
        lowLimit: [
          { required: true, message: '等级下限值不能为空', trigger: 'blur' },
        ],
        highLimit: [
          { required: true, message: '等级上限值不能为空', trigger: 'blur' },
        ],
        sortValue: [
          { required: true, message: '排序值不能为空', trigger: 'blur' },
        ],
        enabled: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
      userLevelConfigStatus,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2userlevelconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.hong2UserLevelConfig.appId
              this.dataForm.levelTitle = data.hong2UserLevelConfig.levelTitle
              this.dataForm.lowLimit = data.hong2UserLevelConfig.lowLimit
              this.dataForm.highLimit = data.hong2UserLevelConfig.highLimit
              this.dataForm.sortValue = data.hong2UserLevelConfig.sortValue
              this.dataForm.enabled = data.hong2UserLevelConfig.enabled
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2userlevelconfig/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              levelTitle: this.dataForm.levelTitle,
              lowLimit: this.dataForm.lowLimit,
              highLimit: this.dataForm.highLimit,
              sortValue: this.dataForm.sortValue,
              enabled: this.dataForm.enabled,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
