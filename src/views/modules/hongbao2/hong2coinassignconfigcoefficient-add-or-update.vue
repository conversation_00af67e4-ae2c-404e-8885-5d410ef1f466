<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="100px"
    >
      <el-form-item label="判断条件" prop="coinAssignConfigId">
        <el-select
          v-model="dataForm.coinAssignConfigId"
          :disabled="!!dataForm.id"
        >
          <el-option
            v-for="(item, index) in conditionList"
            :key="index"
            :label="item.judgeCondition"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="配置值" prop="configValue">
        <el-input-number v-model="dataForm.configValue" placeholder="配置值" />
      </el-form-item>
      <el-form-item label="权重" prop="weights">
        <el-input v-model="dataForm.weights" placeholder="权重"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        judgeCondition: '',
        coinAssignConfigId: '',
        weights: '',
        configValue: '',
      },
      dataRule: {
        judgeCondition: [
          {
            required: true,
            message: '判断条件不能为空',
            trigger: 'blur',
          },
        ],
        coinAssignConfigId: [
          {
            required: true,
            message: '金币发放配置表id不能为空',
            trigger: 'blur',
          },
        ],
        weights: [
          {
            required: true,
            message: '权重不能为空',
            trigger: 'blur',
          },
        ],
        configValue: [
          {
            required: true,
            message: 'configValue不能为空',
            trigger: 'blur',
          },
        ],
      },
      conditionList: [],
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getDataList()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong2/appcoinassignconfigcoefficient/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.judgeCondition =
                data.appCoinAssignConfigCoefficient.judgeCondition
              this.dataForm.coinAssignConfigId =
                data.appCoinAssignConfigCoefficient.coinAssignConfigId
              this.dataForm.weights =
                data.appCoinAssignConfigCoefficient.weights
              this.dataForm.configValue =
                data.appCoinAssignConfigCoefficient.configValue
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const res = this.conditionList.find(
            it => it.id === this.dataForm.coinAssignConfigId
          )
          const judgeCondition =
            res && res.judgeCondition ? res.judgeCondition : ''

          this.$http({
            url: this.$http.adornUrl(
              `/hong2/appcoinassignconfigcoefficient/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              judgeCondition: judgeCondition,
              coinAssignConfigId: this.dataForm.coinAssignConfigId,
              weights: this.dataForm.weights,
              configValue: this.dataForm.configValue,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.$http({
        url: this.$http.adornUrl('/hong2/appcoinassignconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 100,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.conditionList = data.page.list
        } else {
          this.conditionList = []
        }
      })
    },
  },
}
</script>
