<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="190"
  >
    <template #table_item_enabled="{row}">
      <tag-status
        :status="row.enabled"
        active-text="启用"
        inactive-text="关闭"
      />
    </template>
  </page-table>
</template>

<script>
// import { fishingDrawConfig as request, baseConfigRequest } from '@/api/fishing'
import { drawConfigMap } from '@/map/fishing'
import { activeValueCoefficientRequest as request } from '@/api/hong2'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          // { field: 'appId', title: '应用id', width: 100, },
          { field: 'ruleName', title: '规则名称' },
          { field: 'ecpmLowLimit', title: 'ecpm下限' },
          { field: 'ecpmHighLimit', title: 'ecpm上限' },
          { field: 'coefficient', title: '衰减系数' },
          {
            field: 'enabled',
            title: '状态',
            slots: { default: 'table_item_enabled' },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
          { field: 'createdBy', title: '创建者' },
          { field: 'updatedBy', title: '更新者' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '90px',
        },
        formData: {
          id: null,
          ruleName: null,
          ecpmLowLimit: null,
          ecpmHighLimit: null,
          coefficient: null,
          enabled: null,
        },
        formItemMap: {
          ruleName: {
            title: '规则名称',
          },
          ecpmLowLimit: {
            title: 'ecpm下限',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          ecpmHighLimit: {
            title: 'ecpm上限',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          coefficient: {
            title: '衰减系数',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          enabled: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
              attrs: {
                activeText: '启用',
                inactiveText: '关闭',
              },
            },
          },
        },
        formRule: {
          ruleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
          ecpmLowLimit: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          ecpmHighLimit: [
            { required: true, message: '不能为空', trigger: 'blur' },
            {
              validator: (_, value, callback) => {
                if (
                  Number(value) < Number(this.modelConfig.formData.ecpmLowLimit)
                ) {
                  callback(new Error('不能小于 ecpm 下限'))
                  return
                }
                callback()
              },
            },
          ],
          coefficient: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          enabled: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
}
</script>
