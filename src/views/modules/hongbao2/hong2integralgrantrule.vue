<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="阶段类型" prop="levelType">
        <map-select
          v-model="dataForm.levelType"
          :list="integralLevelTypeList"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="currentChangeHandle(1)"
          type="primary"
        >
          查询
        </el-button>
        <el-button
          v-if="isAuth('hongbao2:hong2integralgrantrule:save')"
          icon="el-icon-plus"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('hongbao2:hong2integralgrantrule:delete')"
          icon="el-icon-delete"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      />
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
        width="50px"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <el-tag>
            {{ row.appId | getAppName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="levelType"
        header-align="center"
        align="center"
        label="阶段类型"
      >
        <template slot-scope="{ row }">
          <span>{{ integralLevelTypeList.get(row.levelType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="taskTitle"
        header-align="center"
        align="center"
        label="当前配置的说明"
        width="120px"
      />
      <el-table-column
        prop="askLowLimit"
        header-align="center"
        align="center"
        label="要求下限值"
      />
      <el-table-column
        prop="askHighLimit"
        header-align="center"
        align="center"
        label="要求上限值"
      />
      <el-table-column
        prop="lowLimit"
        header-align="center"
        align="center"
        label="系数配置区间下限值"
        width="130px"
      />
      <el-table-column
        prop="highLimit"
        header-align="center"
        align="center"
        label="系数配置区间上限值"
        width="130px"
      />
      <el-table-column
        prop="sortValue"
        header-align="center"
        align="center"
        label="排序值"
      />
      <el-table-column
        prop="enabled"
        header-align="center"
        align="center"
        label="是否启用"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.enabled === 1 ? 'success' : ''">
            {{ integralGrantRuleStatus.get(row.enabled) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        prop="createdBy"
        header-align="center"
        align="center"
        label="创建者"
      />
      <el-table-column
        prop="updatedBy"
        header-align="center"
        align="center"
        label="更新者"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import MapSelect from '@/components/map-select'
import AddOrUpdate from './hong2integralgrantrule-add-or-update'
import { integralGrantRuleStatus, integralLevelTypeList } from '@/map/lzhbq'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        levelType: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      integralLevelTypeList,
      integralGrantRuleStatus,
    }
  },
  components: {
    AddOrUpdate,
    MapSelect,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hongbao2/hong2integralgrantrule/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/hongbao2/hong2integralgrantrule/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
