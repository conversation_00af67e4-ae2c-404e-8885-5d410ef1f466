<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="500px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="150px"
    >
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model="dataForm.appId"
          placeholder="应用"
          :is-show-all="false"
          width="100%"
        />
      </el-form-item>
      <el-form-item label="阶段类型" prop="levelType">
        <el-select v-model="dataForm.levelType" style="width: 100%;">
          <el-option
            v-for="[key, label] in integralLevelTypeList"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="要求下限值" prop="askLowLimit">
        <el-input
          type="number"
          v-model.number="dataForm.askLowLimit"
          placeholder="要求下限值"
        />
      </el-form-item>
      <el-form-item label="要求上限值" prop="askHighLimit">
        <el-input
          type="number"
          v-model.number="dataForm.askHighLimit"
          placeholder="要求上限值"
        />
      </el-form-item>
      <el-form-item label="系数配置区间下限值" prop="lowLimit">
        <el-input
          type="number"
          v-model.number="dataForm.lowLimit"
          placeholder="系数配置区间下限值"
        />
      </el-form-item>
      <el-form-item label="系数配置区间上限值" prop="highLimit">
        <el-input
          type="number"
          v-model.number="dataForm.highLimit"
          placeholder="系数配置区间上限值"
        />
      </el-form-item>
      <el-form-item label="排序值" prop="sortValue">
        <el-input
          type="number"
          v-model.number="dataForm.sortValue"
          placeholder="排序值"
        />
      </el-form-item>
      <el-form-item label="当前配置的说明" prop="taskTitle">
        <el-input
          type="textarea"
          v-model="dataForm.taskTitle"
          placeholder="当前配置的说明"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-select v-model="dataForm.enabled">
          <el-option
            v-for="[key, label] in integralGrantRuleStatus"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="isLoading">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { integralGrantRuleStatus, integralLevelTypeList } from '@/map/lzhbq'
import AppSelectComponent from '@/components/app-select-component'

export default {
  components: { AppSelectComponent },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appId: '',
        levelType: '',
        taskTitle: '',
        askLowLimit: '',
        askHighLimit: '',
        lowLimit: '',
        highLimit: '',
        sortValue: '',
        enabled: '',
      },
      dataRule: {
        appId: [{ required: true, message: '应用不能为空', trigger: 'blur' }],
        levelType: [
          {
            required: true,
            message: '阶段类型不能为空',
            trigger: 'blur',
          },
        ],
        taskTitle: [
          {
            required: true,
            message: '当前配置的说明不能为空',
            trigger: 'blur',
          },
        ],
        askLowLimit: [
          { required: true, message: '要求下限值不能为空', trigger: 'blur' },
        ],
        askHighLimit: [
          { required: true, message: '要求上限值不能为空', trigger: 'blur' },
        ],
        lowLimit: [
          { required: true, message: '配置的下限值不能为空', trigger: 'blur' },
        ],
        highLimit: [
          { required: true, message: '配置的上限值不能为空', trigger: 'blur' },
        ],
        sortValue: [
          {
            required: true,
            message: '排序值，类型 sort_value必填，最大的值为兜底不能为空',
            trigger: 'blur',
          },
        ],
        enabled: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
      },
      integralLevelTypeList,
      integralGrantRuleStatus,
      isLoading: false,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2integralgrantrule/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.hong2IntegralGrantRule.appId
              this.dataForm.levelType = data.hong2IntegralGrantRule.levelType
              this.dataForm.taskTitle = data.hong2IntegralGrantRule.taskTitle
              this.dataForm.askLowLimit =
                data.hong2IntegralGrantRule.askLowLimit
              this.dataForm.askHighLimit =
                data.hong2IntegralGrantRule.askHighLimit
              this.dataForm.lowLimit = data.hong2IntegralGrantRule.lowLimit
              this.dataForm.highLimit = data.hong2IntegralGrantRule.highLimit
              this.dataForm.sortValue = data.hong2IntegralGrantRule.sortValue
              this.dataForm.enabled = data.hong2IntegralGrantRule.enabled
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.isLoading = true
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao2/hong2integralgrantrule/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              levelType: this.dataForm.levelType,
              taskTitle: this.dataForm.taskTitle,
              askLowLimit: this.dataForm.askLowLimit,
              askHighLimit: this.dataForm.askHighLimit,
              lowLimit: this.dataForm.lowLimit,
              highLimit: this.dataForm.highLimit,
              sortValue: this.dataForm.sortValue,
              enabled: this.dataForm.enabled,
            }),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  },
                })
              } else {
                this.$message.error(data.msg)
              }
            })
            .finally(() => {
              this.isLoading = false
            })
        }
      })
    },
  },
}
</script>
