<template>
  <div class="mod-config">
    <page-table
      :grid-config="gridOptions"
      :request-collection="withdrawalConfig"
      :model-config="modelConfig"
    >
      <template #status_item="{ data }">
        <map-select
          v-model="data.status"
          :list="withdrawalConfigMap.statusList"
          placeholder="请选择"
          clearable
        />
      </template>
      <template #version_item="{ data }">
        <app-version-select
          v-model="data.version"
          :multiple="false"
          :is-show-tools="false"
          :app-id="config.appId"
          placeholder="请选择"
          clearable
        />
      </template>

      <template #table_item_version="{row}">
        <template v-if="row.version">
          <el-popover
            placement="top-start"
            title="全部版本"
            width="200"
            trigger="hover"
          >
            <div>
              {{ row.version | getVersionNameList(appVersionList) }}
            </div>
            <div slot="reference">
              <template v-for="(item, index) in row.version">
                <color-tag
                  v-if="index < 4"
                  :key="index"
                  :id="item"
                  style="margin-right: 5px;"
                >
                  {{ item | getVersionName(appVersionList) }}
                </color-tag>
              </template>
            </div>
          </el-popover>
        </template>
      </template>

      <template #table_item_type="{row}">
        <color-tag :id="row.type">
          {{ withdrawalConfigMap.typeList.get(row.type) }}
        </color-tag>
      </template>

      <template #table_item_status="{row}">
        <color-tag :id="row.status">
          {{ withdrawalConfigMap.statusList.get(row.status) }}
        </color-tag>
      </template>
      <template #table_item_needAudit="{row}">
        <color-tag :id="row.needAudit">
          {{ withdrawalConfigMap.needAuditList.get(row.needAudit) }}
        </color-tag>
      </template>

      <template #model>
        <el-form-item label="可见版本配置" prop="version">
          <app-version-select
            v-model="modelConfig.formData.version"
            :app-id="config.appId"
            placeholder="应用版本号"
          />
        </el-form-item>
        <el-form-item label="提现额度" prop="amount">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.amount"
            placeholder="请输入提现额度，仅支持小数点后1位"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="排序值" prop="sortValue">
          <el-input
            v-model="modelConfig.formData.sortValue"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="角标" prop="mark">
          <el-input
            v-model="modelConfig.formData.mark"
            placeholder="支持最多4个字的文本，空为不显示"
          />
        </el-form-item>
        <el-form-item label="提现类型" prop="type">
          <map-radio
            v-model="modelConfig.formData.type"
            :list="withdrawalConfigMap.typeList"
          />
        </el-form-item>
        <el-form-item label="概率" prop="probability">
          <!--<percentage-input-->
          <!--  v-model="modelConfig.formData.probability"-->
          <!--  placeholder="请输入抽奖的时被抽到的概率"-->
          <!--  :fixed="4"-->
          <!--/>-->
          <el-input
            type="number"
            v-model="modelConfig.formData.probability"
            placeholder="请输入抽奖的时被抽到的概率"
            @blur="handleProbability"
          />
        </el-form-item>
        <el-form-item label="全服提现次数" prop="fullServiceTimes">
          <FullServiceTimes v-model="modelConfig.formData.fullServiceTimes" />
        </el-form-item>
        <el-form-item label="注册时间限制" prop="registerTimeLimit">
          <RegisterTimeLimit
            ref="registerTimeLimit"
            v-model="modelConfig.formData.registerTimeLimit"
            placeholder="注册时间限制"
          />
        </el-form-item>
        <el-form-item
          label="提现等级"
          prop="askLevel"
          style="margin-top: -20px"
        >
          <el-input
            type="number"
            v-model.number="modelConfig.formData.askLevel"
            placeholder="提现等级要求"
          />
        </el-form-item>
        <el-form-item label="累计登录天数" prop="askDays">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.askDays"
            placeholder="累计登录天数"
          />
        </el-form-item>
        <el-form-item label="累计任务数" prop="askTask">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.askTask"
            placeholder="累计任务数"
          />
        </el-form-item>
        <el-form-item label="解锁答题数" prop="lockNum">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.lockNum"
            placeholder="累计任务数"
          />
        </el-form-item>
        <el-form-item label="最低ecpm" prop="askEcpm">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.askEcpm"
            placeholder="提现最低的ecpm"
          />
        </el-form-item>
        <el-form-item label="点击广告数" prop="askAdNum">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.askAdNum"
            placeholder="每日点击广告数"
          />
        </el-form-item>
        <el-form-item label="外部场景code" prop="sceneCode">
          <el-input
            v-model.number="modelConfig.formData.sceneCode"
            placeholder="外部场景code"
          />
        </el-form-item>
        <el-form-item label="可见概率" prop="showProbability">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.showProbability"
            placeholder="可见概率"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            type="textarea"
            v-model="modelConfig.formData.description"
            placeholder="描述"
          />
        </el-form-item>
        <!--<el-form-item label="提现限制的次数" prop="limitNum">-->
        <!--  <el-input-->
        <!--    v-model="modelConfig.formData.limitNum"-->
        <!--    placeholder="提现限制的次数"-->
        <!--  />-->
        <!--</el-form-item>-->
        <!--<el-form-item label="提现限制" prop="withdrawalLimit">-->
        <!--  <map-select-->
        <!--    v-model="modelConfig.formData.withdrawalLimit"-->
        <!--    :list="withdrawalConfigMap.withdrawalLimitList"-->
        <!--  />-->
        <!--</el-form-item>-->
        <!--<el-form-item label="额外信息" prop="extra">-->
        <!--  <el-input-->
        <!--    v-model="modelConfig.formData.extra"-->
        <!--    placeholder="额外信息"-->
        <!--  />-->
        <!--</el-form-item>-->
        <el-form-item label="状态" prop="status">
          <map-radio
            v-model="modelConfig.formData.status"
            :list="withdrawalConfigMap.statusList"
          />
        </el-form-item>
        <el-form-item label="审核" prop="needAudit">
          <map-radio
            v-model="modelConfig.formData.needAudit"
            :list="withdrawalConfigMap.needAuditList"
          />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
// import PercentageInput from '@/components/percentage-input'
import { withdrawalConfigRequest } from '@/api/videoQa'
import { withdrawalConfigMap } from '@/map/videoQa'
import config from './config'
import AppVersionSelect from '@/components/app-version-select'
import FullServiceTimes from './components/FullServiceTimes'
import RegisterTimeLimit from './components/RegisterTimeLimit'
import MapSelect from '@/components/map-select'
import { getAllAppVersion } from '@/repository/app'

export default {
  components: {
    MapSelect,
    AppVersionSelect,
    FullServiceTimes,
    RegisterTimeLimit,
    // PercentageInput,
  },
  data() {
    return {
      config,
      withdrawalConfigMap,
      withdrawalConfig: withdrawalConfigRequest,
      gridOptions: {
        formConfig: {
          items: [
            {
              field: 'version',
              title: '版本',
              itemRender: {
                defaultValue: '',
              },
              slots: { default: 'version_item' },
            },
            {
              field: 'status',
              title: '状态',
              itemRender: { defaultValue: '' },
              slots: { default: 'status_item' },
            },
          ],
        },
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', width: 50, title: 'ID' },
          {
            field: 'version',
            title: '版本',
            width: 120,
            slots: { default: 'table_item_version' },
          },
          { field: 'amount', title: '提现金额/元', width: 100 },
          {
            field: 'type',
            title: '提现类型',
            slots: { default: 'table_item_type' },
            width: 100,
          },
          { field: 'probability', title: '概率', width: 100 },
          { field: 'limitNum', title: '每日上限', width: 100 },
          { field: 'askLevel', title: '提现等级', width: 100 },
          { field: 'askDays', title: '累计登录天数', width: 100 },
          { field: 'askTask', title: '完成任务数', width: 100 },
          { field: 'askDays', title: '累计登录天数', width: 100 },
          { field: 'lockNum', title: '解锁答题数', width: 100 },
          { field: 'askEcpm', title: 'ecpm', width: 100 },
          { field: 'askAdNum', title: '点击广告数', width: 100 },
          { field: 'sceneCode', title: '外部场景code', width: 100 },
          { field: 'showProbability', title: '可见概率', width: 100 },
          {
            field: 'status',
            title: '状态',
            width: 100,
            slots: { default: 'table_item_status' },
          },
          {
            field: 'needAudit',
            title: '自动审核',
            width: 100,
            slots: { default: 'table_item_needAudit' },
          },
          { field: 'sortValue', title: '排序值', width: 100 },
          { field: 'createdAt', title: '创建时间', width: 150 },
          { field: 'updatedAt', title: '更新时间', width: 150 },
          { field: 'createdBy', title: '创建者', width: 70 },
          { field: 'updatedBy', title: '最新编辑者', width: 80 },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '520px',
        },
        formConfig: {
          labelWidth: '120px',
          beforeSubmit: () => {
            return this.$refs.registerTimeLimit.validate()
          },
        },
        formData: {
          id: 0,
          appCode: config.appCode,
          amount: 0,
          type: null,
          sortValue: 0,
          needAudit: null,
          description: '',
          withdrawalLimit: null,
          limitNum: '',
          version: [],
          extra: '',
          status: null,
          mark: '',
          probability: '',
          sceneCode: '',
          showProbability: null,
          fullServiceTimes: [],
          askLevel: 0,
          askEcpm: 0,
          askAdNum: 0,
          askDays: 0,
          askTask: 0,
          lockNum: 0,
          registerTimeLimit: {},
        },
        formRule: {
          amount: [
            {
              required: true,
              message: '提现金额不能为空',
              trigger: 'blur',
            },
          ],
          lockNum: [
            {
              required: true,
              message: '提现金额不能为空',
              trigger: 'blur',
            },
          ],
          type: [
            {
              required: true,
              message: '提现类型不能为空',
              trigger: 'blur',
            },
          ],
          askTask: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          needAudit: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          version: [{ required: true, message: '不能为空', trigger: 'blur' }],
          sortValue: [{ required: true, message: '不能为空', trigger: 'blur' }],
          registerTimeLimit: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          status: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          fullServiceTimes: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          askLevel: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          askEcpm: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          askDays: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          askAdNum: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
        },
      },
      appVersionList: [],
    }
  },
  created() {
    getAllAppVersion(config.appId).then(list => {
      this.appVersionList = list
    })
  },
  methods: {
    handleProbability() {
      this.modelConfig.formData.probability = Number(
        Number(this.modelConfig.formData.probability).toFixed(4)
      )
    },
  },
}
</script>
