<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="人气值，初始值2500" prop="popularityValue">
      <el-input v-model="dataForm.popularityValue" placeholder="人气值，初始值2500"></el-input>
    </el-form-item>
    <el-form-item label="账户金币余额" prop="coinBalance">
      <el-input v-model="dataForm.coinBalance" placeholder="账户金币余额"></el-input>
    </el-form-item>
    <el-form-item label="冻结金币" prop="frozenCoin">
      <el-input v-model="dataForm.frozenCoin" placeholder="冻结金币"></el-input>
    </el-form-item>
    <el-form-item label="冻结金币状态：0：无冻结，1：有冻结" prop="frozenStatus">
      <el-input v-model="dataForm.frozenStatus" placeholder="冻结金币状态：0：无冻结，1：有冻结"></el-input>
    </el-form-item>
    <el-form-item label="用户等级，新增用户注册默认等级1" prop="level">
      <el-input v-model="dataForm.level" placeholder="用户等级，新增用户注册默认等级1"></el-input>
    </el-form-item>
    <el-form-item label="当前用户等级进度：满100 等级加1" prop="levelProcess">
      <el-input v-model="dataForm.levelProcess" placeholder="当前用户等级进度：满100 等级加1"></el-input>
    </el-form-item>
    <el-form-item label="用户积分" prop="integral">
      <el-input v-model="dataForm.integral" placeholder="用户积分"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          userId: '',
          popularityValue: '',
          coinBalance: '',
          frozenCoin: '',
          frozenStatus: '',
          level: '',
          levelProcess: '',
          integral: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          popularityValue: [
            { required: true, message: '人气值，初始值2500不能为空', trigger: 'blur' }
          ],
          coinBalance: [
            { required: true, message: '账户金币余额不能为空', trigger: 'blur' }
          ],
          frozenCoin: [
            { required: true, message: '冻结金币不能为空', trigger: 'blur' }
          ],
          frozenStatus: [
            { required: true, message: '冻结金币状态：0：无冻结，1：有冻结不能为空', trigger: 'blur' }
          ],
          level: [
            { required: true, message: '用户等级，新增用户注册默认等级1不能为空', trigger: 'blur' }
          ],
          levelProcess: [
            { required: true, message: '当前用户等级进度：满100 等级加1不能为空', trigger: 'blur' }
          ],
          integral: [
            { required: true, message: '用户积分不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/videoqa/guessuseraccount/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.userId = data.guessUserAccount.userId
                this.dataForm.popularityValue = data.guessUserAccount.popularityValue
                this.dataForm.coinBalance = data.guessUserAccount.coinBalance
                this.dataForm.frozenCoin = data.guessUserAccount.frozenCoin
                this.dataForm.frozenStatus = data.guessUserAccount.frozenStatus
                this.dataForm.level = data.guessUserAccount.level
                this.dataForm.levelProcess = data.guessUserAccount.levelProcess
                this.dataForm.integral = data.guessUserAccount.integral
                this.dataForm.createdAt = data.guessUserAccount.createdAt
                this.dataForm.updatedAt = data.guessUserAccount.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/videoqa/guessuseraccount/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'userId': this.dataForm.userId,
                'popularityValue': this.dataForm.popularityValue,
                'coinBalance': this.dataForm.coinBalance,
                'frozenCoin': this.dataForm.frozenCoin,
                'frozenStatus': this.dataForm.frozenStatus,
                'level': this.dataForm.level,
                'levelProcess': this.dataForm.levelProcess,
                'integral': this.dataForm.integral,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
