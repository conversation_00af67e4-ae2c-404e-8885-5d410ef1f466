<template>
  <el-form ref="form" inline :model="formData" :rules="formRule">
    <el-form-item prop="days">
      <el-input
        type="number"
        v-model.number="formData.days"
        placeholder="请填写注册时间"
        clearable
      />
    </el-form-item>
    <el-form-item prop="type">
      <el-select v-model="formData.type" style="width: 100px">
        <el-option :value="1" label="之前" />
        <el-option :value="2" label="之后" />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'RegisterTimeLimit',
  props: {
    value: {
      type: Object,
    },
  },
  computed: {
    formData: {
      get() {
        return this.value
      },
      // 这个里根本不走
      // set(o) {
      //   this.$emit('input', o)
      // },
    },
  },
  data() {
    return {
      formRule: {
        type: [{ required: true, message: '不能为空', trigger: 'blur' }],
        days: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  methods: {
    validate() {
      return this.$refs.form.validate()
    },
  },
}
</script>
