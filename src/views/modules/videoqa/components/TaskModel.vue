<template>
  <vxe-modal
    v-model="showModel"
    :title="title"
    width="500px"
    @show="handleShow"
  >
    <template #default>
      <vxe-form
        ref="form"
        :data="dataForm"
        span="24"
        titleAlign="right"
        title-width="80"
        :rules="rules"
      >
        <vxe-form-item title="版本选择" field="version">
          <template #default="{ data }">
            <app-version-select :app-id="config.appId" v-model="data.version" />
          </template>
        </vxe-form-item>
        <vxe-form-item title="任务类型" field="taskType">
          <template #default="{ data }">
            <map-select v-model.number="data.taskType" :list="taskTypeList" />
          </template>
        </vxe-form-item>
        <vxe-form-item title="任务名称" field="taskTitle">
          <template #default="{ data }">
            <vxe-input
              v-model="data.taskTitle"
              placeholder="请输入任务名称"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="展示奖励" field="rewardShow">
          <template #default="{ data }">
            <vxe-input
              v-model="data.rewardShow"
              placeholder="请输入展示奖励"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="按钮文案" field="buttonTitle">
          <template #default="{ data }">
            <vxe-input
              v-model="data.buttonTitle"
              placeholder="请输入按钮文案"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item
          title="实际奖励最小值"
          span="12"
          title-width="110"
          field="lowLimit"
        >
          <template #default="{ data }">
            <vxe-input
              v-model.number="data.lowLimit"
              type="number"
              placeholder="请输入数字"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item
          title="实际奖励最大值"
          span="12"
          title-width="110"
          field="highLimit"
        >
          <template #default="{ data }">
            <vxe-input
              v-model.number="data.highLimit"
              type="number"
              placeholder="请输入数字"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="任务跳转" field="jumpType">
          <template #default="{ data }">
            <vxe-radio-group v-model="data.jumpType">
              <vxe-radio
                v-for="[key, label] in jumpTypeList"
                :key="key"
                :label="key"
                :content="label"
              />
            </vxe-radio-group>
          </template>
        </vxe-form-item>
        <vxe-form-item
          v-if="dataForm.jumpType === 2"
          title="跳转链接"
          field="jumpUrl"
        >
          <template #default="{ data }">
            <vxe-input
              v-model="data.jumpUrl"
              placeholder="请输入跳转链接"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="完成条件" field="askTime">
          <template #default="{ data }">
            <vxe-input
              v-model.number="data.askTime"
              type="number"
              placeholder="请输入次数"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="领奖广告" field="adPosition">
          <template #default="{ data }">
            <vxe-input
              v-model="data.adPosition"
              placeholder="请输入广告位"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="权重" field="sortValue">
          <template #default="{ data }">
            <vxe-input
              v-model="data.sortValue"
              placeholder="请输入权重"
              clearable
            />
          </template>
        </vxe-form-item>
        <vxe-form-item title="状态" field="enabled">
          <template #default="{ data }">
            <vxe-radio-group v-model="data.enabled">
              <vxe-radio
                v-for="[key, label] in taskEnabled"
                :key="key"
                :label="key"
                :content="label"
              />
            </vxe-radio-group>
          </template>
        </vxe-form-item>
        <vxe-form-item align="center">
          <template #default>
            <vxe-button @click="showModel = false">取消</vxe-button>
            <vxe-button status="primary" @click="submitEvent">提交</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </template>
  </vxe-modal>
</template>

<script>
import AppVersionSelect from '@/components/app-version-select'
import { taskConfigRequest } from '@/api/videoQa'
import MapSelect from '@/components/map-select'
import { jumpTypeList, taskEnabledList, taskTypeList } from '@/map/videoQa'
import config from '../config'

const initDataForm = {
  id: 0,
  appId: '',
  taskType: 1,
  taskTitle: '',
  sortValue: '',
  enabled: '',
  version: [],
  rewardShow: '',
  highLimit: '',
  lowLimit: '',
  jumpType: 1,
  jumpUrl: '',
  askTime: '',
  adPosition: '',
  buttonTitle: '领取',
}

export default {
  name: 'TaskModel',
  components: { MapSelect, AppVersionSelect },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dataId: {
      type: [Number, String],
    },
  },
  computed: {
    showModel: {
      set(v) {
        this.$emit('update:visible', v)
      },
      get() {
        return this.visible
      },
    },
    title() {
      return this.dataId ? '编辑' : '新增'
    },
  },
  data() {
    return {
      config,
      jumpTypeList,
      taskEnabled: taskEnabledList,
      taskTypeList,
      dataForm: {
        ...initDataForm,
      },
      rules: {
        taskType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        taskTitle: [{ required: true, message: '不能为空', trigger: 'blur' }],
        sortValue: [{ required: true, message: '不能为空', trigger: 'blur' }],
        enabled: [{ required: true, message: '不能为空', trigger: 'blur' }],
        version: [{ required: true, message: '不能为空', trigger: 'blur' }],
        rewardShow: [{ required: true, message: '不能为空', trigger: 'blur' }],
        highLimit: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: ({ itemValue, data }) => {
              if (itemValue < data.lowLimit) {
                return new Error('不能小于最小值')
              }
            },
          },
        ],
        lowLimit: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: ({ itemValue, data }) => {
              if (itemValue > data.highLimit) {
                return new Error('不能大于最大值')
              }
            },
          },
        ],
        jumpType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        jumpUrl: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { message: '不是url', pattern: /^http[s]?:\/\/.*/, trigger: 'blur' },
        ],
        askTime: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // adPosition: [{ required: true, message: '不能为空', trigger: 'blur' }],
        buttonTitle: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  methods: {
    submitEvent() {
      this.$refs.form.validate().then(ErrMap => {
        if (!ErrMap) {
          taskConfigRequest
            .insertOrUpdate(this.dataId, this.dataForm)
            .then(() => {
              this.showModel = false
              this.$vxeMessage({
                content: '更新成功',
                status: 'success',
              })
              this.$emit('refresh')
            })
        }
      })
    },
    getTaskItem() {
      taskConfigRequest.selectItem(this.dataId).then(res => {
        if (res.guessTaskConfig) {
          try {
            this.dataForm = Object.assign(
              {},
              this.dataForm,
              JSON.parse(
                JSON.stringify(res.guessTaskConfig, Object.keys(this.dataForm)),
                (k, v) => (k === 'version' && !v ? [] : v)
              )
            )
          } catch (e) {
            console.log()
          }
        }
      })
    },
    handleShow() {
      this.dataId && this.getTaskItem()
    },
  },
}
</script>
