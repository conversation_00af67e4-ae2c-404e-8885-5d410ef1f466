<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="app表 code" prop="appCode">
      <el-input v-model="dataForm.appCode" placeholder="app表 code"></el-input>
    </el-form-item>
    <el-form-item label="1:自动审核发放金额限制，2：单个用户每日观看视频数上限，3：单个用户每日获得金币上限，4：ecmp固定值，5：人气值增加，6：人气值减少，7：刮刮卡金币，8：刮刮卡每日活动上限，9：金猪随机发放金额，10：金币随机金币发放配置" prop="ruleType">
      <el-input v-model="dataForm.ruleType" placeholder="1:自动审核发放金额限制，2：单个用户每日观看视频数上限，3：单个用户每日获得金币上限，4：ecmp固定值，5：人气值增加，6：人气值减少，7：刮刮卡金币，8：刮刮卡每日活动上限，9：金猪随机发放金额，10：金币随机金币发放配置"></el-input>
    </el-form-item>
    <el-form-item label="风控规则名称" prop="ruleName">
      <el-input v-model="dataForm.ruleName" placeholder="风控规则名称"></el-input>
    </el-form-item>
    <el-form-item label="数值下限" prop="lowValue">
      <el-input v-model="dataForm.lowValue" placeholder="数值下限"></el-input>
    </el-form-item>
    <el-form-item label="数值上限" prop="highValue">
      <el-input v-model="dataForm.highValue" placeholder="数值上限"></el-input>
    </el-form-item>
    <el-form-item label="启用状态：0 未开启，1：开启" prop="status">
      <el-input v-model="dataForm.status" placeholder="启用状态：0 未开启，1：开启"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间 最近编辑时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间 最近编辑时间"></el-input>
    </el-form-item>
    <el-form-item label="创建者" prop="createdBy">
      <el-input v-model="dataForm.createdBy" placeholder="创建者"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updatedBy">
      <el-input v-model="dataForm.updatedBy" placeholder="更新者"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appCode: '',
          ruleType: '',
          ruleName: '',
          lowValue: '',
          highValue: '',
          status: '',
          createdAt: '',
          updatedAt: '',
          createdBy: '',
          updatedBy: ''
        },
        dataRule: {
          appCode: [
            { required: true, message: 'app表 code不能为空', trigger: 'blur' }
          ],
          ruleType: [
            { required: true, message: '1:自动审核发放金额限制，2：单个用户每日观看视频数上限，3：单个用户每日获得金币上限，4：ecmp固定值，5：人气值增加，6：人气值减少，7：刮刮卡金币，8：刮刮卡每日活动上限，9：金猪随机发放金额，10：金币随机金币发放配置不能为空', trigger: 'blur' }
          ],
          ruleName: [
            { required: true, message: '风控规则名称不能为空', trigger: 'blur' }
          ],
          lowValue: [
            { required: true, message: '数值下限不能为空', trigger: 'blur' }
          ],
          highValue: [
            { required: true, message: '数值上限不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '启用状态：0 未开启，1：开启不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间 最近编辑时间不能为空', trigger: 'blur' }
          ],
          createdBy: [
            { required: true, message: '创建者不能为空', trigger: 'blur' }
          ],
          updatedBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/videoqa/guesslimitrule/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appCode = data.guessLimitRule.appCode
                this.dataForm.ruleType = data.guessLimitRule.ruleType
                this.dataForm.ruleName = data.guessLimitRule.ruleName
                this.dataForm.lowValue = data.guessLimitRule.lowValue
                this.dataForm.highValue = data.guessLimitRule.highValue
                this.dataForm.status = data.guessLimitRule.status
                this.dataForm.createdAt = data.guessLimitRule.createdAt
                this.dataForm.updatedAt = data.guessLimitRule.updatedAt
                this.dataForm.createdBy = data.guessLimitRule.createdBy
                this.dataForm.updatedBy = data.guessLimitRule.updatedBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/videoqa/guesslimitrule/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appCode': this.dataForm.appCode,
                'ruleType': this.dataForm.ruleType,
                'ruleName': this.dataForm.ruleName,
                'lowValue': this.dataForm.lowValue,
                'highValue': this.dataForm.highValue,
                'status': this.dataForm.status,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt,
                'createdBy': this.dataForm.createdBy,
                'updatedBy': this.dataForm.updatedBy
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
