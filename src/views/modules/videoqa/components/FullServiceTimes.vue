<template>
  <el-form :model="formData">
    <el-row v-for="(item, index) in formData.list" :key="index">
      <el-col :span="12">
        <el-form-item :prop="`list.${index}.num`">
          <el-input
            type="number"
            v-model.number="item.num"
            placeholder="请输入全服提现次数"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item :prop="`list.${index}.time`">
          <el-select
            v-model="item.time"
            style="margin-left: 10px;"
            placeholder="请选择刷新时间"
            clearable
          >
            <el-option
              v-for="t in 24"
              :key="t"
              :label="`${t - 1} 点`"
              :value="t - 1"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="2">
        <el-button
          type="text"
          icon="el-icon-delete"
          circle
          style="color: darkred; margin-left: 10px"
          @click="remove(index)"
        />
      </el-col>
    </el-row>
    <el-button type="text" icon="el-icon-circle-plus-outline" @click="add">
      添加提现次数
    </el-button>
  </el-form>
</template>

<script>
export default {
  name: 'FullServiceTimes',
  props: {
    value: {
      type: Array,
    },
  },
  data() {
    return {}
  },
  computed: {
    formData: {
      get() {
        console.log('this.value', this.value)
        return {
          list: this.value,
        }
      },
      set(o) {
        this.$emit('input', o.list)
      },
    },
  },
  methods: {
    add() {
      this.formData.list.push({
        num: 10,
        time: 10,
      })
    },
    remove(index) {
      this.formData.list.splice(index, 1)
    },
  },
}
</script>
