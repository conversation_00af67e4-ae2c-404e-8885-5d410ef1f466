<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="request"
      :model-config="modelConfig"
      :features="[
        'insert',
        'delete',
        'batch_delete',
        'update',
        // 'select',
        'offline',
        'batch_offline',
        'online',
        'batch_online',
      ]"
      operate-width="320px"
    >
      <template #table_item_status="{row}">
        <tag-status :status="row.status" />
      </template>
      <template #table_item_needAudit="{row}">
        <tag-status
          active-text="需要"
          inactive-text="不需要"
          :status="row.needAudit"
        />
      </template>
    </page-table>
  </div>
</template>

<script>
import { guessTaskWithdrawalConfigRequest as request } from '@/api/videoQa'
import config from './config'

export default {
  data() {
    return {
      config,
      request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'videoTimes', title: '提现所需观看视频数', width: 130 },
          { field: 'timeInterval', title: '视频观看时间间隔', width: 130 },
          // { field: 'appCode', title: '应用' },
          { field: 'amount', title: '提现金额' },
          { field: 'sortValue', title: '排序值' },
          {
            field: 'needAudit',
            title: '是否需要审核',
            slots: {
              default: 'table_item_needAudit',
            },
          },
          { field: 'startEcpm', title: '开始ecpm' },
          { field: 'endEcpm', title: '结束ecpm' },
          { field: 'description', title: '提现说明' },
          { field: 'extra', title: '额外信息' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
          { field: 'updatedBy', title: '最新编辑者' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '150px',
        },
        formData: {
          id: null,
          videoTimes: '',
          timeInterval: '',
          appCode: config.appCode,
          amount: '',
          sortValue: '',
          needAudit: '',
          description: '',
          extra: '',
          status: '',
          startEcpm: null,
          endEcpm: null,
        },
        formItemMap: {
          videoTimes: {
            title: '提现所需观看视频数',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          timeInterval: {
            title: '视频观看时间间隔',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          appCode: {
            title: '应用',
            itemRender: {
              attrs: {
                disabled: true,
              },
            },
          },
          amount: {
            title: '提现金额',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          startEcpm: {
            title: '开始ecpm',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          endEcpm: {
            title: '结束ecpm',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          sortValue: {
            title: '排序值',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          needAudit: {
            title: '是否需要审核',
            itemRender: {
              name: 'radio-status',
              attrs: {
                activeText: '需要',
                inactiveText: '不需要',
              },
            },
          },
          description: {
            title: '提现说明',
            itemRender: {
              attrs: {
                type: 'textarea',
              },
            },
          },
          extra: {
            title: '额外信息',
            itemRender: {
              attrs: {
                type: 'textarea',
              },
            },
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
        },
        formRule: {
          videoTimes: [
            {
              required: true,
              message: '提现所需观看视频数不能为空',
              trigger: 'blur',
            },
          ],
          timeInterval: [
            {
              required: true,
              message: '视频观看时间间隔不能为空',
              trigger: 'blur',
            },
          ],
          appCode: [
            { required: true, message: '应用id不能为空', trigger: 'blur' },
          ],
          amount: [
            { required: true, message: '提现金额不能为空', trigger: 'blur' },
          ],
          sortValue: [
            { required: true, message: '排序值不能为空', trigger: 'blur' },
          ],
          needAudit: [
            {
              required: true,
              message: '是否需要审核不能为空',
              trigger: 'blur',
            },
          ],

          status: [
            {
              required: true,
              message: '状态不能为空',
              trigger: 'blur',
            },
          ],
          startEcpm: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          endEcpm: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
}
</script>
