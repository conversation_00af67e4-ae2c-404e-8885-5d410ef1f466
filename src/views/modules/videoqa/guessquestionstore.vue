<template>
  <div class="page-table-height">
    <vxe-grid v-bind="gridOptions" ref="vxe-grid">
      <template #toolbar_buttons>
        <vxe-button
          status="success"
          icon="el-icon-top"
          @click="handleBatchOnline"
          content="批量上线"
        />
        <vxe-button
          status="danger"
          icon="el-icon-bottom"
          @click="handleBatchOffline"
          content="批量下线"
        />
        <!--<vxe-button icon="el-icon-sort" content="降序排序" />-->
        <vxe-button icon="el-icon-plus" @click="add" content="新增题目" />
      </template>
      <template #table_item_operate="{row}">
        <vxe-button icon="el-icon-edit" @click="edit(row)">编辑</vxe-button>
        <vxe-button
          v-if="row.status === 0"
          status="success"
          icon="el-icon-top"
          :content="'上线'"
          @click="toggleStatus('online', [row.id], '确定上线')"
        />
        <vxe-button
          v-else
          status="danger"
          icon="el-icon-bottom"
          :content="'下线'"
          @click="toggleStatus('offline', [row.id], '确定下线')"
        />
      </template>
      <template #table_item_coverUrl="{ row }">
        <el-image
          style="width: 100px;"
          :src="row.coverUrl"
          :preview-src-list="[row.coverUrl]"
        />
      </template>
      <template #table_item_category="{ row }">
        <color-tag :id="row.category">
          {{ questionMap.categoryList.get(row.category) }}
        </color-tag>
      </template>
      <template #table_item_status="{ row }">
        <color-tag :id="row.status">
          {{ questionMap.statusList.get(row.status) }}
        </color-tag>
      </template>
    </vxe-grid>
    <QuestionModel
      :visible.sync="visible"
      :data-id="dataId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script>
import ColorTag from '@/components/color-tag'
import { questionStoreRequest } from '@/api/videoQa'
import { questionMap } from '@/map/videoQa'
import { mapToSelectData } from '@/utils/tools'
import QuestionModel from '@/views/modules/videoqa/components/QuestionModel'

export default {
  components: {
    ColorTag,
    QuestionModel,
  },
  data() {
    return {
      showTitle: true,
      visible: false,
      dataId: null,
      questionMap,
      gridOptions: {
        height: 'auto',
        exportConfig: {},
        pagerConfig: {},
        formConfig: {
          items: [
            {
              field: 'category',
              title: '分类',
              itemRender: {
                name: '$select',
                options: mapToSelectData(questionMap.categoryList),
                props: { placeholder: '请选择' },
                defaultValue: '',
              },
            },
            {
              field: 'status',
              title: '状态',
              itemRender: {
                name: '$select',
                options: [
                  { value: '', label: '全部' },
                  ...mapToSelectData(questionMap.statusList),
                ],
                props: { placeholder: '请选择' },
                defaultValue: '',
              },
            },
            {
              field: 'question',
              title: '题目',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
            {
              field: 'tags',
              title: '标签',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
            {
              align: 'center',
              itemRender: {
                name: '$buttons',
                children: [
                  {
                    props: {
                      type: 'submit',
                      content: '提交',
                      status: 'primary',
                      icon: 'vxe-icon--search',
                    },
                  },
                ],
              },
            },
          ],
        },
        toolbarConfig: {
          slots: {
            buttons: 'toolbar_buttons',
          },
        },
        proxyConfig: {
          form: true, // 启用表单代理
          ajax: {
            query: ({ page, form }) => {
              console.log('form ==> ', form)
              return questionStoreRequest.selectAll({
                currentPage: page.currentPage,
                pageSize: page.pageSize,
                ...form,
              })
            },
          },
        },
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'weights', title: '权重', width: 60 },
          {
            field: 'coverUrl',
            title: '封面',
            width: 120,
            slots: { default: 'table_item_coverUrl' },
          },
          { field: 'tags', title: '标签' },
          {
            field: 'category',
            title: '题目分类',
            width: 90,
            slots: { default: 'table_item_category' },
          },
          { field: 'question', title: '题目' },
          { field: 'answer1', title: '正确答案' },
          { field: 'answer2', title: '错误答案1' },
          { field: 'answer3', title: '错误答案2' },
          { field: 'answer4', title: '错误答案3' },
          { field: 'audioVideoUrl', title: '音视频链接地址', width: 430 },
          { field: 'id', title: '题目ID', width: 60 },
          {
            field: 'status',
            title: '状态',
            width: 70,
            slots: { default: 'table_item_status' },
          },
          {
            title: '操作',
            width: 165,
            slots: { default: 'table_item_operate' },
          },
        ],
      },
    }
  },
  methods: {
    add() {
      this.dataId = null
      this.visible = true
    },
    edit(row) {
      this.dataId = row.id
      this.visible = true
    },
    handleBatchOnline() {
      this.toggleStatus(
        'online',
        this.getCheckboxRecords().map(it => it.id),
        '确定批量上线?'
      )
    },
    handleBatchOffline() {
      this.toggleStatus(
        'offline',
        this.getCheckboxRecords().map(it => it.id),
        '确定批量下线?'
      )
    },
    toggleStatus(type, ids, confirmMsg = '确定批量上线?') {
      if (!ids) {
        return this.$vxeMessage({
          status: 'warning',
          message: '请选选择',
        })
      }

      this.$confirm(confirmMsg, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let request = null

        if (type === 'online') {
          request = questionStoreRequest.batchOnline
        } else if (type === 'offline') {
          request = questionStoreRequest.batchOffline
        } else {
          throw 'type 值不对'
        }

        request(ids).then(() => {
          this.$vxeMessage({
            status: 'success',
            message: '更新成功!',
          })
          this.reloadQuery()
        })
      })
    },
    handleRefresh() {
      this.reloadQuery()
    },
    reloadQuery() {
      this.$refs['vxe-grid'].commitProxy('query')
    },
    getCheckboxRecords() {
      return this.$refs['vxe-grid'].getCheckboxRecords()
    },
  },
}
</script>
