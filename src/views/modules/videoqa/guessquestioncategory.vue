<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="guessQuestionCategoryRequest"
    :model-config="modelConfig"
    :features="['insert', 'update']"
    operate-width="120"
  >
    <template #table_item_enabled="{row}">
      <tag-status :status="row.enabled" />
    </template>
    <template #model>
      <el-form-item label="分类名" prop="name">
        <el-input v-model="modelConfig.formData.name" placeholder="分类名" />
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-input v-model="modelConfig.formData.category" placeholder="分类" />
      </el-form-item>
      <el-form-item label="权重" prop="weight">
        <el-input v-model="modelConfig.formData.weight" placeholder="权重" />
      </el-form-item>
      <el-form-item label="状态" prop="enabled">
        <radio-status v-model="modelConfig.formData.enabled"></radio-status>
      </el-form-item>
    </template>
  </page-table>
</template>

<script>
import { guessQuestionCategoryRequest } from '@/api/videoQa'
import { questionMap } from '@/map/videoQa'

export default {
  data() {
    return {
      guessQuestionCategoryRequest,
      questionMap,
      gridOptions: {
        columns: [
          { field: 'id', width: 50, title: 'ID' },
          { field: 'name', title: '名字' },
          { field: 'weight', title: '权重' },
          {
            field: 'enabled',
            title: '状态',
            slots: {
              default: 'table_item_enabled',
            },
          },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: 0,
          category: '',
          weight: '',
          enabled: '',
          name: '',
        },
        formRule: {
          category: [
            { required: true, message: '分类不能为空', trigger: 'blur' },
          ],
          weight: [
            { required: true, message: '权重不能为空', trigger: 'blur' },
          ],
          enabled: [
            {
              required: true,
              message: '启用：1：启用，0：不启用不能为空',
              trigger: 'blur',
            },
          ],
          name: [
            { required: true, message: '分类名不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
}
</script>
