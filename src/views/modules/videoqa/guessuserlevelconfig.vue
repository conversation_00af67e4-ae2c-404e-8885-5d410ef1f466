<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="userLevelConfig"
      :model-config="modelConfig"
      :features="['insert', 'delete', 'batch_delete', 'update']"
    >
      <template #table_item_enabled="{row}">
        <color-tag :id="row.enabled">
          {{ userLevelConfigMap.enabledList.get(row.enabled) }}
        </color-tag>
      </template>

      <template #model>
        <el-form-item label="用户等级名称" prop="levelTitle">
          <el-input
            v-model="modelConfig.formData.levelTitle"
            placeholder="用户等级名称"
          />
        </el-form-item>
        <el-form-item label="升级需要答对题数" prop="lowLimit">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.lowLimit"
            placeholder="升级需要答对题数"
          />
        </el-form-item>
        <el-form-item label="累计答对题数" prop="highLimit">
          <el-input
            type="number"
            v-model.number="modelConfig.formData.highLimit"
            placeholder="累计答对题数"
          />
        </el-form-item>
        <el-form-item label="排序值" prop="sortValue">
          <el-input
            v-model="modelConfig.formData.sortValue"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <map-radio
            v-model="modelConfig.formData.enabled"
            :list="userLevelConfigMap.enabledList"
          />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import { userLevelConfigRequest } from '@/api/videoQa'
import { userLevelConfigMap } from '@/map/videoQa'

export default {
  data() {
    return {
      userLevelConfig: userLevelConfigRequest,
      userLevelConfigMap,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', width: 50, title: 'ID' },
          { field: 'levelTitle', title: '用户等级名称' },
          { field: 'lowLimit', title: '升级需要答对题数' },
          { field: 'highLimit', title: '累计答对题数' },
          { field: 'sortValue', title: '排序值' },
          {
            field: 'enabled',
            title: '是否启用',
            slots: { default: 'table_item_enabled' },
          },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
        formData: {
          id: 0,
          appId: '',
          levelTitle: '',
          lowLimit: '',
          highLimit: '',
          sortValue: '',
          enabled: '',
        },
        formRule: {
          appId: [
            { required: true, message: '应用id不能为空', trigger: 'blur' },
          ],
          levelTitle: [
            {
              required: true,
              message: '用户等级名称不能为空',
              trigger: 'blur',
            },
          ],
          lowLimit: [
            { required: true, message: '升级需要答对题数', trigger: 'blur' },
          ],
          highLimit: [
            { required: true, message: '累计答对题数', trigger: 'blur' },
          ],
          sortValue: [
            { required: true, message: '排序值不能为空', trigger: 'blur' },
          ],
          enabled: [
            {
              required: true,
              message: '是否启用：1：启用，2：不启用不能为空',
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
}
</script>
