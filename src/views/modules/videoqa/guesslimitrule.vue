<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="limitRule"
    :model-config="modelConfig"
    :features="['update']"
  >
    <template #table_item_lowValue="{row}">
      <span v-if="row.id === 10">
        {{
          row.extraConfig && row.extraConfig[0] && row.extraConfig[0].lowLimit
            ? row.extraConfig[0].lowLimit
            : '-'
        }}
      </span>
      <span v-else>{{ row.lowValue }}</span>
    </template>

    <template #table_item_highValue="{row}">
      <span v-if="row.id === 10">
        {{
          row.extraConfig && row.extraConfig[0] && row.extraConfig[0].highLimit
            ? row.extraConfig[0].highLimit
            : '-'
        }}
      </span>
      <span v-else>{{ row.highValue }}</span>
    </template>

    <template #table_item_clerk="{row}">
      <span>
        {{
          row.extraConfig && row.extraConfig[0] && row.extraConfig[0].clerk
            ? row.extraConfig[0].clerk
            : ''
        }}
      </span>
    </template>

    <template #table_item_status="{row}">
      <color-tag :id="row.status">
        {{ limitRuleMap.statusList.get(row.status) }}
      </color-tag>
    </template>

    <template #model>
      <limit-rule-input
        :type="modelConfig.formData.type"
        :high.sync="modelConfig.formData.highValue"
        :low.sync="modelConfig.formData.lowValue"
        :extra.sync="modelConfig.formData.extraConfig"
        :data-id="modelConfig.formData.id"
      />
      <!--<el-form-item label="启用状态" prop="status">-->
      <!--  <map-radio-->
      <!--    :list="limitRuleMap.statusList"-->
      <!--    v-model="modelConfig.formData.status"-->
      <!--  />-->
      <!--</el-form-item>-->
    </template>
  </page-table>
</template>

<script>
import { limitRuleRequest } from '@/api/videoQa'
import { limitRuleMap } from '@/map/videoQa'
import LimitRuleInput from '@/components/limit-rule-input'

export default {
  components: {
    LimitRuleInput,
  },
  data() {
    return {
      limitRule: limitRuleRequest,
      limitRuleMap,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', width: 50, title: 'ID' },
          {
            field: 'ruleType',
            width: 50,
            title: '类型',
          },
          { field: 'ruleName', title: '风控规则名称', width: 320 },
          {
            field: 'lowValue',
            title: '数值下限',
            slots: { default: 'table_item_lowValue' },
          },
          {
            field: 'highValue',
            title: '数值上限',
            slots: { default: 'table_item_highValue' },
          },
          {
            field: 'clerk',
            title: '文案',
            slots: { default: 'table_item_clerk' },
          },
          {
            field: 'enabled',
            title: '是否启用',
            slots: { default: 'table_item_status' },
          },
          { field: 'createdAt', title: '创建时间' },
          { field: 'updatedAt', title: '更新时间' },
          { field: 'createdBy', title: '创建者' },
          { field: 'updatedBy', title: '更新者' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: 0,
          appCode: '',
          ruleType: '',
          ruleName: '',
          lowValue: null,
          highValue: null,
          status: 1,
          extraConfig: null,
          type: 1, //类型:1:固定，2：区间，3：json
        },
        formRule: {
          ruleType: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          ruleName: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
          lowValue: [{ required: true, message: '不能为空', trigger: 'blur' }],
          highValue: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [
            {
              required: true,
              message: '不能为空',
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
}
</script>
