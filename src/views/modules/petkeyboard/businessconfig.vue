<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="businessConfigRequest"
      :model-config="modelConfig"
      :features="['insert', 'delete', 'update', 'select']"
    >
      <template #model>
        <el-form-item label="参数名" prop="configKey">
          <el-input
            v-model="modelConfig.formData.configKey"
            placeholder="参数名"
          />
        </el-form-item>
        <el-form-item label="参数值" prop="configValue">
          <el-input
            v-model="modelConfig.formData.configValue"
            placeholder="参数值"
          />
        </el-form-item>
        <el-form-item label="参数描述" prop="configDesc">
          <el-input
            type="textarea"
            v-model="modelConfig.formData.configDesc"
            placeholder="参数描述"
          />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import { businessConfigRequest } from '@/api/petkeyboard'

export default {
  name: 'businessconfig',
  data() {
    return {
      businessConfigRequest,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'configKey', title: '参数名' },
          { field: 'configValue', title: '参数值' },
          { field: 'configDesc', title: '参数描述' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          configKey: '',
          configValue: '',
          configDesc: '',
        },
        formRule: {
          configKey: [{ required: true, message: '不能为空', trigger: 'blur' }],
          configValue: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          configDesc: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
}
</script>

<style scoped></style>
