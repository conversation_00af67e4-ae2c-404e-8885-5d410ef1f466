<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="skinRequest"
      :model-config="modelConfig"
      :features="[
        'insert',
        'delete',
        'update',
        'batch_online',
        'batch_offline',
        'offline',
        'online',
      ]"
      :button-txt="{
        insert: '上传皮肤',
      }"
      operate-width="360"
    >
      <template #table_item_status="{row}">
        <tag-status :status="row.status" />
      </template>

      <template #model>
        <el-form-item label="皮肤名称" prop="name">
          <el-input v-model="modelConfig.formData.name" placeholder="排序值" />
        </el-form-item>
        <el-form-item label="排序值" prop="sortNum">
          <el-input
            v-model.number="modelConfig.formData.sortNum"
            type="number"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="皮肤图片" prop="previewUrl">
          <el-upload
            :auto-upload="false"
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="handleAvatarChange"
            :before-upload="beforeAvatarUpload"
          >
            <img
              v-if="modelConfig.formData.previewUrl"
              class="upload-preview"
              :src="modelConfig.formData.previewUrl"
              alt=""
            />
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item label="皮肤上传" prop="skinUrl">
          <el-upload
            :auto-upload="false"
            :show-file-list="false"
            action=""
            :on-change="handleSkinChange"
          >
            <div v-if="modelConfig.formData.skinUrl" class="upload-preview">
              <el-image :src="modelConfig.formData.skinUrl">
                <div slot="error" class="image-slot">
                  <el-link
                    style="margin-left: 5px;"
                    icon="el-icon-picture-outline"
                    type="primary"
                    :underline="false"
                  >
                    {{ modelConfig.formData.skinUrl }}
                  </el-link>
                </div>
              </el-image>
            </div>
            <el-button v-else icon="el-icon-upload">文件上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <radio-status v-model="modelConfig.formData.status" />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import config from './config'
import { skinRequest } from '@/api/petkeyboard'

export default {
  data() {
    return {
      config,
      skinRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'name', title: '皮肤名称' },
          { field: 'sortNum', title: '排序值' },
          {
            field: 'status',
            title: '上/下架',
            slots: { default: 'table_item_status' },
          },
        ],
      },
      modelConfig: {
        // autoCommit: false,
        // autoUpdate: false,
        adapterFormData(data) {
          const formData = new FormData()

          for (const key in data) {
            // eslint-disable-next-line no-prototype-builtins
            if (data.hasOwnProperty(key)) {
              if (
                (key === 'previewFile' && !data[key]) ||
                (key === 'skinFile' && !data[key]) ||
                (key === 'id' && !data[key])
              ) {
                continue
              }
              formData.append(key, data[key])
            }
          }

          return formData
        },
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          // appId: config.appCode, // 它其实是一个 code
          name: '', // 皮肤名称
          previewUrl: '',
          skinUrl: '',
          sortNum: '',
          status: 0, // 上下架
          previewFile: '', // 预览图片文件
          skinFile: '', // 皮肤文件
        },
        formRule: {
          name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          previewUrl: [
            { required: true, message: '预览图不能为空', trigger: 'blur' },
          ],
          skinUrl: [
            { required: true, message: '皮肤包不能为空', trigger: 'blur' },
          ],
          sortNum: [
            { required: true, message: '排序不能为空', trigger: 'blur' },
          ],
          status: [
            { required: true, message: '状态不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
  methods: {
    handleAvatarChange(file) {
      this.modelConfig.formData.previewUrl = URL.createObjectURL(file.raw)
      this.modelConfig.formData.previewFile = file.raw
    },
    handleSkinChange(file) {
      const fileType = file.name
        .substr(file.name.lastIndexOf('.') + 1)
        .toLocaleLowerCase()
      this.modelConfig.formData.skinUrl =
        fileType === 'png' ||
        fileType === 'jpg' ||
        fileType === 'jpeg' ||
        fileType === 'bmp'
          ? URL.createObjectURL(file.raw)
          : file.name
      this.modelConfig.formData.skinFile = file.raw
    },
    beforeAvatarUpload(file) {
      const isImg =
        file.type === 'image/jpeg' ||
        file.type === 'image/jpg' ||
        file.type === 'image/png'

      if (!isImg) {
        this.$message.error('只能是 JPG JPG PNG 格式!')
      }

      return isImg
    },
  },
}
</script>

<style lang="scss" scoped>
.avatar-uploader {
  ::v-deep {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
  }
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.upload-preview {
  max-width: 100%;
  display: block;
}
</style>
