<template>
  <div>
    <page-table
      :request-collection="userRequest"
      :model-config="modelConfig"
      :grid-config="gridOptions"
      :features="['select']"
      :show-operate="false"
    >
      <template #table_item_status="{row}">
        <color-tag :id="row.status">
          {{ userMap.status.get(row.status) }}
        </color-tag>
      </template>
    </page-table>
  </div>
</template>

<script>
import { userRequest } from '@/api/petkeyboard'
import { userMap } from '@/map/petkeyboard'

export default {
  name: 'user',
  data() {
    return {
      userRequest,
      userMap,
      gridOptions: {
        columns: [
          { type: 'seq', title: '序号', width: 60 },
          { field: 'id', title: 'id', width: 60 },
          { field: 'uid', title: '设备id' },
          { field: 'wxOpenId', title: '微信 openid' },
          { field: 'qqOpenId', title: 'qq openid' },
          {
            field: 'status',
            title: '账户状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '注册时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
      },
    }
  },
}
</script>

<style scoped></style>
