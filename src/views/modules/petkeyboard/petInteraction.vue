<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="petInteractionRequest"
      :model-config="modelConfig"
      :features="[
        'insert',
        'delete',
        'update',
        'batch_online',
        'batch_offline',
        'offline',
        'online',
      ]"
      operate-width="340"
    >
      <template #table_item_status="{row}">
        <tag-status :status="row.status" />
      </template>

      <template #model>
        <el-form-item label="词句大类" prop="type">
          <map-radio
            :list="petInteractionMap.wordsType"
            v-model.number="modelConfig.formData.type"
          />
        </el-form-item>
        <el-form-item label="时间间隔" prop="intervalTime">
          <el-input v-model.number="modelConfig.formData.intervalTime" type="number">
            <template #append>分钟</template>
          </el-input>
        </el-form-item>
        <el-form-item label="大类排序" prop="sortNum">
          <el-input
            v-model.number="modelConfig.formData.sortNum"
            type="number"
          />
        </el-form-item>
        <el-form-item label="日期分类">
          <map-radio :list="petInteractionMap.dateType" v-model="dateType" />
        </el-form-item>
        <el-form-item label="时间选择">
          <pet-interaction-time-select
            ref="et-interaction-time-select"
            v-model="modelConfig.formData.configList"
            :show-date-range="dateType === 2"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <radio-status v-model="modelConfig.formData.status" />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import { rewardAdditionConfigMap } from '@/map/videoQa'
import config from './config'
import { petInteractionMap } from '@/map/petkeyboard'
import { petInteractionRequest } from '@/api/petkeyboard'
import PetInteractionTimeSelect from '@/components/pet-interaction-time-select'

export default {
  components: {
    PetInteractionTimeSelect,
  },
  data() {
    return {
      config,
      dateType: 1,
      checkList: [],
      errorList: [],
      petInteractionRequest,
      petInteractionMap,
      rewardAdditionConfigMap,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', width: 50, title: 'ID' },
          { field: 'type', title: '语句大类' },
          { field: 'intervalTime', title: '时间间隔' },
          { field: 'sortNum', title: '大类排序' },
          {
            field: 'status',
            title: '状态',
            slots: { default: 'table_item_status' },
          },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '630px',
        },
        formConfig: {
          labelWidth: '80px',
          beforeSubmit: () => {
            return this.$refs['et-interaction-time-select'].validate()
          },
        },
        adapterFormData: data => {
          const configList = data.configList.map(it => {
            const effectiveDate =
              this.dateType === 2 ? JSON.stringify(it.effectiveDate) : ''
            const timeConfig = JSON.stringify(it.timeConfig)
            // 键盘语句
            const keyboardContentList = it.contentList.map(it => it[0])
            // app语句
            const appContentList = it.contentList.map(it => it[1])

            return {
              times: it.times,
              timeConfig,
              effectiveDate,
              keyboardContentList,
              appContentList,
              defaultContent: it.defaultContent,
            }
          })
          return {
            ...data,
            configList,
          }
        },
        adapterResData: data => {
          const configList = data.configList.map(it => {
            let timeConfig
            let effectiveDate

            try {
              timeConfig = JSON.parse(it.timeConfig)
            } catch (e) {
              timeConfig = []
            }

            try {
              effectiveDate = JSON.parse(it.effectiveDate)
            } catch (e) {
              effectiveDate = []
            }

            const contentList = []
            it.keyboardContentList.forEach((value, index) => {
              contentList.push([value, it.appContentList[index]])
            })

            console.log('contentList ---', contentList)

            return {
              effectiveDate,
              defaultContent: it.defaultContent,
              timeConfig,
              times: it.times,
              contentList,
            }
          })

          const res = configList.some(
            it => it.effectiveDate && it.effectiveDate.length
          )
          this.dateType = res ? 2 : 1

          return {
            ...data,
            configList,
          }
        },
        formData: {
          id: null,
          type: '天气',
          intervalTime: '',
          sortNum: '',
          status: 0,
          configList: [
            {
              effectiveDate: '',
              defaultContent: '',
              timeConfig: [],
              times: 0,
              contentList: [['请输入键盘语句', '请输入APP语句']],
            },
          ],
        },
        formRule: {
          type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
          intervalTime: [
            {
              required: true,
              message: '间隔时间，单位分钟不能为空',
              trigger: 'blur',
            },
          ],
          sortNum: [
            { required: true, message: '排序不能为空', trigger: 'blur' },
          ],
          status: [
            { required: true, message: '状态不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
}
</script>
