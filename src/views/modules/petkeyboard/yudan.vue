<template>
  <div>
    <page-table
      ref="page-table"
      :grid-config="gridOptions"
      :request-collection="yudanRequest"
      :model-config="modelConfig"
      :features="[
        'insert',
        'update',
        'batch_online',
        'batch_offline',
        'offline',
        'online',
      ]"
      operate-width="260"
    >
      <template #table_item_status="{row}">
        <tag-status :status="row.status" />
      </template>

      <template #custom_toolbar>
        <vxe-button icon="el-icon-upload" :loading="isUploading">
          <el-upload
            style="display: inline-block"
            :auto-upload="false"
            action=""
            :show-file-list="false"
            :on-change="batchUpload"
          >
            批量上传
          </el-upload>
        </vxe-button>
        <vxe-button icon="el-icon-download">
          <a
            :href="require('@/assets/file/yuDanTemp.xlsx')"
            download="批量上传模板"
            style="color: #333"
          >
            下载批量上传模板
          </a>
        </vxe-button>
      </template>

      <template #model>
        <el-form-item label="语弹标题" prop="sortNum">
          <el-input
            v-model="modelConfig.formData.title"
            placeholder="语弹标题"
          />
        </el-form-item>
        <el-form-item label="语弹类别" prop="classification">
          <el-select v-model="modelConfig.formData.classification">
            <el-option
              v-for="(item, index) in typeList"
              :key="index"
              :value="item"
              :label="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="modelConfig.formData.sortNum"
            placeholder="排序"
            type="number"
          />
        </el-form-item>
        <el-form-item label="语弹内容" prop="contentList">
          <add-input v-model="modelConfig.formData.contentList" />
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <radio-status v-model="modelConfig.formData.status" />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import config from './config'
import AddInput from '@/components/add-input'
import { yudanRequest, yuDanClassificationRequest } from '@/api/petkeyboard'
import { yuDanMap } from '@/map/petkeyboard'

export default {
  components: {
    AddInput,
  },
  data() {
    return {
      config,
      isUploading: false,
      yuDanMap,
      yudanRequest,
      typeList: [],
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'classification', title: '类别' },
          { field: 'title', title: '标题' },
          { field: 'sortNum', title: '排序' },
          {
            field: 'status',
            title: '上/下架',
            slots: { default: 'table_item_status' },
          },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '90px',
        },
        formData: {
          id: null,
          title: '',
          contentList: [],
          classification: '',
          sortNum: '',
          status: 0,
        },
        formRule: {
          sortNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
          contentList: [
            { required: true, message: '不能为空', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (!value.length) {
                  return callback(new Error('错误'))
                }
                if (value.some(it => !it)) {
                  return callback(new Error('错误'))
                }
                callback()
              },
            },
          ],
          classification: [
            { required: true, message: '类别不能为空', trigger: 'blur' },
          ],
          title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
          status: [
            { required: true, message: '状态不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
  activated() {
    yuDanClassificationRequest
      .selectAll({
        pageSize: 10000,
        currentPage: 1,
      })
      .then(res => {
        if (res.page && res.page.list) {
          this.typeList = res.page.list.map(it => it.classification)
        } else {
          this.typeList = []
        }
      })
      .catch(() => {
        this.typeList = []
      })
  },
  methods: {
    batchUpload(file) {
      const formData = new FormData()
      formData.append('file', file.raw)
      this.isUploading = true
      yudanRequest.batchUpload(formData).finally(() => {
        this.isUploading = false
        this.$refs['page-table'].reloadQuery()
      })
    },
  },
}
</script>
