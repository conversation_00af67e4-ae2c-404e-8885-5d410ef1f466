<template>
  <div>
    <page-table
      ref="page-table"
      :grid-config="gridOptions"
      :request-collection="yuDanClassificationRequest"
      :model-config="modelConfig"
      :features="[
        'insert',
        'update',
        'batch_online',
        'batch_offline',
        'offline',
        'online',
      ]"
      operate-width="256"
    >
      <template #table_item_status="{row}">
        <tag-status :status="row.status" />
      </template>

      <template #model>
        <el-form-item label="语弹类别" prop="classification">
          <el-input
            v-model="modelConfig.formData.classification"
            placeholder="语弹类别"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            type="number"
            :min="0"
            v-model="modelConfig.formData.sortNum"
            placeholder="排序"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <radio-status v-model="modelConfig.formData.status" />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import { yuDanClassificationRequest } from '@/api/petkeyboard'

export default {
  name: 'classification',
  data() {
    return {
      yuDanClassificationRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'classification', title: '类别' },
          { field: 'sortNum', title: '排序' },
          {
            field: 'status',
            title: '上/下架',
            slots: { default: 'table_item_status' },
          },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '90px',
        },
        formData: {
          id: null,
          classification: '',
          sortNum: '',
          status: '',
        },
        formRule: {
          classification: [
            { required: true, message: '类别不能为空', trigger: 'blur' },
          ],
          sortNum: [
            { required: true, message: '排序不能为空', trigger: 'blur' },
          ],
          status: [
            { required: true, message: '状态不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
}
</script>

<style scoped></style>
