<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    :before-select="beforeSelect"
  >
    <template #form_userId>
      <el-input v-model="selectFormData.userId" placeholder="用户ID" clearable />
    </template>
    <template #form_orderNo>
      <el-input v-model="selectFormData.orderNo" placeholder="订单号"clearable  />
    </template>
    <template #form_day>
      <el-date-picker
        v-model="selectFormData.day"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyyMMdd"
      />
    </template>
    <template #form_handleStatus>
      <map-select v-model='selectFormData.handleStatus' :list='ocrMap.handleStatus' clearable />
    </template>
    <template #form_payStatus>
      <map-select v-model='selectFormData.status' :list='ocrMap.payStatus' clearable  />
    </template>
    <template #table_item_appCode="{row}">
      <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
    </template>
    <template #table_item_status='{row}'>
      <color-tag :id='row.status'>{{ocrMap.payStatus.get(row.status)}}</color-tag>
    </template>
    <template #table_item_handleStatus="{row}">
      <color-tag :id="row.handleStatus">{{ ocrMap.handleStatus.get(row.handleStatus) }}</color-tag>
    </template>
    <template #table_item_payType="{row}">
      <color-tag :id="row.payType">{{ ocrMap.payMethods.get(row.payType) }}</color-tag>
    </template>
    <template #table_item_brand="{row}">
      <color-tag :id="row.brand">{{row.brand}}</color-tag>
    </template>
    <template #table_item_day="{row}">
      <span>{{ row.day | formatDate }}</span>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>

    <template #custom_table_operate='{row}'>
      <vxe-button status="danger" content='退费' @click='tradeRefund(row.id)'/>
      <vxe-button content='撤销权益'  @click='cancelOrder(row.id)'/>
    </template>
  </page-table>
</template>

<script>
import { ocrOrderRequest as request } from '@/api/quickApp'
import { ocrMap } from '@/map/quickApp'

export default {
  computed: {
    ocrMap() {
      return ocrMap
    }
  },
  data() {
    return {
      fishList: [],
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          { field: 'id', title: 'id', minWidth: 60 },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 120,
            slots: {
              default: 'table_item_appCode',
            },
          },

          { field: 'orderNo', title: '订单号', minWidth: 150 },
          { field: 'userId', title: '用户ID', minWidth: 60 },
          { field: 'vipConfigId', title: 'VIP配置ID', minWidth: 100 },
          { field: 'payType', title: '支付方式', minWidth: 100,
            slots: {
              default: 'table_item_payType'
            }
          },
          { field: 'payAmount', title: '支付金额', minWidth: 100 },
          { field: 'payTime', title: '支付时间', minWidth: 150 },
          { field: 'appVersion', title: 'APP版本', minWidth: 100 },
          { field: 'brand', title: '品牌', minWidth: 150,
            slots: {
              default: "table_item_brand"
            }
          },
          { field: 'handleStatus', title: '待处理状态', minWidth: 150,
            slots: {
            default: "table_item_handleStatus"
            }
          },
          { field: 'status', title: '状态', minWidth: 150,
            slots: {
              default: 'table_item_status'
            }
          },

          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          { field: 'updatedAt', title: '更新时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              title: '用户ID',
              slots: {
                default: 'form_userId',
              },
            },
            {
              title: '订单号',
              slots: {
                default: 'form_orderNo',
              },
            },
            {
              title: '日期',
              slots: {
                default: 'form_day',
              },
            },
            {
              title: '待处理状态',
              slots: {
                default: 'form_handleStatus',
              },
            },
            {
              title: '状态',
              slots: {
                default: 'form_payStatus',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
      },
      selectFormData: {
        userId: null,
        orderNo: null,
        status: null,
        handleStatus: null,
        day: null,
      },
      beforeSelect: () => {
        const startDate = this.selectFormData.day ? this.selectFormData.day[0] : null
        const endDate = this.selectFormData.day ? this.selectFormData.day[1] : null
        return {
          userId: this.selectFormData.userId,
          orderNo: this.selectFormData.orderNo,
          status: this.selectFormData.status,
          handleStatus: this.selectFormData.handleStatus,
          startDate,
          endDate,
        }
      },
    }
  },
  methods: {
    // 退费
    tradeRefund(id) {
      this.$confirm(`确定退费?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        request.tradeRefund(id).then(() => {
          this.$message.success("退费成功")
        }).catch(e => {
          this.$message.error(e.message || "退费失败")
        })
      })
    },
    // 撤销权益
    cancelOrder(id) {
      this.$confirm(`确定撤销权益?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        request.cancelOrder(id).then(() => {
          this.$message.success("成功")
        }).catch(e => {
          this.$message.error(e.message || "失败")
        })
      })
    },
  }
}
</script>
