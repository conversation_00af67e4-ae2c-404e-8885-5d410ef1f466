<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    operate-width="180"
    :show-operate="false"
  >
    <template #table_item_appCode="{row}">
      <color-tag v-if="row.appCode" :id="row.appCode">
        {{ row.appCode | getAppName }}
      </color-tag>
    </template>
    <template #table_item_vipType="{row}">
      <color-tag :id="row.vipType">
        {{ ocrMap.userStatus.get(row.vipType) }}
      </color-tag>
    </template>
    <template #table_item_avatar="{row}">
      <img width="80" :src="row.avatar" alt="" />
    </template>
    <template #table_item_wechatAvatar="{row}">
      <img width="80" :src="row.wechatAvatar" alt="" />
    </template>
  </page-table>
</template>

<script>
import { ocrUserRequest as request } from '@/api/quickApp'
import { ocrMap } from '@/map/quickApp'

export default {
  computed: {
    ocrMap() {
      return ocrMap
    },
  },
  data() {
    return {
      fishList: [],
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          { field: 'id', title: '用户ID', minWidth: 60 },
          { field: 'uid', title: '设备ID', minWidth: 120 },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 120,
            slots: {
              default: 'table_item_appCode',
            },
          },
          { field: 'nickName', title: '昵称', minWidth: 60 },
          {
            field: 'avatar',
            title: '头像',
            minWidth: 60,
            slots: {
              default: 'table_item_avatar',
            },
          },
          { field: 'openId', title: '微信openid', minWidth: 90 },
          { field: 'wechatNickName', title: '微信昵称', minWidth: 60 },
          {
            field: 'wechatAvatar',
            title: '微信头像',
            minWidth: 60,
            slots: {
              default: 'table_item_wechatAvatar',
            },
          },
          { field: 'loginTime', title: '登录时间', minWidth: 120 },
          { field: 'loginDays', title: '登录天数', minWidth: 120 },
          { field: 'createdAt', title: '注册时间', minWidth: 120 },
          {
            field: 'vipType',
            title: '会员类型',
            minWidth: 60,
            slots: {
              default: 'table_item_vipType',
            },
          },
          { field: 'vipExpireTime', title: '会员有效期', minWidth: 80 },
          { field: 'appVersion', title: '当前版本', minWidth: 60 },
          { field: 'ocrNum', title: '扫描总次数', minWidth: 80 },
          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          // { field: 'updatedAt', title: '更新时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            {
              field: 'userId',
              title: '用户ID',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
      },
      // selectFormData: {
      //   appId: null,
      //   day: null,
      //   country: null,
      // },
      // beforeSelect: () => {
      //   return {
      //     ...this.selectFormData,
      //   }
      // },
    }
  },
}
</script>
