<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="requestCollection"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
    ]"
    operate-width="330"
  >
    <template #table_item_app="{row}">
      <color-tag :id="row.appId">
        {{ row.appId | getAppName }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_appVersion="{row}">
      <template v-if="row.appVersion">
        <el-popover
          placement="top-start"
          title="全部版本"
          width="200"
          trigger="hover"
        >
          <div>
            {{ row.appVersion }}
          </div>
          <div slot="reference">
            <template v-for="(item, index) in row.appVersion">
              <color-tag
                v-if="index < 4"
                :key="index"
                :id="index"
                style="margin-right: 5px;"
              >
                {{ item }}
              </color-tag>
            </template>
          </div>
        </el-popover>
      </template>
    </template>

    <template #table_item_appBrand="{row}">
      <template v-if="row.appBrand">
        <el-popover
          placement="top-start"
          title="全部品牌"
          width="200"
          trigger="hover"
        >
          <div>
            {{ row.appBrand }}
          </div>
          <div slot="reference">
            <template v-for="(item, index) in row.appBrand">
              <color-tag
                v-if="index < 4"
                :key="index"
                :id="index"
                style="margin-right: 5px;"
              >
                {{ item }}
              </color-tag>
            </template>
          </div>
        </el-popover>
      </template>
    </template>
    <template #model>
      <el-form-item v-if="!modelConfig.formData.id" label="应用" prop="appId">
        <app-select-component
          v-model.trim="modelConfig.formData.appId"
          placeholder="应用"
          :isShowAll="false"
          @user-change="changeApp"
        />
      </el-form-item>
      <el-form-item
        v-if="modelConfig.formData.appId"
        label="版本"
        prop="appVersion"
      >
        <app-version-select
          v-model="modelConfig.formData.appVersion"
          :app-id="modelConfig.formData.appId | getAppId"
          select-key="versionName"
        />
      </el-form-item>
      <el-form-item
        v-if="modelConfig.formData.appId"
        label="品牌"
        prop="appBrand"
      >
        <brand-select
          v-model="modelConfig.formData.appBrand"
          :app-code="modelConfig.formData.appId"
        />
      </el-form-item>
      <el-form-item label="参数" prop="configCode">
        <el-input v-model.trim="modelConfig.formData.configCode" />
      </el-form-item>
      <el-form-item label="参数值" prop="configValue">
        <el-input v-model.trim="modelConfig.formData.configValue" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <radio-status v-model.trim="modelConfig.formData.status" />
      </el-form-item>
    </template>
  </page-table>
</template>

<script>
import { businessConfigVersionChannelRequest } from '@/api/quickApp'
import { channelNameList, channelNameListMap } from '@/map/common'
import BrandSelect from '@/components/brand-select'

export default {
  components: {
    BrandSelect,
  },
  data() {
    return {
      channelNameListMap,
      channelNameList,
      requestCollection: businessConfigVersionChannelRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          {
            field: 'appId',
            title: '应用',
            slots: {
              default: 'table_item_app',
            },
          },
          { field: 'configCode', title: '参数' },
          { field: 'configValue', title: '参数值' },
          {
            field: 'appVersion',
            title: '版本',
            slots: {
              default: 'table_item_appVersion',
            },
          },
          {
            field: 'appBrand',
            title: '品牌',
            slots: {
              default: 'table_item_appBrand',
            },
          },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          appId: null,
          configCode: '',
          configValue: '',
          appVersion: [],
          appBrand: [],
          status: 0,
        },
        formRule: {
          configCode: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          configValue: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          appVersion: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          appBrand: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  methods: {
    changeApp() {
      this.modelConfig.formData.appVersion = []
      this.modelConfig.formData.appBrand = []
    },
  },
}
</script>
