<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="requestCollection"
    :model-config="modelConfig"
    :features="[
      'batch_online',
      'batch_offline',
      'online',
      'offline',
      'delete',
      'update',
    ]"
    operate-width="330"
  >
    <template #table_item_recommendPosition="{row}">
      <color-tag :id="row.recommendPosition">
        {{
          comicRecommendPositionMap.recommendPosition.get(row.recommendPosition)
        }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_bookId="{row}">
      {{ findNovelName(row) }}
    </template>
  </page-table>
</template>

<script>
import {
  comicBookInfoRequest,
  comicRecommendPositionRequest,
} from '@/api/quickApp'
import { comicRecommendPositionMap } from '@/map/quickApp'

export default {
  data() {
    return {
      novelBookList: [],
      comicRecommendPositionMap,
      requestCollection: comicRecommendPositionRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          {
            field: 'recommendPosition',
            title: '推荐位置',
            slots: {
              default: 'table_item_recommendPosition',
            },
          },
          {
            field: 'bookId',
            title: '漫画',
            slots: {
              default: 'table_item_bookId',
            },
          },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'sortNum', title: '排序' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          recommendPosition: '',
          bookId: '',
          sortNum: '',
        },
        formItemMap: {
          recommendPosition: {
            title: '推荐位置',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: comicRecommendPositionMap.recommendPosition,
              },
            },
          },
          bookId: {
            title: '漫画',
            itemRender: {
              name: 'arr-select',
              attrs: {
                list: [],
                valueKey: 'id',
                labelKey: 'name',
                filterable: true,
                style: {
                  width: '100%',
                },
              },
            },
          },
          sortNum: {
            title: '排序',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
        },
        formRule: {
          categoryId: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          bookId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          sortNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  activated() {
    this.getNovelList()
  },
  methods: {
    getNovelList() {
      comicBookInfoRequest
        .selectAll({
          currentPage: 1,
          pageSize: 1000,
        })
        .then(res => {
          if (res.code === 0 && res.page && res.page.list) {
            this.novelBookList = res.page.list
            this.modelConfig.formItemMap.bookId.itemRender.attrs.list =
              res.page.list
          }
        })
    },
    findNovelName(row) {
      const res = this.novelBookList.find(it => it.id === row.bookId)
      return res && res.name ? res.name : '-'
    },
  },
}
</script>
