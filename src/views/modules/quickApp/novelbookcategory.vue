<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="novelBookCategoryRequest"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
    ]"
    operate-width="330"
  >
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
  </page-table>
</template>

<script>
import { novelBookCategoryRequest } from '@/api/quickApp'

export default {
  data() {
    return {
      novelBookCategoryRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'category', title: '分类' },
          { field: 'sortNum', title: '排序' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          category: '',
          sortNum: '',
          status: '',
        },
        formItemMap: {
          category: {
            title: '分类',
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
          sortNum: {
            title: '排序',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
        },
        formRule: {
          category: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
          sortNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
}
</script>
