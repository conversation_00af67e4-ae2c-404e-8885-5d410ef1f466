<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="novelBookCategoryItemRequest"
    :model-config="modelConfig"
    :features="['delete', 'insert', 'update']"
  >
    <template #table_item_category="{row}">
      <color-tag :id="row.categoryId">
        {{ row.category }}
      </color-tag>
    </template>

    <template #table_item_bookId="{row}">
      {{ findNovelName(row) }}
    </template>
  </page-table>
</template>

<script>
import {
  novelBookCategoryItemRequest,
  novelBookCategoryRequest,
  novelBookInfoRequest,
} from '@/api/quickApp'

export default {
  data() {
    return {
      novelBookList: [],
      novelBookCategoryItemRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          // { field: 'categoryId', title: '分类ID' },
          {
            field: 'category',
            title: '分类',
            slots: {
              default: 'table_item_category',
            },
          },
          {
            field: 'bookId',
            title: '小说',
            slots: {
              default: 'table_item_bookId',
            },
          },
          { field: 'sortNum', title: '排序' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          categoryId: '',
          bookId: '',
          sortNum: '',
        },
        formItemMap: {
          categoryId: {
            title: '分类',
            itemRender: {
              name: 'arr-select',
              attrs: {
                list: [],
                valueKey: 'id',
                labelKey: 'category',
              },
            },
          },
          bookId: {
            title: '小说',
            itemRender: {
              name: 'arr-select',
              attrs: {
                list: [],
                valueKey: 'id',
                labelKey: 'name',
                filterable: true,
                style: {
                  width: '100%',
                },
              },
            },
          },
          sortNum: {
            title: '排序',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
        },
        formRule: {
          categoryId: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          bookId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          sortNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  activated() {
    this.getCategoryList()
    this.getNovelList()
  },
  methods: {
    getCategoryList() {
      novelBookCategoryRequest
        .selectAll({
          currentPage: 1,
          pageSize: 1000,
        })
        .then(res => {
          if (res.code === 0 && res.page && res.page.list) {
            this.modelConfig.formItemMap.categoryId.itemRender.attrs.list =
              res.page.list
          }
        })
    },
    getNovelList() {
      novelBookInfoRequest
        .selectAll({
          currentPage: 1,
          pageSize: 1000,
        })
        .then(res => {
          if (res.code === 0 && res.page && res.page.list) {
            this.novelBookList = res.page.list
            this.modelConfig.formItemMap.bookId.itemRender.attrs.list =
              res.page.list
          }
        })
    },
    findNovelName(row) {
      const res = this.novelBookList.find(it => it.id === row.bookId)
      return res && res.name ? res.name : '-'
    },
  },
}
</script>
