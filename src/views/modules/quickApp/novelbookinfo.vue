<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="requestCollection"
    :features="['batch_offline', 'batch_online', 'offline', 'online']"
  >
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_cover="{row}">
      <el-image :src="row.cover" />
    </template>
    <template #table_item_source="{row}">
      <color-tag :id="row.source">
        <span>{{ novelBookInfoMap.resource.get(row.source) }}</span>
      </color-tag>
    </template>
    <template #table_item_introduction="{row}">
      <el-popover
        placement="top-start"
        title="简介"
        width="200"
        trigger="hover"
        :content="row.introduction"
      >
        <div
          style="width: 100%;white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
          slot="reference"
        >
          {{ row.introduction }}
        </div>
      </el-popover>
    </template>
  </page-table>
</template>

<script>
import { novelBookInfoRequest } from '@/api/quickApp'
import { novelBookInfoMap } from '@/map/quickApp'

export default {
  data() {
    return {
      novelBookInfoMap,
      requestCollection: novelBookInfoRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'name', title: '名称' },
          { field: 'author', title: '作者' },
          {
            field: 'introduction',
            title: '简介',
            width: 150,
            slots: {
              default: 'table_item_introduction',
            },
          },
          {
            field: 'cover',
            title: '封面',
            slots: {
              default: 'table_item_cover',
            },
          },
          { field: 'category', title: '分类' },
          { field: 'tags', title: '标签' },
          { field: 'words', title: '总字数' },
          { field: 'chapterCount', title: '章节数' },
          {
            field: 'source',
            title: '来源',
            slots: {
              default: 'table_item_source',
            },
          },
          { field: 'sourceId', title: '来源ID' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
    }
  },
}
</script>
