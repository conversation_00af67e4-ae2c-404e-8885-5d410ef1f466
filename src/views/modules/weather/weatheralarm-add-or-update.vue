<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="位置ID" prop="locationId">
      <el-input v-model="dataForm.locationId" placeholder="位置ID"></el-input>
    </el-form-item>
    <el-form-item label="预警唯一ID，可用于去重" prop="alarmId">
      <el-input v-model="dataForm.alarmId" placeholder="预警唯一ID，可用于去重"></el-input>
    </el-form-item>
    <el-form-item label="气象灾害预警信息" prop="alarm">
      <el-input v-model="dataForm.alarm" placeholder="气象灾害预警信息"></el-input>
    </el-form-item>
    <el-form-item label="通知：1已 0未" prop="notified">
      <el-input v-model="dataForm.notified" placeholder="通知：1已 0未"></el-input>
    </el-form-item>
    <el-form-item label="数据日期" prop="dateAt">
      <el-input v-model="dataForm.dateAt" placeholder="数据日期"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          locationId: '',
          alarmId: '',
          alarm: '',
          notified: '',
          dateAt: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          locationId: [
            { required: true, message: '位置ID不能为空', trigger: 'blur' }
          ],
          alarmId: [
            { required: true, message: '预警唯一ID，可用于去重不能为空', trigger: 'blur' }
          ],
          alarm: [
            { required: true, message: '气象灾害预警信息不能为空', trigger: 'blur' }
          ],
          notified: [
            { required: true, message: '通知：1已 0未不能为空', trigger: 'blur' }
          ],
          dateAt: [
            { required: true, message: '数据日期不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/weather/weatheralarm/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.locationId = data.weatherAlarm.locationId
                this.dataForm.alarmId = data.weatherAlarm.alarmId
                this.dataForm.alarm = data.weatherAlarm.alarm
                this.dataForm.notified = data.weatherAlarm.notified
                this.dataForm.dateAt = data.weatherAlarm.dateAt
                this.dataForm.createdAt = data.weatherAlarm.createdAt
                this.dataForm.updatedAt = data.weatherAlarm.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/weather/weatheralarm/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'locationId': this.dataForm.locationId,
                'alarmId': this.dataForm.alarmId,
                'alarm': this.dataForm.alarm,
                'notified': this.dataForm.notified,
                'dateAt': this.dataForm.dateAt,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
