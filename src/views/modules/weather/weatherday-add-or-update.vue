<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="位置ID" prop="locationId">
      <el-input v-model="dataForm.locationId" placeholder="位置ID"></el-input>
    </el-form-item>
    <el-form-item label="天气预报" prop="weather">
      <el-input v-model="dataForm.weather" placeholder="天气预报"></el-input>
    </el-form-item>
    <el-form-item label="空气预报" prop="air">
      <el-input v-model="dataForm.air" placeholder="空气预报"></el-input>
    </el-form-item>
    <el-form-item label="日出日落" prop="sunRiseset">
      <el-input v-model="dataForm.sunRiseset" placeholder="日出日落"></el-input>
    </el-form-item>
    <el-form-item label="生活指数" prop="lifeSuggest">
      <el-input v-model="dataForm.lifeSuggest" placeholder="生活指数"></el-input>
    </el-form-item>
    <el-form-item label="数据日期" prop="dateAt">
      <el-input v-model="dataForm.dateAt" placeholder="数据日期"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          locationId: '',
          weather: '',
          air: '',
          sunRiseset: '',
          lifeSuggest: '',
          dateAt: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          locationId: [
            { required: true, message: '位置ID不能为空', trigger: 'blur' }
          ],
          weather: [
            { required: true, message: '天气预报不能为空', trigger: 'blur' }
          ],
          air: [
            { required: true, message: '空气预报不能为空', trigger: 'blur' }
          ],
          sunRiseset: [
            { required: true, message: '日出日落不能为空', trigger: 'blur' }
          ],
          lifeSuggest: [
            { required: true, message: '生活指数不能为空', trigger: 'blur' }
          ],
          dateAt: [
            { required: true, message: '数据日期不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/weather/weatherday/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.locationId = data.weatherDay.locationId
                this.dataForm.weather = data.weatherDay.weather
                this.dataForm.air = data.weatherDay.air
                this.dataForm.sunRiseset = data.weatherDay.sunRiseset
                this.dataForm.lifeSuggest = data.weatherDay.lifeSuggest
                this.dataForm.dateAt = data.weatherDay.dateAt
                this.dataForm.createdAt = data.weatherDay.createdAt
                this.dataForm.updatedAt = data.weatherDay.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/weather/weatherday/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'locationId': this.dataForm.locationId,
                'weather': this.dataForm.weather,
                'air': this.dataForm.air,
                'sunRiseset': this.dataForm.sunRiseset,
                'lifeSuggest': this.dataForm.lifeSuggest,
                'dateAt': this.dataForm.dateAt,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
