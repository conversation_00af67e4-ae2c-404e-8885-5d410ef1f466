<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="1080px"
    @closed="$emit('closed')"
  >
    <el-form
      :model="dataForm"
      ref="dataForm"
      label-width="120px"
      :rules="dataRules"
    >
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model="dataForm.appId"
          :is-show-all="false"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="规则名" prop="name">
        <el-input v-model.trim="dataForm.name" />
      </el-form-item>
      <el-form-item label="管理广告账号" prop="advertiserIds">
        <el-input
          type="textarea"
          :rows="5"
          resize="none"
          v-model.trim="dataForm.advertiserIds"
        />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="回传有效时间" prop="convMinute">
            <el-input
              type="number"
              v-model.number="hour"
              @change="changeTime"
              placeholder="小时"
              :min="0"
              :max="24"
              style="width: 140px; margin-right: 20px;"
            >
              <template slot="append">小时</template>
            </el-input>
            <el-input
              type="number"
              v-model.number="minute"
              @change="changeTime"
              placeholder="分钟"
              :min="0"
              style="width: 140px"
            >
              <template slot="append">分钟</template>
            </el-input>
            <div style="color: #E6A23C; font-size: 12px; margin-top: 5px">
              折合分钟：{{ dataForm.convMinute }} 分钟.
              注意：折合成的分钟不能大于
              <span style="color: #F56C6C; font-size: 14px; font-weight: bold">
                {{ maxDay }}
              </span>
              天且不能为小数
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <div>
            <el-form-item
              label="激活条件"
              prop="extra.act_type"
              style="display: inline-block"
            >
              <el-select v-model="dataForm.extra.act_type">
                <el-option
                  v-for="[key, label] in actTypeList"
                  :value="key"
                  :label="label"
                  :key="key"
                />
              </el-select>
            </el-form-item>
            <el-form-item label-width="0" style="display: inline-block">
              <el-input
                type="number"
                :min="0"
                v-model.number="dataForm.ruleStrategyList[0].extra[0]"
                style="margin-left: 10px; width: 160px;"
              >
                <template slot="prepend">大于</template>
              </el-input>
            </el-form-item>
            <el-form-item label-width="0" style="display: inline-block">
              <el-input
                type="number"
                :min="0"
                v-model.number="dataForm.ruleStrategyList[0].extra[1]"
                style="margin-left: 10px; width: 300px;"
              >
                <template slot="prepend">并且 累计ecpm 大于</template>
              </el-input>
            </el-form-item>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关键行为条件" prop="extra.con_type">
            <el-select v-model="dataForm.extra.con_type" style="width: 100%">
              <el-option
                v-for="item in conTypeList"
                :label="item.label"
                :key="item.key"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件类型" prop="extra.event_type">
            <!--<el-select v-model="dataForm.extra.event_type" style="width: 100%">-->
            <!--  <el-option-->
            <!--    v-for="item in keyActionList"-->
            <!--    :label="item.label"-->
            <!--    :key="item.key"-->
            <!--    :value="item.key"-->
            <!--  />-->
            <!--</el-select>-->
            <map-select
              v-model="dataForm.extra.event_type"
              style="width: 100%"
              :list="keyActionList"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
          <el-form
            v-if="index > 0"
            :model="item"
            :rules="strategyListRules"
            inline
            ref="ruleStrategyListForm"
          >
            <el-form-item prop="extra[0]" :label="inputLabel.itemLabel1">
              <el-input
                type="number"
                v-model.number="item.extra[0]"
                clearable
                style="width: 120px"
              />
            </el-form-item>
            <el-form-item prop="extra[1]" :label="inputLabel.itemLabel2">
              <el-input
                type="number"
                v-model.number="item.extra[1]"
                clearable
                style="width: 120px"
              />
            </el-form-item>
            <el-form-item label="DEPTH =">
              <el-select
                v-model="item.properties.depth"
                clearable
                @change="item.extra[2] = $event"
                style="width: 120px"
              >
                <template v-for="num in 100">
                  <el-option
                    v-if="num % 5 === 0"
                    :key="num"
                    :label="num"
                    :value="num"
                  />
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="GA =" prop="extra[3]" :rules="buildGARules()">
              <el-select
                v-model="item.properties.is_ga_convert"
                @change="handleChangeGA(item, $event)"
                clearable
              >
                <el-option
                  v-for="ga in gaConvertList"
                  :key="ga.value"
                  :label="ga.label"
                  :value="ga.value"
                />
              </el-select>
            </el-form-item>
            <el-popconfirm
              v-if="dataForm.ruleStrategyList.length > 2"
              title="确定删除吗？"
              @confirm="removeRule(item, index)"
            >
              <i
                slot="reference"
                class="el-icon-remove-outline"
                style="color: red; cursor: pointer; padding: 0 5px;"
              />
            </el-popconfirm>
            <i
              class="el-icon-circle-plus-outline"
              style="color: #0BB2D4; cursor: pointer; padding: 0 5px;"
              @click="addRule(item, index)"
            />
          </el-form>
        </div>
        <el-alert
          v-if="isShowRemark"
          title="关键广告是指：开屏 + 激励视频 + 全屏视频 + 插屏"
          type="info"
          @close="closeRemark"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <map-select
          v-model.number="dataForm.status"
          :list="activateRulesStatus"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import AppSelectComponent from '@/components/app-select-component'
import { activateRulesStatus, actTypeList, keyActionList } from '@/map/activate'
import MapSelect from '@/components/map-select'
import { cloneDeep, difference } from 'lodash'

const rules = [
  { required: true, message: '必填', trigger: 'blur' },
  { type: 'integer', message: '必须是整数', trigger: 'blur' },
  { type: 'number', min: 0, message: '不能小于0' },
]

const localCloseKey = 'action-close-remark'

const properties = {
  is_ga_convert: 0,
  depth: 10,
  action_type1: null,
  action_type2: null,
  value1: null,
  value2: null,
}

// 每个页面都不一样
const ruleType = 4
// 每个页面都不一样
const category = 3

export default {
  components: { MapSelect, AppSelectComponent },
  data() {
    return {
      visible: false,
      dataForm: {
        id: null,
        appId: '',
        name: '',
        advertiserIds: '',
        convMinute: '',
        status: 0,
        ruleType,
        ruleStrategyList: [
          {
            category,
            conditions: 'page_show > 0',
            description: '到首页 > 0次',
            extra: [0, 0],
            properties: null,
            id: null,
            ruleId: null,
            type: 1,
            uniqueType: null,
          },
          {
            category,
            conditions: '',
            description: '',
            extra: [0, 0],
            properties,
            id: null,
            ruleId: null,
            type: 3, // 激活1，别的是3
            uniqueType: null,
          },
        ],
        extra: { con_type: '', act_type: 1, event_type: 'key_action1' },
        delRuleStrategyIds: [],
      },
      dataRules: {
        appId: [{ required: true, message: '必填', trigger: 'blur' }],
        name: [{ required: true, message: '必填', trigger: 'blur' }],
        advertiserIds: [{ required: true, message: '必填', trigger: 'blur' }],
        convMinute: [
          { required: true, message: '必填', trigger: 'blur' },
          { type: 'integer', message: '必须是整数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === 0) {
                return callback(new Error('折合分钟不能为0'))
              }
              if (value > this.maxDay * 24 * 60) {
                return callback(new Error('不能大于1天'))
              }
              callback()
            },
          },
        ],
        status: [{ required: true, message: '必填', trigger: 'blur' }],
        'extra.act_type': [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        'extra.event_type': [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        'extra.con_type': [
          { required: true, message: '必填', trigger: 'blur' },
        ],
      },
      strategyListRules: {
        'extra[0]': rules,
        'extra[1]': rules,
        'extra[2]': rules,
        'extra[3]': rules,
      },
      day: 0,
      hour: 0,
      minute: 1,
      maxDay: 1,
      activateRulesStatus,
      cacheRuleStrategyList: {},
      conTypeList: [
        {
          key: 1,
          label: '关键平均ECPM+激励视频IPU',
          itemLabel1: 'ECPM >=',
          itemLabel2: 'IPU >=',
        },
        {
          key: 2,
          label: '累计ECPM+累计IPU',
          itemLabel1: 'ECPM >=',
          itemLabel2: 'IPU >=',
        },
        {
          key: 3,
          label: '变现ECPM底价+激励视频IPU',
          itemLabel1: 'ECPM >=',
          itemLabel2: 'IPU >=',
        },
        {
          key: 4,
          label: '变现ECPM摸高达成+激励视频IPU',
          itemLabel1: 'ECPM >=',
          itemLabel2: 'IPU >=',
        },
        // video_success_show
        {
          key: 5,
          label: '关键平均ECPM+观看视频数',
          itemLabel1: 'ECPM >=',
          itemLabel2: '视频数 >=',
        },
        {
          key: 6,
          label: '累计ECPM+观看视频数',
          itemLabel1: 'ECPM >=',
          itemLabel2: '视频数 >=',
        },
      ],
      keyActionList,
      isInitLoading: false,
      // 缓存源数据
      cacheResourceData: {},
      isShowRemark: !localStorage.getItem(localCloseKey),
      actTypeList,
      // 行为类型：GA
      gaConvertList: [
        { value: 0, label: '衍生行为' },
        { value: 1, label: '关键行为' },
      ],
    }
  },
  computed: {
    inputLabel() {
      const res = this.conTypeList.find(
        it => it.key === this.dataForm.extra.con_type
      )
      return res ? res : {}
    },
  },
  watch: {
    'dataForm.extra.con_type'(currentType, oldType) {
      // 缓存上一次数据
      this.cacheRuleStrategyList[oldType] = cloneDeep(
        this.dataForm.ruleStrategyList
      )

      if (this.cacheRuleStrategyList[currentType]) {
        // 获取缓存数据
        this.dataForm.ruleStrategyList = cloneDeep(
          this.cacheRuleStrategyList[currentType]
        )
      } else {
        if (this.isInitLoading) return
        // 设置新数据
        const firstItem = cloneDeep(this.dataForm.ruleStrategyList[0])
        const otherItem = cloneDeep(this.dataForm.ruleStrategyList[0])
        otherItem.properties = { ...properties }
        let len = 4
        otherItem.id = null
        otherItem.extra = new Array(len).fill(0)
        otherItem.type = 3
        this.dataForm.ruleStrategyList = [firstItem, otherItem]
      }

      // 必须等待元素更新完毕再检查，否则 $refs 是没有更新的
      this.$nextTick(() => {
        this.ruleStrategyListFormValidate()
      })
    },
  },
  methods: {
    init(id) {
      this.visible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm.id = id || null

        if (this.dataForm.id) {
          this.isInitLoading = true
          this.$http({
            url: this.$http.adornUrl(
              `/activate/activaterules/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                for (const key in this.dataForm) {
                  const resItem = data.activateRules[key]
                  if (
                    key in data.activateRules &&
                    resItem !== undefined &&
                    resItem !== null
                  ) {
                    this.dataForm[key] = resItem
                  }
                }
                this.formatTime()

                if (!this.dataForm.extra.act_type) {
                  this.dataForm.extra.act_type = 1
                }
                // 缓存源数据
                this.cacheResourceData = cloneDeep(this.dataForm)
              } else {
                this.$message.error(data.msg || '服务器错误')
              }
            })
            .finally(() => {
              this.isInitLoading = false
            })
        } else {
          // 默认值写在上面会出问题
          this.dataForm.extra.con_type = 1
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      Promise.all([
        ...this.$refs.ruleStrategyListForm.map(form => form.validate()),
        this.$refs.dataForm.validate(),
      ]).then(() => {
        // 组织一下数据
        this.organizeData()
        // 赛选出要删除的 id
        this.buildDelRuleStrategyIds()
        console.log('校验通过:', this.dataForm)
        // 发送请求
        this.submitData()
      })
    },
    submitData() {
      this.$http({
        url: this.$http.adornUrl(`/activate/activaterules/save/behavior`),
        method: 'post',
        data: this.$http.adornData(this.dataForm),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.visible = false
          this.$emit('refreshDataList')
        } else {
          this.$message.error(data.msg || '服务器错误')
        }
      })
    },
    changeTime() {
      this.dataForm.convMinute =
        this.day * 24 * 60 + this.hour * 60 + this.minute
    },
    formatTime() {
      // 小时数
      let hour = Math.floor(this.dataForm.convMinute / 60)
      // 减去小时
      let minute = this.dataForm.convMinute - hour * 60
      this.hour = hour
      this.minute = minute
    },
    addRule(item, index) {
      const data = cloneDeep(item)
      const depth = 10
      data.id = null
      data.extra = data.extra.map(() => 0)
      // properties.depth 中的值必须和 data.extra[2] 保持一致
      data.properties.depth = depth
      data.extra[2] = depth

      this.dataForm.ruleStrategyList.splice(index + 1, 0, data)
    },
    removeRule(item, index) {
      this.dataForm.ruleStrategyList.splice(index, 1)
    },
    // 组织一下数据
    organizeData() {
      this.dataForm.ruleStrategyList = this.dataForm.ruleStrategyList.map(
        (it, index) => {
          const properties = {
            ...it.properties,
            ...this.buildActionTypeAndValue(it),
          }

          // 虽然初始化的时候有ruleType,category，这里做个重置，修复以前的脏数据
          return {
            ...it,
            ...this.buildConditionsAndDescription(it, index),
            properties: index === 0 ? null : properties,
            ruleType,
            category,
          }
        }
      )
    },
    // 生成条件语句和描述
    buildConditionsAndDescription(it, index) {
      let description = it.description
      let conditions = it.conditions
      let type = 1

      if (index !== 0) {
        type = 3
        switch (this.dataForm.extra.con_type) {
          case 1:
            description = `关键平均ECPM >= ${it.extra[0]} 且 激励视频次数 >= ${it.extra[1]}次`
            conditions = `key_avg_ecpm >= ${it.extra[0]} AND reward_exposure >= ${it.extra[1]}`
            break
          case 2:
            description = `累计ECPM >= ${it.extra[0]}  且 累计IPU >= ${it.extra[1]}次`
            conditions = `exposure_ecpm >= ${it.extra[0]}  AND advertise_exposure >= ${it.extra[1]}`
            break
          case 3:
            description = `变现ECPM低价 >= ${it.extra[0]} 且 激励视频IPU >= ${it.extra[1]}次`
            conditions = `min_ecpm >= ${it.extra[0]} AND reward_exposure >= ${it.extra[1]}`
            break
          case 4:
            description = `变现ECPM摸高达成 >= ${it.extra[0]} 且 激励视频IPU >= ${it.extra[1]}次`
            conditions = `max_ecpm >= ${it.extra[0]} AND reward_exposure >= ${it.extra[1]}`
            break

          case 5:
            description = `关键平均ECPM >= ${it.extra[0]} 且 观看视频数 >= ${it.extra[1]}次`
            conditions = `key_avg_ecpm >= ${it.extra[0]} AND video_success_show >= ${it.extra[1]}`
            break
          case 6:
            description = `累计ECPM >= ${it.extra[0]} 且 观看视频数 >= ${it.extra[1]}次`
            conditions = `exposure_ecpm >= ${it.extra[0]} AND video_success_show >= ${it.extra[1]}`
            break
        }
      } else {
        type = 1
        const value2 = it.extra[1]

        switch (this.dataForm.extra.act_type) {
          case 1:
            description = `到首页 > ${it.extra[0]}`
            conditions = `page_show > ${it.extra[0]}`
            break
          case 2:
            description = `心跳 > ${it.extra[0]}`
            conditions = `device_ping > ${it.extra[0]}`
            break
          case 3:
            description = `广告曝光次数 > ${it.extra[0]}`
            conditions = `exposure > ${it.extra[0]}`
            break
          case 4:
            description = `广告曝点击次数 > ${it.extra[0]}`
            conditions = `click > ${it.extra[0]}`
            break
          case 5:
          description = `观看时长 > ${it.extra[0]}`
          conditions = `click > ${it.extra[0]}`
          break  
        }

        if (value2) {
          description += ` 累计ecpm > ${value2}`
          conditions += ` && reward_ecpm_first > ${value2}`
        }
      }

      return {
        type,
        description,
        conditions,
      }
    },
    /**
     * 设置 action_type 和 value 值
     *
     * 关于 action_type1、action_type2、value1、value2 值的说明
     *  首先：
     *    action_type1 和 value1 是一对值
     *    action_type2 和 value2 是一对值
     *
     *  action_type1、value1 -> 对应的是 '关键平均ECPM' 或 '累计ECPM'
     *    action_type1：对应的type值
     *    value1：对应的是那个输入框的值
     *  action_type2、value1 -> 对应的是 '激励视频IPU' 或 '累计IPU'
     *    action_type2：对应的type值
     *    value2：对应的是那个输入框的值
     *
     *  action_type的值又有对应
     *    关键平均ecpm        ->     2  【value * 100】
     *    激励视频ipu         ->     3  【value * 1】
     *    累积ecpm           ->     4   【value / 10】
     *    累计ipc            ->     1   【value * 1】
     *    变现ecpm底价        ->     9  【value * 100】
     *    变现ecpm摸高达成     ->     13 【value * 100】
     *    观看视频数           ->     13 【value * 1】
     */
    buildActionTypeAndValue(it) {
      let value1 = it.extra[0]
      let value2 = it.extra[1]
      let action_type1 = ''
      let action_type2 = ''

      switch (this.dataForm.extra.con_type) {
        case 1:
          action_type1 = 2
          action_type2 = 3
          value1 *= 100
          break
        case 2:
          action_type1 = 4
          action_type2 = 1
          value1 /= 10
          break
        case 3:
          action_type1 = 9
          action_type2 = 3
          value1 *= 100
          break
        case 4:
          action_type1 = 13
          action_type2 = 3
          value1 *= 100
          break

        case 5:
          action_type1 = 2
          action_type2 = 5
          value1 *= 100
          break
        case 6:
          action_type1 = 4
          action_type2 = 5
          value1 /= 10
          break
      }

      return {
        value1,
        value2,
        action_type1,
        action_type2,
      }
    },
    buildMoreThanValidator(minValue) {
      return [
        ...rules,
        {
          validator: (rule, value, callback) =>
            value < minValue
              ? callback(new Error('不能小于前面的数字'))
              : callback(),
        },
      ]
    },
    buildGARules() {
      return [
        {
          validator: (rule, value, callback) => {
            const list = this.dataForm.ruleStrategyList.filter(
              it => it.properties && it.properties.is_ga_convert === 1
            )

            if (list && list.length !== 1) {
              callback(new Error('有且必须只能有一个关键行为'))
            } else {
              callback()
            }
          },
        },
      ]
    },
    // 构建删除id
    buildDelRuleStrategyIds() {
      if (
        this.cacheResourceData.ruleStrategyList &&
        this.dataForm.ruleStrategyList
      ) {
        // 原先的 id
        const resourceIds = this.cacheResourceData.ruleStrategyList
          .filter(it => it.id)
          .map(it => it.id)
        // 现存的 id
        const currentIds = this.dataForm.ruleStrategyList
          .filter(it => it.id)
          .map(it => it.id)
        // 要删除的 id
        this.dataForm.delRuleStrategyIds = difference(resourceIds, currentIds)
      }
    },
    closeRemark() {
      localStorage.setItem(localCloseKey, 'true')
    },
    ruleStrategyListFormValidate() {
      const forms = this.$refs.ruleStrategyListForm
      forms && forms.forEach(form => form.validate())
    },
    handleChangeGA(item, $event) {
      item.extra[3] = $event
      this.ruleStrategyListFormValidate()
    },
  },
}
</script>
