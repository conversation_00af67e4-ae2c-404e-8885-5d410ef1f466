<template>
  <div style="display: inline-block">
    <el-popconfirm v-if="showDel" title="确定删除吗？" @confirm="removeRule">
      <i
        slot="reference"
        class="el-icon-remove-outline"
        style="color: red; cursor: pointer; padding: 0 5px;"
      />
    </el-popconfirm>
    <i
      v-if="showAdd"
      class="el-icon-circle-plus-outline"
      style="color: #0BB2D4; cursor: pointer; padding: 0 5px;"
      @click="addRule"
    />
  </div>
</template>

<script>
export default {
  props: {
    showDel: {
      type: Boolean,
      default: false,
    },
    showAdd: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    removeRule() {
      this.$emit('remove-rule')
    },
    addRule() {
      this.$emit('add-rule')
    },
  },
}
</script>
