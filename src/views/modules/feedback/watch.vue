<template>
  <div>
    <base-form
      ref="form"
      :model.sync="dataForm"
      inline
      @submit="submit"
      :rules="dataRule"
    >
      <el-form-item label="应用">
        <app-select @change="getDataList" @init-app-id="getDataList" />
      </el-form-item>
      <el-form-item prop="returnType" label="回传条件">
        <el-select v-model="dataForm.returnType">
          <el-option
            v-for="(item, index) in returnTypeList"
            :key="index"
            :value="item.id"
            :label="item.text"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="activityReturnType" label="激活回传条件">
        <el-select v-model="dataForm.activityReturnType" clearable>
          <el-option
            v-for="[key, text] in activityReturnTypeList"
            :key="key"
            :value="key"
            :label="text"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="dayTime" label="日期">
        <el-date-picker
          v-model="dataForm.dayTime"
          align="right"
          type="date"
          value-format="yyyyMMdd"
          placeholder="选择日期"
        />
      </el-form-item>
    </base-form>
    <div style="margin-bottom: 20px;">
      <el-button
        type="primary"
        icon="el-icon-download"
        @click="$downloadTableToExcel()"
      >
        下载Excel
      </el-button>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%;"
    >
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      />
      <el-table-column
        prop="ecpmLow"
        header-align="center"
        align="center"
        label="分量ecpm"
      >
        <template slot-scope="{ row }">
          {{ [row.ecpmLow, row.ecpmHigh].filter(it => it).join('-') }}
        </template>
      </el-table-column>
      <el-table-column
        prop="returnType"
        header-align="center"
        align="center"
        label="条件"
      >
        <template slot-scope="{ row }">
          <div v-if="row.returnType === 3">
            {{
              [
                {
                  num: row.confInsTimes,
                  text: '激励视频次数',
                },
                {
                  num: row.confFullTimes,
                  text: '全屏视频次数',
                },
              ]

                .filter(it => it.num)
                .map(it => `${it.text}${it.num}`)
                .join('或')
            }}
          </div>
          <div v-if="row.returnType === 1 || row.returnType === 5">
            激励视频次数 {{ row.confInsTimes }}
          </div>
          <div v-if="row.returnType === 2">
            全屏视频次数 {{ row.confFullTimes }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="newNum"
        header-align="center"
        align="center"
        label="新增用户"
      />
      <el-table-column
        prop="transformNum"
        header-align="center"
        align="center"
        label="关键行为转化用户数"
      />
      <el-table-column
        prop="transformPercent"
        header-align="center"
        align="center"
        label="关键行为转化率"
      >
        <template slot-scope="{ row }">
          {{ ((row.transformPercent || 0) * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column
        prop="userPercent"
        header-align="center"
        align="center"
        label="用户占比"
      >
        <template slot-scope="{ row }">
          {{ ((row.userPercent || 0) * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="CPA"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="红包消耗"
      />
      <el-table-column
        prop="returnArpu"
        header-align="center"
        align="center"
        label="回传时间ARPU"
      />
      <el-table-column
        prop="totalArpu"
        header-align="center"
        align="center"
        label="累计ARPU"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="回传时间ROI"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="累计时间ROI"
      />
    </el-table>
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import BaseForm from '@/components/base-form'
import { activityReturnTypeList, returnTypeList } from '@/map/common'
import dayjs from 'dayjs'

export default {
  name: 'watch',
  components: { BaseForm, AppSelect },
  data() {
    return {
      activityReturnTypeList,
      returnTypeList,
      dataForm: {
        activityReturnType: Array.from(activityReturnTypeList.keys())[0],
        returnType: returnTypeList[0].id,
        dayTime: dayjs().format('YYYYMMDD'),
      },
      dataListLoading: false,
      dataList: [],
      dataRule: {
        // activityReturnType: [{ required: true, message: '不能为空' }],
        returnType: [{ required: true, message: '不能为空' }],
        dayTime: [{ required: true, message: '不能为空' }],
      },
    }
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    submit() {
      this.getDataList()
    },
    getDataList() {
      this.$refs.form.getElForm().validate(validate => {
        if (validate) {
          this.dataListLoading = true
          this.$http({
            url: this.$http.adornUrl(
              '/feedback/lanchconfigdetail/feedback_report_list'
            ),
            method: 'get',
            params: this.$http.adornParams({
              appId: this.getAppCodeInfo().code,
              ...this.dataForm,
            }),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataList = data.list
              } else {
                this.dataList = []
                this.$message.error(data.msg || '服务器错误')
              }
            })
            .finally(() => {
              this.dataListLoading = false
            })
        }
      })
    },
    getAppCodeInfo() {
      const res = this.$store.state.ad.appList.find(
        it => it.id === this.$store.state.ad.appId
      )

      return {
        code: res && res.code ? res.code : 0,
        appName: res && res.name ? res.name : '',
      }
    },
  },
}
</script>
