<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="1100px"
    @closed="$emit('closed')"
    :modal-append-to-body="false"
    :append-to-body="false"
  >
    <el-form
      :model="dataForm"
      ref="dataForm"
      label-width="140px"
      :rules="dataRules"
    >
      <el-form-item label="应用" prop="appId">
        <app-select-component v-model="dataForm.appId" :is-show-all="false" />
      </el-form-item>
      <el-form-item label="类型" prop="ruleType">
        <!--<app-select-component v-model="dataForm.appId" :is-show-all="false" />-->
        <el-select v-model="dataForm.ruleType">
          <el-option :value="3" label="回传2.0" />
          <el-option :value="1" label="回传3.0" />
<!--          <el-option :value="5" label="付费行为" />-->
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="dataForm.ruleType === 1"
        label="次留类型"
        prop="extra.alive_type"
      >
        <map-select v-model="dataForm.extra.alive_type" :list="aliveTypeList" />
      </el-form-item>
      <el-form-item label="规则名" prop="name">
        <el-input v-model.trim="dataForm.name" />
      </el-form-item>
      <el-form-item label="管理广告账号" prop="advertiserIds">
        <el-input
          type="textarea"
          :rows="5"
          resize="none"
          v-model.trim="dataForm.advertiserIds"
          placeholder="请输入广告账号ID，每行一个或用逗号分隔"
        />
        <div style="color: #909399; font-size: 12px; margin-top: 5px;">
          支持换行分隔或逗号分隔，提交时会自动转换为逗号分隔格式
        </div>
      </el-form-item>
      <el-form-item v-if="!dataForm.id" label="批量添加广告账号">
        <el-checkbox v-model="batchAddAdvertiserIds">逐个批量添加</el-checkbox>
      </el-form-item>
      <el-form-item label="回传有效时间" prop="convMinute">
        <div style="display: flex">
          <el-input
            v-model.number="day"
            @change="changeTime"
            type="number"
            placeholder="天"
            :min="0"
            :max="maxDay"
            style="margin-right: 20px"
          >
            <template slot="append">天</template>
          </el-input>
          <el-input
            v-model="hour"
            @change="changeTime"
            type="number"
            placeholder="小时"
            :min="0"
            :max="24"
            style="margin-right: 20px"
          >
            <template slot="append">小时</template>
          </el-input>
          <el-input
            v-model.number="minute"
            type="number"
            @change="changeTime"
            placeholder="分钟"
            :min="0"
          >
            <template slot="append">分钟</template>
          </el-input>
        </div>
        <div style="color: #e6a23c; font-size: 12px">
          <span style="color: #0bb2d4; margin-right: 10px">
            折合分钟：{{ dataForm.convMinute || 0 }}
          </span>
          注意：折合成的分钟不能大于
          <span style="color: #f56c6c; font-size: 14px; font-weight: bold">
            {{ maxDay }}
          </span>
          天且不能为小数
          <span style="color: black;margin-left: 10px">
            <el-tooltip
              class="item"
              effect="dark"
              content="1、计算用户在有效时间内的价格 2、当不满足回传比例的时，则从最近20分钟以内达标用户中进行回传"
              placement="top"
            >
          <i class="el-icon-question" />
        </el-tooltip>
          </span>
        </div>
      </el-form-item>

      <el-form-item label="激活回传有效时间" prop="actConvMinute">
        <el-input-number
          v-model="actConvHour"
          @change="changeActConvMinute"
          placeholder="小时"
          :min="0"
          :max="24"
        />
        小时
        <el-input-number
          v-model="actConvMinute"
          @change="changeActConvMinute"
          placeholder="分钟"
          :min="0"
        />
        分钟
        <span style="color: #0bb2d4; margin-left: 20px">
          折合分钟：{{ dataForm.actConvMinute }}
        </span>
        <div style="color: #e6a23c; font-size: 12px">
          注意：折合成的分钟不能大于
          <span style="color: #f56c6c; font-size: 14px; font-weight: bold">
            {{ actConvMaxDay }}
          </span>
          天且不能为小数
        </div>
      </el-form-item>

      <div>
        <el-form-item
          label="激活条件"
          prop="extra.act_type"
          style="display: inline-block"
        >
          <el-select v-model="dataForm.extra.act_type">
            <el-option
              v-for="[key, label] in actTypeList"
              :value="key"
              :label="label"
              :key="key"
            />
          </el-select>
          <span style="padding: 0 5px">大于等于</span>
          <el-form-item label-width="0" style="display: inline-block">
            <el-input-number
              type="number"
              :min="0"
              v-model="dataForm.ruleStrategyList[0].extra[0]"
            />
            <span style="color: #0bb2d4; margin-left: 10px" v-if="dataForm.extra.act_type === 8">
              {{ `(${(dataForm.ruleStrategyList[0].extra[0] / 100).toFixed(2)}元)` }}
            </span>
          </el-form-item>
          <template v-if="dataForm.extra.act_type !== 5">
            <br>
            <span style="padding-left: 10px">并且</span>
            <span style="padding-left: 10px">用户累计ARPU值(分)</span>
            <span style="padding-left: 10px">>=</span>
            <el-form-item
              label-width="320"
              style="display: inline-block; margin-left: 10px"
            >
              <el-input-number
                type="number"
                :min="0"
                v-model="dataForm.ruleStrategyList[0].extra[1]"
              />
              <span style="color: #0bb2d4; margin-left: 10px">
                {{ `(${(dataForm.ruleStrategyList[0].extra[1] / 100000).toFixed(2)}元)` }}
              </span>
            </el-form-item>
            <span style="padding-left: 10px">并且&lt;=</span>
            <el-form-item
              label-width="320"
              style="display: inline-block; margin-left: 10px"
            >
              <el-input-number
                type="number"
                :min="0"
                v-model="dataForm.ruleStrategyList[0].extra[2]"
              />
              <span style="color: #0bb2d4; margin-left: 10px">
                {{ `(${(dataForm.ruleStrategyList[0].extra[2] / 100000).toFixed(2)}元)` }}
              </span>
            </el-form-item>
          </template>
        </el-form-item>
      </div>
      <el-form-item label="厂商" prop="uniqueType">
        <el-select v-model="dataForm.uniqueType" style="width: 400px">
          <el-option
            v-for="item in uniqueTypeList"
            :label="item.label"
            :key="item.key"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="ruleStrategyLabel" prop="ruleStrategyList">
        <el-select v-model="dataForm.extra.con_type" style="width: 400px">
          <el-option
            v-for="item in conTypeList"
            :label="item.label"
            :key="item.key"
            :value="item.key"
          />
        </el-select>
        <!--ECPM+激励视频-->
        <div v-if="dataForm.extra.con_type === 2" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次激励视频</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--ECPM+全屏视频-->
        <div v-if="dataForm.extra.con_type === 3" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次全屏视频</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--ECPM+激励/全屏视频-->
        <div v-if="dataForm.extra.con_type === 4" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次激励视频</span>
              <span style="padding: 0 5px">或</span>
              <el-form-item prop="extra[3]">
                <el-input-number v-model="item.extra[3]" />
              </el-form-item>
              <span style="padding: 0 5px">次全屏视频</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--单ECPM-->
        <div v-if="dataForm.extra.con_type === 5" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <!--单激励视频-->
        <div v-if="dataForm.extra.con_type === 6" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <span style="padding-right: 5px">要求</span>
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">次激励视频</span>
            </el-form>
          </div>
        </div>
        <!--平均ecpm+激励视频-->
        <div v-if="dataForm.extra.con_type === 7" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次激励视频</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--激励平均ecpm+提现次数-->
        <div v-if="dataForm.extra.con_type === 8" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次提现次数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--键广告ecpm+关键IPU-->
        <div v-if="dataForm.extra.con_type === 9" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">关键IPU</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--激励视频平均ECPM + IPU-->
        <div v-if="dataForm.extra.con_type === 10" style="margin-top: 10px">
          <div
            v-for="(item, index) in dataForm.ruleStrategyList"
            :key="index"
            style="margin-top: 10px"
          >
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">IPU</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--关键广告累计ECPM+观看视频数-->
        <div v-if="dataForm.extra.con_type === 11" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次观看视频数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--关键广告平均ECPM+观看视频数-->
        <div v-if="dataForm.extra.con_type === 12" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次观看视频数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--激励视频累计ECPM+观看视频数-->
        <div v-if="dataForm.extra.con_type === 13" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次观看视频数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--激励视频平均ECPM+观看视频数-->
        <div v-if="dataForm.extra.con_type === 14" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[0])"
              >
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[2]">
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">次观看视频数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--关键广告曝光+章节数-->
        <div v-if="dataForm.extra.con_type === 15" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <span style="padding-right: 5px">要求</span>
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
                <span style="padding-left: 5px">次关键广告曝光</span>
              </el-form-item>
              <span style="padding: 0 5px">并且 >=</span>
              <el-form-item prop="extra[1]">
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span>章节数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--关键广告曝光+答题数-->
        <div v-if="dataForm.extra.con_type === 16" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <span style="padding-right: 5px">要求</span>
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
                <span style="padding-left: 5px">次关键广告曝光</span>
              </el-form-item>
              <span style="padding: 0 5px">并且 >=</span>
              <el-form-item prop="extra[1]">
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span>答题数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--激励视频+章节数-->
        <div v-if="dataForm.extra.con_type === 17" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <span style="padding-right: 5px">要求</span>
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
                <span style="padding-left: 5px">次激励视频</span>
              </el-form-item>
              <span style="padding: 0 5px">并且 >=</span>
              <el-form-item prop="extra[1]">
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span>章节数</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--提现次数+关键广告平均ECPM+激励视频IPU-->
        <div v-if="dataForm.extra.con_type === 18" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
                <span style="padding-left: 5px">次提现</span>
              </el-form-item>
              <span style="padding: 0 5px">并且</span>

              <el-form-item prop="extra[1]">
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[2]"
                :rules="buildMoreThanValidator(item.extra[1])"
              >
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[3]">
                <el-input-number v-model="item.extra[3]" />
              </el-form-item>
              <span style="padding: 0 5px">IPU</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>
        <!--提现次数+激励视频平均ECPM+激励视频IPU-->
        <div v-if="dataForm.extra.con_type === 19" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
                <span style="padding-left: 5px">次提现</span>
              </el-form-item>
              <span style="padding: 0 5px">并且</span>

              <el-form-item prop="extra[1]">
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[2]"
                :rules="buildMoreThanValidator(item.extra[1])"
              >
                <el-input-number v-model="item.extra[2]" />
              </el-form-item>
              <span style="padding: 0 5px">要求</span>
              <el-form-item prop="extra[3]">
                <el-input-number v-model="item.extra[3]" />
              </el-form-item>
              <span style="padding: 0 5px">IPU</span>

              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>

        <!--激励视频平均ECPM+激励视频IPU+关键广告累计ECPM-->
        <div v-if="dataForm.extra.con_type === 20" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input
                  type="number"
                  v-model.number="item.extra[0]"
                  style="width: 100px"
                />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[1]"
                :rules="buildMoreThanValidator(item.extra[1])"
              >
                <el-input
                  type="number"
                  v-model.number="item.extra[1]"
                  style="width: 100px"
                />
              </el-form-item>

              <span style="margin-left: 10px; margin-right: 5px">要求</span>

              <el-form-item prop="extra[2]">
                <el-input
                  type="number"
                  v-model.number="item.extra[2]"
                  style="width: 100px"
                />
              </el-form-item>
              <span>IPU</span>

              <span style="margin-left: 20px; margin-right: 5px">要求</span>

              <el-form-item prop="extra[3]">
                <el-input
                  type="number"
                  v-model.number="item.extra[3]"
                  style="width: 100px"
                />
              </el-form-item>
              <span style="padding: 0 5px">&le; ECPM &lt;</span>
              <el-form-item
                prop="extra[4]"
                :rules="buildMoreThanValidator(item.extra[4])"
              >
                <el-input
                  type="number"
                  v-model.number="item.extra[4]"
                  style="width: 100px"
                />
              </el-form-item>

              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>

        <!--激励视频平均ECPM+激励视频IPU+关键广告累计ECPM-->
        <div v-if="dataForm.extra.con_type === 21" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input
                  type="number"
                  v-model.number="item.extra[0]"
                  style="width: 100px"
                />
              </el-form-item>
              <span style="padding: 0 5px">次</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>

        <!--付费次数-->
        <div v-if="dataForm.extra.con_type === 22" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input
                  type="number"
                  v-model.number="item.extra[0]"
                  style="width: 100px"
                />
              </el-form-item>
              <span style="padding: 0 5px">次</span>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>

        <!--加桌成功+付费页曝光-->
        <div v-if="dataForm.extra.con_type === 23" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <!--<el-form-item prop="extra[0]">-->
              <!--  <el-input-->
              <!--    type="number"-->
              <!--    v-model.number="item.extra[0]"-->
              <!--    style="width: 100px;"-->
              <!--    disabled-->
              <!--  />-->
              <!--</el-form-item>-->
              <el-form-item prop="extra[1]">
                <el-input
                  type="number"
                  v-model.number="item.extra[1]"
                  style="width: 100px"
                />
              </el-form-item>
              <span style="padding: 0 5px">次(付费页曝光)</span>
              <add-remove-btn
                :show-add="false"
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>

        <!--GPT激活-->
        <div v-if="dataForm.extra.con_type === 24" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="strategyListRules"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <el-form-item prop="extra[0]">
                <el-input
                  type="number"
                  v-model.number="item.extra[0]"
                  style="width: 100px"
                />
              </el-form-item>
              <span style="padding: 0 5px">次</span>
              <!--<add-remove-btn-->
              <!--  :show-del="dataForm.ruleStrategyList.length > 2"-->
              <!--  @remove-rule="removeRule(item, index)"-->
              <!--  @add-rule="addRule(item, index)"-->
              <!--/>-->
            </el-form>
          </div>
        </div>

        <!--ecpm + 回传比例-->
        <div v-if="dataForm.extra.con_type === 1" style="margin-top: 10px">
          <div v-for="(item, index) in dataForm.ruleStrategyList" :key="index">
            <el-form
              v-if="index > 0"
              :model="item"
              :rules="ecpmRoat"
              label-width="0"
              inline
              ref="ruleStrategyListForm"
            >
              <span style="padding-right: 5px">要求ecpm >=</span>
              <el-form-item prop="extra[0]">
                <el-input-number v-model="item.extra[0]" />
              </el-form-item>
              <span style="padding: 0 5px">回传比例：</span>
              <el-form-item prop="extra[1]">
                <el-input-number v-model="item.extra[1]" />
              </el-form-item>
              <add-remove-btn
                :show-del="dataForm.ruleStrategyList.length > 2"
                @remove-rule="removeRule(item, index)"
                @add-rule="addRule(item, index)"
              />
            </el-form>
          </div>
        </div>

        <el-alert
          v-if="isShowRemark"
          title="关键广告是指：开屏 + 激励视频 + 全屏视频 + 插屏"
          type="info"
          @close="closeRemark"
        />
      </el-form-item>

      <el-form
        :model="deductionList"
        label-width="140px"
        ref="deductionListForm"
        :rules="deductionRules"
      >
        <el-form-item
          v-for="(deduction, index) in deductionList"
          :key="deduction.type"
          :prop="index + '.data'"
          :label="deduction.label"
        >
          <el-input
            v-model="deduction.data"
            style="width: 200px"
          >
            <template #append>%</template>
          </el-input>
        </el-form-item>
      </el-form>

      <el-form-item label="状态" prop="status">
        <map-select
          v-model.number="dataForm.status"
          :list="activateRulesStatus"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import AppSelectComponent from '@/components/app-select-component'
import { activateRulesStatus, actTypeList, aliveTypeList } from '@/map/activate'
import MapSelect from '@/components/map-select'
import { cloneDeep, difference } from 'lodash'
import { conTypeList } from '@/map/feedback'
import { genSql, genSql2 } from '@/views/modules/feedback/utils/genSql'
import AddRemoveBtn from '@/views/modules/feedback/components/add-remove-btn.vue'
import { addOrUpdateDeduction, getDeduction } from '@/api/feedback'

const rules = [
  { required: true, message: '必填', trigger: 'blur' },
  { type: 'integer', message: '必须是整数', trigger: 'blur' },
  { type: 'number', min: 0, message: '不能小于0' },
]

const rules2 = [
  { required: true, message: '必填', trigger: 'blur' },
  {
    validator: (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('不能为空'))
      } else if (!isNaN(value) && !isNaN(parseFloat(value))) {
        callback()
      } else {
        callback(new Error('必须是数字'))
      }
    },
    trigger: 'blur',
  },
  { type: 'number', min: 0, message: '不能小于0' },
]

const localCloseKey = 'launch-config-close-remark'

export default {
  components: { MapSelect, AppSelectComponent, AddRemoveBtn },
  data() {
    return {
      aliveTypeList,
      visible: false,
      dataForm: {
        id: null,
        appId: '',
        name: '',
        advertiserIds: '',
        convMinute: '',
        status: 0,
        ruleType: 3,
        actConvMinute: 0,
        uniqueType: '',
        ruleStrategyList: [
          {
            category: 2,
            conditions: 'page_show > 0',
            description: '到首页 > 0次',
            extra: [0, 0, 0],
            id: null,
            ruleId: null,
            type: 1,
            uniqueType: null,
          },
          {
            category: 2,
            conditions: '',
            description: '',
            extra: [0, 0, 0],
            id: null,
            ruleId: null,
            type: 3,
            uniqueType: null,
          },
        ],
        // 双出价：才有 alive_type
        extra: { con_type: '', act_type: 1, alive_type: 1 },
        delRuleStrategyIds: [],
      },
      dataRules: {
        appId: [{ required: true, message: '必填', trigger: 'blur' }],
        name: [{ required: true, message: '必填', trigger: 'blur' }],
        ruleType: [{ required: true, message: '必填', trigger: 'blur' }],
        advertiserIds: [
          { required: true, message: '必填', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (!value) {
                return callback();
              }
              // 移除中文逗号检查，允许换行符
              if (/，/.test(value)) {
                return callback(new Error('请使用英文逗号或换行符分隔广告账号ID，不能使用中文逗号'));
              }
              // 将换行符转换为逗号进行验证
              const normalizedValue = value.replace(/[\n\r]+/g, ',').replace(/,+/g, ',').replace(/^,|,$/g, '');
              if (!/^[^\s,]+(?:,[^\s,]+)*$/.test(normalizedValue)) {
                return callback(new Error('广告账号ID格式不正确，请确保每个ID不为空'));
              }
              callback();
            }, trigger: 'blur' }
        ],
        uniqueType: [{ required: true, message: '必填', trigger: 'blur' }],
        convMinute: [
          { required: true, message: '必填', trigger: 'blur' },
          { type: 'integer', message: '必须是整数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              console.log('convMinute value', value)
              if (value === 0) {
                return callback(new Error('折合分钟不能为0'))
              }
              if (value > this.maxDay * 24 * 60) {
                return callback(new Error(`不能大于${this.maxDay}天`))
              }
              callback()
            },
          },
        ],
        actConvMinute: [
          { required: true, message: '必填', trigger: 'blur' },
          { type: 'integer', message: '必须是整数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === 0) {
                return callback(new Error('折合分钟不能为0'))
              }
              if (value > this.actConvMaxDay * 24 * 60) {
                return callback(new Error('不能大于1天'))
              }
              callback()
            },
          },
        ],
        status: [{ required: true, message: '必填', trigger: 'blur' }],
        'extra.act_type': [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        'extra.alive_type': [
          { required: true, message: '必填', trigger: 'blur' },
        ],
      },
      strategyListRules: {
        'extra[0]': rules,
        'extra[1]': rules,
        'extra[2]': rules,
        'extra[3]': rules,
        'extra[4]': rules,
        'extra[5]': rules,
      },
      ecpmRoat: {
        'extra[0]': rules,
        'extra[1]': rules2,
      },
      deductionList: [
        { label: '激活回传百分比', type: 1, data: null },
        { label: '次留回传百分比', type: 2, data: null },
        { label: '关键行为回传百分比', type: 3, data: null },
      ],
      deductionRules: {
        '0.data': [
          { required: true, message: '必填', trigger: 'blur' },
        ],
      },
      day: 0,
      actConvDay: 0,
      hour: 0,
      actConvHour: 0,
      minute: 0,
      actConvMinute: 0,
      maxDay: 7,
      actConvMaxDay: 1,
      activateRulesStatus,
      cacheRuleStrategyList: {},
      conTypeList: conTypeList,
      isInitLoading: false,
      // 缓存源数据
      cacheResourceData: {},
      isShowRemark: !localStorage.getItem(localCloseKey),
      actTypeList,
      timer: null,
      uniqueTypeList: [
        { label: 'VIVO', key: '1' },
        { label: 'OPPO', key: '2' },
        { label: '小米', key: '3' },
        { label: '华为', key: '4' },
        { label: '荣耀', key: '5' },
      ],
      batchAddAdvertiserIds: false,
    }
  },

  computed: {
    adList() {
      // 将换行符转换为逗号，然后处理
      const normalizedIds = this.dataForm.advertiserIds
        .replace(/[\n\r]+/g, ',')
        .replace(/,+/g, ',')
        .replace(/^,|,$/g, '');
      return normalizedIds.split(',').join('_')
    },
    ruleStrategyLabel() {
      let str = ''
      switch (this.dataForm.ruleType) {
        case 1:
          str = '次留条件'
          break
        case 3:
          str = '关键行为'
          break
        default:
          str = '付费行为'
      }
      return str
    },
  },
  watch: {
    /*'dataForm.advertiserIds': debounce(function () {
      this.updatePercentage()
    }, 300),
    'dataForm.appId'() {
      this.updatePercentage()
    },*/
  },
  methods: {
    init(id, copy = false) {
      this.visible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()

        if (id) {
          this.isInitLoading = true
          this.$http({
            url: this.$http.adornUrl(
              `/activate/activaterules/info/${id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                for (const key in this.dataForm) {
                  const resItem = data.activateRules[key]
                  if (
                    key in data.activateRules &&
                    resItem !== undefined &&
                    resItem !== null
                  ) {
                    this.dataForm[key] = resItem
                  }
                }
                if (copy) {
                  this.dataForm.id = null
                  this.dataForm.ruleStrategyList.forEach(item => {
                    item.id = null
                    item.ruleId = null
                  })
                }
                this.formatTime()
                this.formatTime2()

                if (!this.dataForm.extra.act_type) {
                  this.dataForm.extra.act_type = 1
                }
                this.dataForm.uniqueType = this.dataForm.ruleStrategyList[0].uniqueType
                // 缓存源数据
                this.cacheResourceData = cloneDeep(this.dataForm)

                this.getDeductionData(id)

              } else {
                this.$message.error(data.msg || '服务器错误')
              }
            })
            .finally(() => {
              this.isInitLoading = false
            })
        } else {
          // 默认值写在上面会出问题
          this.dataForm.extra.con_type = 1
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      Promise.all([
        ...this.$refs.ruleStrategyListForm.map(form => form.validate()),
        this.$refs.dataForm.validate(),
        this.$refs.deductionListForm.validate(),
      ]).then(async () => {
        this.buildConditionsAndDescription();
        this.buildDelRuleStrategyIds();

        if (!this.dataForm.id && this.batchAddAdvertiserIds) {
          // 新增模式且批量
          // 处理换行分隔的输入
          const normalizedIds = this.dataForm.advertiserIds
            .replace(/[\n\r]+/g, ',')
            .replace(/,+/g, ',')
            .replace(/^,|,$/g, '');
          const ids = normalizedIds.split(',').map(id => id.trim()).filter(Boolean);
          if (ids.length === 0) {
            this.$message.error('广告账号ID不能为空');
            return;
          }
          this.$refs.dataForm.validate().then(() => {
            // 逐个提交
            const submitList = ids.map(id => {
              const formCopy = cloneDeep(this.dataForm);
              formCopy.advertiserIds = id;
              // 规则名后加广告账户ID，避免重复
              formCopy.name = `${this.dataForm.name}-${id}`;
              return this.submitData(formCopy);
            });
            Promise.all(submitList).then(results => {
              const successCount = results.filter(Boolean).length;
              this.$message.success(`批量添加完成，成功${successCount}个，失败${results.length - successCount}个`);
              this.visible = false;
              this.$emit('refreshDataList');
            });
          });
        } else {
          this.submitData(this.dataForm).then(success => {
            if (success) {
              this.$message({
                message: '操作成功',
                type: 'success',
              })
              this.visible = false
              this.$emit('refreshDataList')
            } else {
              this.$message.error('操作失败，请稍后再试')
            }
          });
        }
      });
    },
    submitData(dataForm) {
      // 创建副本并确保 advertiserIds 使用逗号分隔
      const submitForm = cloneDeep(dataForm);
      submitForm.advertiserIds = submitForm.advertiserIds
        .replace(/[\n\r]+/g, ',')
        .replace(/,+/g, ',')
        .replace(/^,|,$/g, '');

      return this.$http({
        url: this.$http.adornUrl(`/activate/activaterules/save/behavior`),
        method: 'post',
        data: this.$http.adornData(submitForm),
      }).then(async ({ data }) => {
        if (data && data.code === 0) {
          await this.saveDeductionData(submitForm, data.id)
          return true
        } else {
          return false
        }
      })
    },
    changeTime() {
      this.dataForm.convMinute =
        this.day * 24 * 60 + this.hour * 60 + this.minute
    },
    changeActConvMinute() {
      this.dataForm.actConvMinute =
        this.actConvDay * 24 * 60 + this.actConvHour * 60 + this.actConvMinute
    },
    formatTime() {
      // 小时数
      let hour = Math.floor(this.dataForm.convMinute / 60)
      // 减去小时
      let minute = this.dataForm.convMinute - hour * 60
      this.hour = hour
      this.minute = minute
    },
    formatTime2() {
      // 小时数
      let hour = Math.floor(this.dataForm.actConvMinute / 60)
      // 减去小时
      let minute = this.dataForm.actConvMinute - hour * 60
      this.actConvHour = hour
      this.actConvMinute = minute
    },
    addRule(item, index) {
      const data = cloneDeep(item)
      data.id = null
      data.extra = data.extra.map(() => 0)

      this.dataForm.ruleStrategyList.splice(index + 1, 0, data)
    },
    removeRule(item, index) {
      this.dataForm.ruleStrategyList.splice(index, 1)
    },
    buildConditionsAndDescription() {
      this.dataForm.ruleStrategyList = this.dataForm.ruleStrategyList.map(
        (it, index) => {
          let description = it.description
          let conditions = it.conditions
          let type = 1

          if (index !== 0) {
            type = 3
            const result = genSql(
              this.dataForm.extra.con_type,
              it.extra[0],
              it.extra[1],
              it.extra[2],
              it.extra[3],
              it.extra[4]
            )
            description = result.description
            conditions = result.conditions
          } else {
            type = 1
            const value1 = it.extra[0]
            const value2 = it.extra[1]
            const value3 = it.extra[2]

            const result = genSql2(this.dataForm.extra.act_type, value1, value2, value3)
            description = result.description
            conditions = result.conditions
          }

          return {
            ...it,
            type,
            description,
            conditions,
            uniqueType: this.dataForm.uniqueType
          }
        }
      )
    },
    buildMoreThanValidator(minValue) {
      return [
        ...rules,
        {
          validator: (rule, value, callback) =>
            value < minValue
              ? callback(new Error('不能小于前面的数字'))
              : callback(),
        },
      ]
    },
    // 构建删除id
    buildDelRuleStrategyIds() {
      if (
        this.cacheResourceData.ruleStrategyList &&
        this.dataForm.ruleStrategyList
      ) {
        // 原先的 id
        const resourceIds = this.cacheResourceData.ruleStrategyList
          .filter(it => it.id)
          .map(it => it.id)
        // 现存的 id
        const currentIds = this.dataForm.ruleStrategyList
          .filter(it => it.id)
          .map(it => it.id)
        // 要删除的 id
        this.dataForm.delRuleStrategyIds = difference(resourceIds, currentIds)
      }
    },
    closeRemark() {
      localStorage.setItem(localCloseKey, 'true')
    },
    getDeductionData(id) {
      this.deductionList.forEach(async it => {
        let deduction = await getDeduction({
          appId: this.dataForm.appId,
          adList: this.adList,
          type: it.type,
          ruleId: id,
        })
        if (deduction.data > 0) {
          it.data = (deduction.data * 100).toFixed(1)
        }
      })
    },
    saveDeductionData(dataForm, id) {
      // 处理换行分隔的输入
      const normalizedIds = dataForm.advertiserIds
        .replace(/[\n\r]+/g, ',')
        .replace(/,+/g, ',')
        .replace(/^,|,$/g, '');

      const taskList = this.deductionList
        .filter(it => it.data > 0)
        .map(it => {
          return addOrUpdateDeduction({
            appId: dataForm.appId,
            adList: normalizedIds.split(',').join('_'),
            type: it.type,
            percent: it.data / 100,
            ruleId: dataForm.id || id,
          })
        })
      return Promise.all(taskList)
    }
  },
}
</script>
