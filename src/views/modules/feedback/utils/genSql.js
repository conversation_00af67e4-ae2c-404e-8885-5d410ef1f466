export function genSql(conType, value1, value2, value3, value4, value5) {
  let description = ''
  let conditions = ''
  switch (conType) {
    case 1:
      description = `ecpm >= ${value1} 且 回传比例: ${value2}`
      conditions = `ecpm >= ${value1} AND rate: ${value2}`
      break
    case 2:
      description = `关键广告累计ECPM >= ${value1} 且 < ${value2} 且 激励视频次数 >= ${value3}次`
      conditions = `key_ecpm >= ${value1} AND key_ecpm < ${value2} AND reward_exposure >= ${value3}`
      break
    case 3:
      description = `关键广告累计ECPM >= ${value1} 且 < ${value2} 且 全屏视频次数 >= ${value3}次`
      conditions = `key_ecpm >= ${value1} AND key_ecpm < ${value2} AND interaction_exposure >= ${value3}`
      break
    case 4:
      description = `关键广告累计ECPM >= ${value1} 且 < ${value2} 且 (激励视频次数 >= ${value3}次 或 全屏视频次数 >= ${value4}次)`
      conditions = `key_ecpm >= ${value1} AND key_ecpm < ${value2} AND (reward_exposure >= ${value3} OR interaction_exposure >= ${value4})`
      break
    case 5:
      description = `关键广告累计ECPM >= ${value1}`
      conditions = `key_ecpm >= ${value1}`
      break
    case 6:
      description = `激励视频次数 >= ${value1}次`
      conditions = `reward_exposure >= ${value1}`
      break
    case 7:
      description = `关键广告平均ECPM >= ${value1} 且 < ${value2} 且 激励视频次数 >= ${value3}次`
      conditions = `key_avg_ecpm >= ${value1} AND key_avg_ecpm < ${value2} AND reward_exposure >= ${value3}`
      break
    case 8:
      description = `激励视频平均ECPM >= ${value1} 且 < ${value2} 且 提现次数 >= ${value3}次`
      conditions = `reward_avg_ecpm >= ${value1} AND reward_avg_ecpm < ${value2} AND withdraw_count >= ${value3}`
      break
    case 9:
      description = `关键广告平均ECPM >= ${value1} 且 < ${value2} 且 关键广告IPU >= ${value3}次`
      conditions = `key_avg_ecpm >= ${value1} AND key_avg_ecpm < ${value2} AND key_exposure >= ${value3}`
      break
    case 10:
      description = `激励视频平均ECPM >= ${value1} 且 < ${value2} 且 激励视频IPU >= ${value3}次`
      conditions = `reward_avg_ecpm >= ${value1} AND reward_avg_ecpm < ${value2} AND reward_exposure >= ${value3}`
      break

    case 11:
      description = `关键广告累计ECPM >= ${value1} 且 < ${value2} 且 观看视频 >= ${value3}次`
      conditions = `key_ecpm >= ${value1} AND key_ecpm < ${value2} AND video_success_show >= ${value3}`
      break
    case 12:
      description = `关键广告平均ECPM >= ${value1} 且 < ${value2} 且 观看视频 >= ${value3}次`
      conditions = `key_avg_ecpm >= ${value1} AND key_avg_ecpm < ${value2} AND video_success_show >= ${value3}`
      break
    case 13:
      description = `激励视频累计ECPM >= ${value1} 且 < ${value2} 且 观看视频 >= ${value3}次`
      conditions = `reward_ecpm >= ${value1} AND reward_ecpm < ${value2} AND video_success_show >= ${value3}`
      break
    case 14:
      description = `激励视频平均ECPM >= ${value1} 且 < ${value2} 且 观看视频 >= ${value3}次`
      conditions = `reward_avg_ecpm >= ${value1} AND reward_avg_ecpm < ${value2} AND video_success_show >= ${value3}`
      break

    case 15:
      description = `关键广告曝光 >= ${value1}  且 章节数 >= ${value2}`
      conditions = `key_exposure >= ${value1} AND section_watch_num >= ${value2}`
      break
    case 16:
      description = `关键广告曝光 >= ${value1}  且 答题数 >= ${value2}`
      conditions = `key_exposure >= ${value1} AND answer_num >= ${value2}`
      break
    case 17:
      description = `激励视频 >= ${value1}  且 章节数 >= ${value2}`
      conditions = `reward_exposure >= ${value1} AND section_watch_num >= ${value2}`
      break
    case 18:
      description = `提现次数 >= ${value1} 且 关键广告平均ECPM >= ${value2} 且 < ${value3} 且 激励视频IPU >= ${value4}次`
      conditions = `withdraw_count >= ${value1} AND key_avg_ecpm >= ${value2} AND key_avg_ecpm < ${value3} AND reward_exposure >= ${value4}`
      break
    case 19:
      description = `提现次数 >= ${value1} 且 激励视频平均ECPM >= ${value2} 且 < ${value3} 且 激励视频IPU >= ${value4}次`
      conditions = `withdraw_count >= ${value1} AND reward_avg_ecpm >= ${value2} AND reward_avg_ecpm < ${value3} AND reward_exposure >= ${value4}`
      break

    case 20:
      description = `激励视频平均ECPM >= ${value1} 且 < ${value2} 且 激励视频IPU >= ${value3}次 且 累计ECPM(所有广告) >= ${value4} 且 累计ECPM(所有广告)  < ${value5}`
      conditions = `reward_avg_ecpm >= ${value1} AND reward_avg_ecpm < ${value2} AND reward_exposure >= ${value3} AND exposure_ecpm >= ${value4} AND exposure_ecpm < ${value5}`
      break

    case 21:
      description = `广告曝光次数 >= ${value1}`
      conditions = `advertise_exposure >= ${value1}`
      break

    case 22:
      description = `付费次数 >= ${value1}`
      conditions = `section_watch_num >= ${value1}`
      break

    case 23:
      description = `加桌成功 且 付费页曝光 >= ${value2}`
      conditions = `add_desktop > 0 AND vip_center_show >= ${value2}`
      break

    case 24:
      description = `免费次数用完 >= ${value1}`
      conditions = `add_desktop >= ${value1}`
      break
  }

  return {
    description,
    conditions,
  }
}

export function genSql2(actType, value1, value2, value3) {
  let description = ''
  let conditions = ''

  switch (actType) {
    case 1:
      description = `到首页 >= ${value1}`
      conditions = `page_show >= ${value1}`
      break
    case 2:
      description = `心跳 >= ${value1}`
      conditions = `device_ping >= ${value1}`
      break
    case 3:
      description = `广告曝光次数 >= ${value1}`
      conditions = `exposure >= ${value1}`
      break
    case 4:
      description = `广告点击次数 >= ${value1}`
      conditions = `click >= ${value1}`
      break
    case 5:
      description = `观看时长 >= ${value1}`
      conditions = `click >= ${value1}`
      break
    case 6:
      description = `答题数 >= ${value1}`
      conditions = `answer_num >= ${value1}`
      break
    case 7:
      description = `GPT激活 >= ${value1}`
      conditions = `add_desktop >= ${value1}`
      break
    case 8:
      description = `平均ECPM >= ${value1}`
      conditions = `avg_ecpm >= ${value1}`
      break
  }

  if (value2 && actType !== 5) {
    description += ` 用户累计UP值 >= ${value2}`
    conditions += ` && reward_ecpm_first >= ${value2}`
    if (value3 > value2) {
      conditions += ` && reward_ecpm_first <= ${value3}`
    }
  }

  return {
    description,
    conditions,
  }
}
