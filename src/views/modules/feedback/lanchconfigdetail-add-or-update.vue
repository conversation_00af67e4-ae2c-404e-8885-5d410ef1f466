<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="投放配置表id" prop="configId">
      <el-input v-model="dataForm.configId" placeholder="投放配置表id"></el-input>
    </el-form-item>
    <el-form-item label="ecpm区间下限" prop="ecpmLow">
      <el-input v-model="dataForm.ecpmLow" placeholder="ecpm区间下限"></el-input>
    </el-form-item>
    <el-form-item label="ecpm区间上限" prop="ecpmHigh">
      <el-input v-model="dataForm.ecpmHigh" placeholder="ecpm区间上限"></el-input>
    </el-form-item>
    <el-form-item label="激励视频观看次数" prop="inspireVideoTimes">
      <el-input v-model="dataForm.inspireVideoTimes" placeholder="激励视频观看次数"></el-input>
    </el-form-item>
    <el-form-item label="全屏视频观看次数" prop="fullScreenTimes">
      <el-input v-model="dataForm.fullScreenTimes" placeholder="全屏视频观看次数"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updatedBy">
      <el-input v-model="dataForm.updatedBy" placeholder="更新者"></el-input>
    </el-form-item>
    <el-form-item label="创建者" prop="createdBy">
      <el-input v-model="dataForm.createdBy" placeholder="创建者"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          configId: '',
          ecpmLow: '',
          ecpmHigh: '',
          inspireVideoTimes: '',
          fullScreenTimes: '',
          createdAt: '',
          updatedAt: '',
          updatedBy: '',
          createdBy: ''
        },
        dataRule: {
          configId: [
            { required: true, message: '投放配置表id不能为空', trigger: 'blur' }
          ],
          ecpmLow: [
            { required: true, message: 'ecpm区间下限不能为空', trigger: 'blur' }
          ],
          ecpmHigh: [
            { required: true, message: 'ecpm区间上限不能为空', trigger: 'blur' }
          ],
          inspireVideoTimes: [
            { required: true, message: '激励视频观看次数不能为空', trigger: 'blur' }
          ],
          fullScreenTimes: [
            { required: true, message: '全屏视频观看次数不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          updatedBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ],
          createdBy: [
            { required: true, message: '创建者不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/feedback/lanchconfigdetail/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.configId = data.lanchConfigDetail.configId
                this.dataForm.ecpmLow = data.lanchConfigDetail.ecpmLow
                this.dataForm.ecpmHigh = data.lanchConfigDetail.ecpmHigh
                this.dataForm.inspireVideoTimes = data.lanchConfigDetail.inspireVideoTimes
                this.dataForm.fullScreenTimes = data.lanchConfigDetail.fullScreenTimes
                this.dataForm.createdAt = data.lanchConfigDetail.createdAt
                this.dataForm.updatedAt = data.lanchConfigDetail.updatedAt
                this.dataForm.updatedBy = data.lanchConfigDetail.updatedBy
                this.dataForm.createdBy = data.lanchConfigDetail.createdBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/feedback/lanchconfigdetail/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'configId': this.dataForm.configId,
                'ecpmLow': this.dataForm.ecpmLow,
                'ecpmHigh': this.dataForm.ecpmHigh,
                'inspireVideoTimes': this.dataForm.inspireVideoTimes,
                'fullScreenTimes': this.dataForm.fullScreenTimes,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt,
                'updatedBy': this.dataForm.updatedBy,
                'createdBy': this.dataForm.createdBy
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
