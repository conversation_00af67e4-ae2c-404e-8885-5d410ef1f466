<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button @click="getDataList()" type="primary" icon="el-icon-search">
          查询
        </el-button>
        <el-button
          v-if="isAuth('apps:app:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          icon="el-icon-plus"
        >
          新增
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('apps:app:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      class="adapter-height"
      :max-height="tableHeight"
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
        width="50"
      />
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="应用代码"
        width="90"
      />
      <el-table-column
        prop="packageName"
        header-align="center"
        align="center"
        label="应用包名"
        width="190"
      />
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="APP名称"
      />

      <el-table-column
        prop="platform"
        header-align="center"
        align="center"
        label="平台"
        width="75"
      >
        <template slot-scope="scope">
          <el-tag type="warning">
            {{ appPlatform.get(scope.row.platform) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="kind"
        header-align="center"
        align="center"
        label="种类"
        width="75"
      >
        <template slot-scope="scope">
          <el-tag>
            {{ appKind.get(scope.row.kind) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="category"
        header-align="center"
        align="center"
        label="类型"
        width="75"
      >
        <template slot-scope="scope">
          <el-tag type="success">
            {{ appCategory.get(scope.row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="groupId"
        header-align="center"
        align="center"
        label="所属主体"
        width="75"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.groupId"
            :type="scope.row.groupId === 1 ? 'warning' : ''"
          >
            {{ appGroup.get(scope.row.groupId) }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
        width="75"
      >
        <template slot-scope="scope">
          <el-tag type="success">
            {{ appStatus.get(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop=" genre"
        header-align="center"
        align="center"
        label="分类"
        width="75"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.genre">
            {{ appClassification.get(scope.row.genre) }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="description"
        header-align="center"
        align="center"
        label="描述"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="150"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
        width="150"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <!--<el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import {
  appCategory,
  appKind,
  appGroup,
  appStatus,
  appPlatform,
  appClassification,
} from '@/map/common'
import AddOrUpdate from './app-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      appCategory,
      appKind,
      appGroup,
      appStatus,
      appPlatform,
      appClassification,
    }
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        key: this.dataForm.key,
      }

      this.$store
        .dispatch('api/app/getAppListWithRole', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            // this.dataList = data.page.list
            this.dataList = data.apps
            // this.totalPage = data.page.totalCount
            this.totalPage = this.dataList.length
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$store.dispatch('api/app/deleteApp', ids).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
