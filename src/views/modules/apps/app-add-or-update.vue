<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="应用代码" prop="code">
            <el-input
              v-model="dataForm.code"
              :disabled="!!dataForm.id"
              placeholder="应用代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="APP名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="APP名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="APP包名" prop="package_name">
            <el-input
              v-model="dataForm.packageName"
              :disabled="!!dataForm.id"
              placeholder="APP包名"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="平台" prop="platform">
            <el-select v-model="dataForm.platform" placeholder="平台">
              <el-option
                v-for="[key, label] in appPlatform"
                :key="key"
                :label="label"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="种类" prop="kind">
            <el-select v-model="dataForm.kind" placeholder="种类">
              <el-option
                v-for="[key, label] in appKind"
                :key="key"
                :label="label"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" prop="category">
            <el-select
              v-model="dataForm.category"
              placeholder="类型"
              :disabled="!!dataForm.id"
            >
              <el-option
                v-for="[key, label] in appCategory"
                :key="key"
                :label="label"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="dataForm.status" placeholder="状态">
              <el-option
                v-for="[key, label] in appStatus"
                :key="key"
                :label="label"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="主体" prop="groupId">
            <el-select v-model="dataForm.groupId" placeholder="主体">
              <!--<el-option-->
              <!--  v-for="[key, label] in appGroup"-->
              <!--  :key="key"-->
              <!--  :label="label"-->
              <!--  :value="key"-->
              <!--/>-->
              <template v-for="key in $store.state.user.groupIdList">
                <el-option
                  v-if="key !== 0"
                  :key="key"
                  :label="appGroup.get(key)"
                  :value="key"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分类" prop="genre">
            <el-select v-model="dataForm.genre" placeholder="分类">
              <el-option
                v-for="[key, label] in appClassification"
                :key="key"
                :label="label"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="dataForm.description"
          type="textarea"
          placeholder="描述"
          :autosize="{ minRows: 6, maxRows: 16 }"
        />
      </el-form-item>
      <!--    <el-form-item label="创建时间" prop="createdAt">-->
      <!--      <el-input v-model="dataForm.createdAt" placeholder="创建时间"/>-->
      <!--    </el-form-item>-->
      <!--    <el-form-item label="更新时间" prop="updatedAt">-->
      <!--      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"/>-->
      <!--    </el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  appCategory,
  appKind,
  appGroup,
  appStatus,
  appPlatform,
  appClassification,
} from '@/map/common'
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        code: 0,
        packageName: '',
        name: '',
        description: '',
        platform: '',
        kind: '',
        category: '',
        status: '',
        groupId: this.$store.state.user.groupIdList.filter(it => it !== 0)[0],
        genre: '',
      },
      dataRule: {
        code: [
          {
            required: false,
            message: '应用代码不能为空',
            trigger: 'blur',
          },
        ],
        packageName: [
          {
            required: true,
            message: '包名不能为空',
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            message: 'APP名称不能为空',
            trigger: 'blur',
          },
        ],
        groupId: [
          {
            required: true,
            message: '主体不能为空',
            trigger: 'blur',
          },
        ],
        description: [
          {
            required: true,
            message: '描述不能为空',
            trigger: 'blur',
          },
        ],
        platform: [
          {
            required: true,
            message: '平台不能为空',
            trigger: 'blur',
          },
        ],
        kind: [
          {
            required: true,
            message: '种类不能为空',
            trigger: 'blur',
          },
        ],
        category: [
          {
            required: true,
            message: '类型不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '状态不能为空',
            trigger: 'blur',
          },
        ],
        genre: [
          {
            required: true,
            message: '分类不能为空',
            trigger: 'blur',
          },
        ],
      },
      appCategory,
      appKind,
      appGroup,
      appStatus,
      appPlatform,
      appClassification,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$store
            .dispatch('api/app/getAppInfo', this.dataForm.id)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.code = data.app.code
                this.dataForm.packageName = data.app.packageName
                this.dataForm.name = data.app.name
                this.dataForm.description = data.app.description
                this.dataForm.platform = data.app.platform
                this.dataForm.kind = data.app.kind
                this.dataForm.category = data.app.category
                this.dataForm.status = data.app.status
                this.dataForm.groupId = data.app.groupId
                this.dataForm.genre = data.app.genre
              }
            })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = {
            id: this.dataForm.id || undefined,
            code:
              this.dataForm.code < 1 ? this.dataForm.id : this.dataForm.code,
            packageName: this.dataForm.packageName,
            name: this.dataForm.name,
            description: this.dataForm.description,
            platform: this.dataForm.platform,
            kind: this.dataForm.kind,
            category: this.dataForm.category,
            status: this.dataForm.status,
            groupId: this.dataForm.groupId,
            genre: this.dataForm.genre,
          }

          this.$store
            .dispatch('api/app/addOrUpdateApp', data)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                })

                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            })
        }
      })
    },
  },
}
</script>
