<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-form-item label="渠道名" prop="channelName">
        <el-input
          v-model="dataForm.channelName"
          placeholder="渠道名"
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道号" prop="channelCode">
        <el-input
          v-model="dataForm.channelCode"
          placeholder="渠道号"
        ></el-input>
      </el-form-item>
      <el-form-item label="应用" prop="appId">
        <!--<el-input v-model="dataForm.appId" placeholder="应用自增ID"></el-input>-->
        <el-select v-model="dataForm.appId">
          <el-option
            v-for="item in appList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    appList: {
      type: Array,
    },
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        channelName: '',
        channelCode: '',
        appId: '',
        createdAt: '',
        updatedAt: '',
      },
      dataRule: {
        channelName: [
          { required: true, message: '渠道名不能为空', trigger: 'blur' },
        ],
        channelCode: [
          { required: true, message: '渠道号不能为空', trigger: 'blur' },
        ],
        appId: [
          { required: true, message: '应用自增ID不能为空', trigger: 'blur' },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        updatedAt: [
          { required: true, message: '更新时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$store
            .dispatch('api/app/getChannelInfo', this.dataForm.id)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.channelName = data.appChannel.channelName
                this.dataForm.channelCode = data.appChannel.channelCode
                this.dataForm.appId = data.appChannel.appId
                this.dataForm.createdAt = data.appChannel.createdAt
                this.dataForm.updatedAt = data.appChannel.updatedAt
              }
            })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = {
            id: this.dataForm.id || undefined,
            channelName: this.dataForm.channelName,
            channelCode: this.dataForm.channelCode,
            appId: this.dataForm.appId,
            createdAt: this.dataForm.createdAt,
            updatedAt: this.dataForm.updatedAt,
          }

          this.$store
            .dispatch('api/app/addOrUpdateChannel', data)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  },
                })
              } else {
                this.$message.error(data.msg)
              }
            })
        }
      })
    },
  },
}
</script>
