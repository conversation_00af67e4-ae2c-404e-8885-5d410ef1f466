<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="460px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="65px"
    >
      <el-form-item label="应用" prop="appId">
        <el-select v-model="dataForm.appId" style="width: 100%;">
          <el-option
            v-for="item in appList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="versionName">
        <el-input
          v-model="dataForm.versionName"
          placeholder="版本号 例子：1.0.0"
        />
      </el-form-item>
      <el-form-item label="版本码" prop="versionCode">
        <el-input v-model="dataForm.versionCode" placeholder="版本码 例子：1" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    appList: {
      type: Array,
    },
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        versionName: '',
        versionCode: '',
        appId: '',
      },
      dataRule: {
        versionName: [
          { required: true, message: '版本号不能为空', trigger: 'blur' },
        ],
        versionCode: [
          { required: true, message: '版本码不能为空', trigger: 'blur' },
        ],
        appId: [
          { required: true, message: '应用自增ID不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$store
            .dispatch('api/app/getVersionInfo', this.dataForm.id)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.versionName = data.appVersion.versionName
                this.dataForm.versionCode = data.appVersion.versionCode
                this.dataForm.appId = data.appVersion.appId
              }
            })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = {
            id: this.dataForm.id || undefined,
            versionName: this.dataForm.versionName,
            versionCode: this.dataForm.versionCode,
            appId: this.dataForm.appId,
          }
          this.$store
            .dispatch('api/app/addOrUpdateVersion', data)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  },
                })
              } else {
                this.$message.error(data.msg)
              }
            })
        }
      })
    },
  },
}
</script>
