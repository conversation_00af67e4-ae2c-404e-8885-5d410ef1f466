<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="760px"
    @closed="$emit('closed')"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="100px"
    >
      <el-form-item label="用户名" prop="userName">
        <el-input v-model="dataForm.userName" placeholder="登录帐号" />
      </el-form-item>
      <el-form-item
        label="密码"
        prop="password"
        :class="{ 'is-required': !dataForm.id }"
      >
        <el-input
          v-model="dataForm.password"
          type="password"
          placeholder="密码"
        />
      </el-form-item>
      <el-form-item
        label="确认密码"
        prop="comfirmPassword"
        :class="{ 'is-required': !dataForm.id }"
      >
        <el-input
          v-model="dataForm.comfirmPassword"
          type="password"
          placeholder="确认密码"
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="dataForm.email" placeholder="邮箱" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="dataForm.mobile" placeholder="手机号" />
      </el-form-item>
      <el-form-item label="角色" size="mini" prop="roleIdList">
        <el-checkbox-group v-model="dataForm.roleIdList">
          <el-checkbox
            v-for="role in roleList"
            :key="role.roleId"
            :label="role.roleId"
          >
            {{ role.roleName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!--<el-form-item label="主体" size="mini" prop="groupIdList">-->
      <!--  <el-checkbox-->
      <!--    :indeterminate="isIndeterminate"-->
      <!--    v-model="checkAll"-->
      <!--    @change="handleCheckAllChange"-->
      <!--  >-->
      <!--    全部-->
      <!--  </el-checkbox>-->
      <!--  <el-checkbox-group v-model="dataForm.groupIdList">-->
      <!--    <el-checkbox v-for="[key, label] in appGroup" :label="key" :key="key">-->
      <!--      {{ label }}-->
      <!--    </el-checkbox>-->
      <!--  </el-checkbox-group>-->
      <!--</el-form-item>-->
      <el-form-item prop="appIdList" label="授权APP">
        <!--:default-expanded-keys="defaultExpandedKeys"-->
        <el-tree
          ref="appListTree"
          :data="appList"
          :props="defaultProps"
          @node-click="handleNodeClick"
          show-checkbox
          node-key="key"
          :default-checked-keys="defaultCheckedKeys"
          :default-expanded-keys="false"
        />
      </el-form-item>
      <el-form-item prop="accountIdList" label="授权投放账户">
        <!--:default-expanded-keys="defaultExpandedKeys"-->
        <el-tree
          ref="accountIdListTree"
          :data="accountIdList"
          :props="defaultProps"
          @node-click="handleNodeClick"
          show-checkbox
          node-key="key"
          :default-checked-keys="defaultCheckedAccountIds"
          :default-expanded-keys="false"
        />
      </el-form-item>
      <el-form-item label="状态" size="mini" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">禁用</el-radio>
          <el-radio :label="1">正常</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmail, isMobile } from '@/utils/validate'
import { appGroup, appGroupWithAll } from '@/map/common'

export default {
  data() {
    const validatePassword = (rule, value, callback) => {
      if (!this.dataForm.id && !/\S/.test(value)) {
        callback(new Error('密码不能为空'))
      } else {
        callback()
      }
    }
    const validateComfirmPassword = (rule, value, callback) => {
      if (!this.dataForm.id && !/\S/.test(value)) {
        callback(new Error('确认密码不能为空'))
      } else if (this.dataForm.password !== value) {
        callback(new Error('确认密码与密码输入不一致'))
      } else {
        callback()
      }
    }
    const validateEmail = (rule, value, callback) => {
      if (!isEmail(value)) {
        callback(new Error('邮箱格式错误'))
      } else {
        callback()
      }
    }
    const validateMobile = (rule, value, callback) => {
      if (!isMobile(value)) {
        callback(new Error('手机号格式错误'))
      } else {
        callback()
      }
    }
    return {
      isIndeterminate: false,
      checkAll: false,
      visible: false,
      roleList: [],
      appGroupWithAll,
      appGroup,
      dataForm: {
        id: 0,
        userName: '',
        password: '',
        comfirmPassword: '',
        salt: '',
        email: '',
        mobile: '',
        roleIdList: [],
        status: 1,
        groupIdList: [],
        appIdList: [],
        accountIdList: [],
      },
      dataRule: {
        userName: [
          { required: true, message: '用户名不能为空', trigger: 'blur' },
        ],
        appIdList: [{ required: true, message: '授权APP不能为空', trigger: 'blur' }],
        password: [{ validator: validatePassword, trigger: 'blur' }],
        groupIdList: [
          { trigger: 'blur', required: true, message: '主体不能为空' },
        ],
        comfirmPassword: [
          { validator: validateComfirmPassword, trigger: 'blur' },
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' },
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' },
        ],
      },
      appList: [
        {
          name: '全部',
          id: 0,
          groupId: 0,
          isGroup: true,
          key: 'group-id-0',
          children: [],
        },
      ],
      accountIdList: [
      {
          name: '全部',
          id: 0,
          groupId: 0,
          isGroup: true,
          key: 'account-id-all',
          children: [],
        },
      ],
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      defaultCheckedAccountIds: [],
      allGroupMap: [],
    }
  },
  watch: {
    'dataForm.groupIdList'(value) {
      let checkedCount = value.length

      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.appGroup.size
      this.checkAll = checkedCount === this.appGroup.size
    },
  },
  methods: {
    handleNodeClick(data) {
      console.log(data)
    },
    async init(id) {
      await this.getAppList()
      await this.getAccountIdList()

      this.dataForm.id = id || 0
      this.$http({
        url: this.$http.adornUrl('/sys/role/select'),
        method: 'get',
        params: this.$http.adornParams(),
      })
        .then(({ data }) => {
          this.roleList = data && data.code === 0 ? data.list : []
        })
        .then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm'].resetFields()
          })
        })
        .then(() => {
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/sys/user/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams(),
            }).then(({ data }) => {
              if (data && data.code === 0) {
                let groupIdList = []
                if (data.user.groupIdList) {
                  if (data.user.groupIdList.includes(0)) {
                    let s = new Set(data.user.groupIdList)
                    s.delete(0)
                    groupIdList = Array.from(s)
                  } else {
                    groupIdList = data.user.groupIdList
                  }
                }
                this.dataForm.userName = data.user.username
                this.dataForm.salt = data.user.salt
                this.dataForm.email = data.user.email
                this.dataForm.mobile = data.user.mobile
                this.dataForm.roleIdList = data.user.roleIdList
                this.dataForm.status = data.user.status
                this.dataForm.groupIdList = groupIdList
                this.dataForm.appIdList = data.user.appIdList
                this.dataForm.accountIdList = data.user.accountIdList

                this.defaultExpandedKeys = this.dataForm.groupIdList.map(
                  it => `group-id-${it}`
                )
                this.defaultCheckedKeys = this.dataForm.appIdList.map(
                  it => `app-id-${it}`
                )
                this.defaultCheckedAccountIds = this.dataForm.accountIdList.map(
                  it => `account-id-${it}`
                )
                this.dataForm.groupIdList.forEach(it => {
                  if (
                    this.allGroupMap[it] &&
                    this.allGroupMap[it].length === 0
                  ) {
                    this.defaultCheckedKeys.push('group-id-' + it)
                  }
                })
                console.log('defaultExpandedKeys: ', this.defaultExpandedKeys)
                console.log('defaultCheckedKeys: ', this.defaultCheckedKeys)
                console.log('defaultCheckedAccountIds: ', this.defaultCheckedAccountIds)
              }
            })
          }
        })
    },
    // 表单提交
    dataFormSubmit() {
      const checkedNodes = this.$refs.appListTree.getCheckedNodes()
      console.log('checkedNodes: ', checkedNodes)
      console.log('save user groupIdList 1: ', this.dataForm.groupIdList)
      if (checkedNodes) {
        this.dataForm.groupIdList = checkedNodes
          .map(it => (it.isGroup ? it.id : it.groupId))
          .filter((it, index, arr) => arr.indexOf(it) === index && it)
        console.log('this.dataForm.groupIdList::', this.dataForm.groupIdList)
        if (
          this.dataForm.groupIdList.length ===
          Array.from(appGroup.keys()).length
        ) {
          this.dataForm.groupIdList.unshift(0)
        }
        this.dataForm.appIdList = checkedNodes
          .filter(it => !it.isGroup)
          .map(it => it.code)
      }

      const checkedAccountIdNodes = this.$refs.accountIdListTree.getCheckedNodes()
      console.log('checkedAccountIdNodes: ', checkedAccountIdNodes)
      console.log('save user accountIdList 1: ', this.dataForm.accountIdList)
      if (checkedAccountIdNodes) {
        this.dataForm.accountIdList = checkedAccountIdNodes
          .filter(it => !it.isGroup)
          .map(it => (it.id))
        console.log('this.dataForm.accountIdList::', this.dataForm.accountIdList)
      }

      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          // if (this.checkAll) {
          //   this.dataForm.groupIdList.unshift(0)
          // }
          console.log('save user groupIdList 2: ', this.dataForm.groupIdList)

          this.$http({
            url: this.$http.adornUrl(
              `/sys/user/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              userId: this.dataForm.id || undefined,
              username: this.dataForm.userName,
              password: this.dataForm.password,
              salt: this.dataForm.salt,
              email: this.dataForm.email,
              mobile: this.dataForm.mobile,
              status: this.dataForm.status,
              roleIdList: this.dataForm.roleIdList,
              groupIdList: this.dataForm.groupIdList,
              appIdList: this.dataForm.appIdList,
              accountIdList: this.dataForm.accountIdList,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                  this.$store.commit(
                    'user/updateGroupIdList',
                    this.dataForm.groupIdList
                  )
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    handleCheckAllChange(val) {
      this.dataForm.groupIdList = val ? Array.from(this.appGroup.keys()) : []
    },
    async getAppList() {
      return this.$store
        .dispatch('api/app/getAppList', {
          page: 1,
          limit: 1000,
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            const group = {}
            appGroup.forEach((_, key) => (group[key] = []))
            data.page.list.forEach(it => {
              const item = { ...it, key: `app-id-${it.code}` }
              if (group[it.groupId]) {
                group[it.groupId].push(item)
              } else {
                group[it.groupId] = [item]
              }
            })
            console.log('group', group)
            for (const key in group) {
              const groupKey = Number(key)
              this.appList[0].children.push({
                name: appGroup.get(groupKey),
                id: groupKey,
                key: `group-id-${groupKey}`,
                isGroup: true,
                children: group[key],
              })
            }
            this.allGroupMap = group
            console.log('appList:', this.appList)
          }
        })
    },
    async getAccountIdList() {
      return this.$store
        .dispatch('api/app/getAccountIdList', {
          page: 1,
          limit: 1000,
          appId: '',
          channel: '',
          brand: '',
          accountId: ''
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            let accountIds = [...new Set(data.page.list.map(it => it.accountIds))]
            accountIds.forEach(it => {
              this.accountIdList[0].children.push({
                name: it,
                id: it,
                key: `account-id-${it}`,
                isGroup: false,
              })
            })
            console.log('accountIdList:', this.accountIdList)
          }
        })
    },
  },
}
</script>
