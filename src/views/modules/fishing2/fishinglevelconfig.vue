<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="230"
  />
</template>

<script>
import {
  fishingLevelConfig as request,
  baseConfigRequest,
} from '@/api/fishing2'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'level', title: '等级' },
          { field: 'experience', title: '经验' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '60px',
        },
        formData: {
          id: null,
          level: '',
          experience: '',
        },
        formItemMap: {
          level: {
            title: '概率',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          experience: {
            title: '经验',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
        },
        formRule: {
          level: [{ required: true, message: '不能为空', trigger: 'blur' }],
          experience: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
  activated() {
    baseConfigRequest
      .selectAll({ currentPage: 1, pageSize: 10000 })
      .then(res => {
        if (res.code === 0 && res.page && res.page.list) {
          this.modelConfig.formItemMap.fishId.itemRender.attrs.list =
            res.page.list
          this.fishList = res.page.list
        }
      })
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
