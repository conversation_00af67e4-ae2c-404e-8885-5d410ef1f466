<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
    ]"
    operate-width="330"
  >
    <template #table_item_type="{row}">
      <color-tag :id="row.type">
        {{ withdrawalConfigMap.typeList.get(row.type) }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_needAudit="{row}">
      <tag-status
        :status="row.needAudit"
        activeText="需要"
        inactiveText="不需要"
      />
    </template>
  </page-table>
</template>

<script>
import { withdrawalConfigRequest as request } from '@/api/fishing2'
import { withdrawalConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      withdrawalConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          {
            field: 'type',
            title: '类型',
            slots: {
              default: 'table_item_type',
            },
          },
          { field: 'amount', title: '额度' },
          { field: 'fishingNum', title: '需要钓鱼数' },
          { field: 'loginDays', title: '需要登录天数' },
          { field: 'level', title: '需要等级' },
          {
            field: 'needAudit',
            title: '是否需要审核',
            slots: {
              default: 'table_item_needAudit',
            },
          },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '110px',
        },
        formData: {
          id: null,
          type: '',
          amount: '',
          fishingNum: '',
          loginDays: '',
          level: '',
          needAudit: '',
          status: '',
        },
        formItemMap: {
          type: {
            title: '类型',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: withdrawalConfigMap.typeList,
              },
            },
          },
          amount: {
            title: '额度',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          fishingNum: {
            title: '需要钓鱼数',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          loginDays: {
            title: '需要登录天数',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          level: {
            title: '需要等级',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          needAudit: {
            title: '是否需要审核',
            itemRender: {
              name: 'radio-status',
              attrs: {
                inactiveText: '不需要',
                activeText: '需要',
              },
            },
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
        },
        formRule: {
          type: [{ required: true, message: '不能为空', trigger: 'blur' }],
          amount: [{ required: true, message: '不能为空', trigger: 'blur' }],
          fishingNum: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          loginDays: [{ required: true, message: '不能为空', trigger: 'blur' }],
          level: [{ required: true, message: '不能为空', trigger: 'blur' }],
          needAudit: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
}
</script>
