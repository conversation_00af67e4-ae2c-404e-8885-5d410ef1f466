<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :features="['select']"
    :show-operate="false"
  >
    <template #table_item_operatorType="{row}">
      <span>{{ userCoinRecordMap.operateList.get(row.operatorType) }}</span>
    </template>
  </page-table>
</template>

<script>
import { fishingUserCoinRecord as request } from '@/api/fishing2'
import { drawConfigMap, userCoinRecordMap } from '@/map/fishing'

export default {
  data() {
    return {
      userCoinRecordMap,
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'coinSerialNo', title: '金币流水编号', width: 200 },
          { field: 'relatedCoinSerialNo', title: '奖励关联的金币流水编号' },
          { field: 'userId', title: '用户表id' },
          { field: 'appCode', title: '应用 code' },
          { field: 'coins', title: '获得金币数，负数为扣除金币' },
          {
            field: 'operatorType',
            title: '金币操作类型',
            slots: {
              default: 'table_item_operatorType',
            },
          },
          { field: 'ecpm', title: '广告返回的ecpm' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
        formConfig: {
          items: [
            {
              field: 'userId',
              title: '用户ID',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
          ],
        },
      },
    }
  },
}
</script>
