<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="330"
  >
    <template #table_item_type="{row}">
      <color-tag :id="row.drawType">
        {{ drawConfigMap.typeList.get(row.drawType) }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_fishId="{row}">
      <color-tag :id="row.fishId">
        {{ getFishName(row.fishId) }}
      </color-tag>
    </template>
  </page-table>
</template>

<script>
import { businessConfig as request, baseConfigRequest } from '@/api/fishing2'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'configKey', title: '参数名' },
          // { field: 'configValue', title: '参数值' },
          { field: 'configDesc', title: '参数描述' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
          labelPosition: 'top',
        },
        formData: {
          id: null,
          configKey: '',
          configValue: '',
          configDesc: '',
        },
        formItemMap: {
          configKey: {
            title: '参数名',
          },
          configValue: {
            title: '参数值',
            itemRender: {
              name: 'ConfigJsonEditor',
              attrs: {
                height: 500,
              },
            },
          },
          configDesc: {
            title: '描述',
            itemRender: {
              attrs: {
                type: 'textarea',
              },
            },
          },
        },
        formRule: {
          configKey: [{ required: true, message: '不能为空', trigger: 'blur' }],
          configValue: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
  activated() {
    baseConfigRequest
      .selectAll({ currentPage: 1, pageSize: 10000 })
      .then(res => {
        if (res.code === 0 && res.page && res.page.list) {
          this.modelConfig.formItemMap.fishId.itemRender.attrs.list =
            res.page.list
          this.fishList = res.page.list
        }
      })
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
