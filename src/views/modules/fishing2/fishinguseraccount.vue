<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="request"
      :features="[
        // 'delete',
        // 'insert',
        // 'update',
        // 'batch_offline',
        // 'batch_online',
        // 'offline',
        // 'online',
        'select',
      ]"
      operate-width="100"
    >
      <template #table_item_type="{row}">
        <color-tag :id="row.drawType">
          {{ drawConfigMap.typeList.get(row.drawType) }}
        </color-tag>
      </template>
      <template #table_item_rewardFishType="{row}">
        <tag-status
          :status="row.rewardFishType"
          active-text="是"
          inactive-text="否"
        />
      </template>
      <template #table_item_rewardType="{row}">
        <color-tag :id="row.rewardType">
          {{ baseConfigMap.rewardType.get(row.rewardType) }}
        </color-tag>
      </template>
      <template #table_item_icon="{row}">
        <img width="80" :src="row.icon" alt="" />
      </template>

      <template #custom_table_operate="{row}">
        <el-button type="text" @click="getDataDetail(row)">查看</el-button>
      </template>
    </page-table>
    <el-dialog title="详细信息" :visible.sync="dialogVisible">
      <el-descriptions border size="medium" direction="vertical">
        <el-descriptions-item label="累计ecpm">
          {{ itemInfo.exposureEcpm }}
        </el-descriptions-item>
        <el-descriptions-item label="累计总ipu">
          {{ itemInfo.totalIpu }}
        </el-descriptions-item>
        <el-descriptions-item label="激励视频ipu">
          {{ itemInfo.videoIpu }}
        </el-descriptions-item>
        <el-descriptions-item label="提现金额">
          {{ itemInfo.withdrawalAmount }}
        </el-descriptions-item>
        <el-descriptions-item label="钓鱼总数">
          {{ itemInfo.fishingNum }}
        </el-descriptions-item>
        <el-descriptions-item label="当前金宝鱼数量">
          {{ itemInfo.goldFishCur }}
        </el-descriptions-item>
        <el-descriptions-item label="累计金宝鱼数量">
          {{ itemInfo.goldFishTotal }}
        </el-descriptions-item>
        <el-descriptions-item label="累计登录天数">
          {{ itemInfo.loginDays }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { fishingUserAccount as request } from '@/api/fishing2'
import { baseConfigMap, drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      dialogVisible: false,
      itemInfo: {},
      fishList: [],
      drawConfigMap,
      baseConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'seq', title: '序号', minWidth: 60 },
          { field: 'userId', title: '用户ID', minWidth: 120 },
          { field: 'coinBalance', title: '账户金币余额' },
          { field: 'appVersion', title: '版本' },
          // { field: 'level', title: '用户等级', width: 120 },
          // { field: 'levelProcess', title: '当前用户等级', width: 120 },
          { field: 'createdAt', title: '创建时间' },
          { field: 'updatedAt', title: '更新时间' },
        ],
        formConfig: {
          items: [
            {
              field: 'userId',
              title: '用户ID',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
          ],
        },
      },
    }
  },
  methods: {
    getDataDetail(row) {
      this.dialogVisible = true
      request.selectItem(row.userId).then(res => {
        if (res.code === 0 && res.__data__) {
          this.itemInfo = Object.assign({}, res.__data__)
        }
      })
    },
  },
}
</script>
