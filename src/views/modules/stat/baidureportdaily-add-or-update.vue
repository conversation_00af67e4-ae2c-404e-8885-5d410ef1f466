<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      disabled
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-form-item label="后台应用" prop="appCode">
        <el-input v-model="dataForm.appCode" placeholder="后台应用app_code" />
      </el-form-item>
      <el-form-item label="应用类型" prop="appTypeName">
        <el-input v-model="dataForm.appTypeName" placeholder="应用类型" />
      </el-form-item>
      <el-form-item label="代码位类型" prop="adTypeName">
        <el-input v-model="dataForm.adTypeName" placeholder="代码位类型" />
      </el-form-item>
      <el-form-item label=" appsid" prop="appId">
        <el-input v-model="dataForm.appId" placeholder=" appsid" />
      </el-form-item>
      <el-form-item label="appName" prop="appName">
        <el-input v-model="dataForm.appName" placeholder="appName" />
      </el-form-item>
      <el-form-item label="系统" prop="systemName">
        <el-input v-model="dataForm.systemName" placeholder="系统" />
      </el-form-item>
      <el-form-item label="代码位id" prop="adPositionId">
        <el-input v-model="dataForm.adPositionId" placeholder="代码位id" />
      </el-form-item>
      <el-form-item label="代码位名称" prop="adPositionName">
        <el-input v-model="dataForm.adPositionName" placeholder="代码位名称" />
      </el-form-item>
      <el-form-item label="代码位展现量" prop="view">
        <el-input v-model="dataForm.view" placeholder="代码位展现量" />
      </el-form-item>
      <el-form-item label="预计收入" prop="income">
        <el-input v-model="dataForm.income" placeholder="预计收入" />
      </el-form-item>
      <el-form-item label="点击量" prop="click">
        <el-input v-model="dataForm.click" placeholder="点击量" />
      </el-form-item>
      <el-form-item label="eCPM" prop="ecpm">
        <el-input v-model="dataForm.ecpm" placeholder="eCPM" />
      </el-form-item>
      <el-form-item label="点击率" prop="ctr">
        <el-input v-model="dataForm.ctr" placeholder="点击率" />
      </el-form-item>
      <el-form-item label="CPC" prop="cpc">
        <el-input v-model="dataForm.cpc" placeholder="CPC" />
      </el-form-item>
      <el-form-item label="填充率" prop="fillratio">
        <el-input v-model="dataForm.fillratio" placeholder="填充率" />
      </el-form-item>
      <el-form-item label="请求" prop="request">
        <el-input v-model="dataForm.request" placeholder="请求" />
      </el-form-item>
      <el-form-item label="有效请求" prop="effectRequest">
        <el-input v-model="dataForm.effectRequest" placeholder="有效请求" />
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input v-model="dataForm.createdAt" placeholder="" />
      </el-form-item>
      <el-form-item label="日期" prop="day">
        <el-input v-model="dataForm.day" placeholder="日期：数值：yyyyMMdd" />
      </el-form-item>
      <el-form-item label="日期" prop="time">
        <el-input v-model="dataForm.time" placeholder="日期，格式：yyyyMMd" />
      </el-form-item>
    </el-form>
    <!--<span slot="footer" class="dialog-footer">-->
    <!--  <el-button @click="visible = false">取消</el-button>-->
    <!--  <el-button type="primary" @click="dataFormSubmit()">确定</el-button>-->
    <!--</span>-->
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appCode: '',
        appTypeName: '',
        adTypeName: '',
        appId: '',
        appName: '',
        systemName: '',
        adPositionId: '',
        adPositionName: '',
        view: '',
        income: '',
        click: '',
        ecpm: '',
        ctr: '',
        cpc: '',
        fillratio: '',
        request: '',
        effectRequest: '',
        createdAt: '',
        day: '',
        time: '',
      },
      dataRule: {
        appCode: [
          {
            required: true,
            message: '后台应用app_code不能为空',
            trigger: 'blur',
          },
        ],
        appTypeName: [
          { required: true, message: '应用类型不能为空', trigger: 'blur' },
        ],
        adTypeName: [
          { required: true, message: '代码位类型不能为空', trigger: 'blur' },
        ],
        appId: [
          { required: true, message: ' appsid不能为空', trigger: 'blur' },
        ],
        appName: [
          { required: true, message: 'appName不能为空', trigger: 'blur' },
        ],
        systemName: [
          { required: true, message: '系统不能为空', trigger: 'blur' },
        ],
        adPositionId: [
          { required: true, message: '代码位id不能为空', trigger: 'blur' },
        ],
        adPositionName: [
          { required: true, message: '代码位名称不能为空', trigger: 'blur' },
        ],
        view: [
          { required: true, message: '代码位展现量不能为空', trigger: 'blur' },
        ],
        income: [
          { required: true, message: '预计收入不能为空', trigger: 'blur' },
        ],
        click: [{ required: true, message: '点击量不能为空', trigger: 'blur' }],
        ecpm: [{ required: true, message: 'eCPM不能为空', trigger: 'blur' }],
        ctr: [{ required: true, message: '点击率不能为空', trigger: 'blur' }],
        cpc: [{ required: true, message: 'CPC不能为空', trigger: 'blur' }],
        fillratio: [
          { required: true, message: '填充率不能为空', trigger: 'blur' },
        ],
        request: [{ required: true, message: '请求不能为空', trigger: 'blur' }],
        effectRequest: [
          { required: true, message: '有效请求不能为空', trigger: 'blur' },
        ],
        createdAt: [{ required: true, message: '不能为空', trigger: 'blur' }],
        day: [
          {
            required: true,
            message: '日期：数值：yyyyMMdd不能为空',
            trigger: 'blur',
          },
        ],
        time: [
          {
            required: true,
            message: '日期，格式：yyyyMMd不能为空',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/baidureportdaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appCode = data.baiduReportDaily.appCode
              this.dataForm.appTypeName = data.baiduReportDaily.appTypeName
              this.dataForm.adTypeName = data.baiduReportDaily.adTypeName
              this.dataForm.appId = data.baiduReportDaily.appId
              this.dataForm.appName = data.baiduReportDaily.appName
              this.dataForm.systemName = data.baiduReportDaily.systemName
              this.dataForm.adPositionId = data.baiduReportDaily.adPositionId
              this.dataForm.adPositionName =
                data.baiduReportDaily.adPositionName
              this.dataForm.view = data.baiduReportDaily.view
              this.dataForm.income = data.baiduReportDaily.income
              this.dataForm.click = data.baiduReportDaily.click
              this.dataForm.ecpm = data.baiduReportDaily.ecpm
              this.dataForm.ctr = data.baiduReportDaily.ctr
              this.dataForm.cpc = data.baiduReportDaily.cpc
              this.dataForm.fillratio = data.baiduReportDaily.fillratio
              this.dataForm.request = data.baiduReportDaily.request
              this.dataForm.effectRequest = data.baiduReportDaily.effectRequest
              this.dataForm.createdAt = data.baiduReportDaily.createdAt
              this.dataForm.day = data.baiduReportDaily.day
              this.dataForm.time = data.baiduReportDaily.time
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/baidureportdaily/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appCode: this.dataForm.appCode,
              appTypeName: this.dataForm.appTypeName,
              adTypeName: this.dataForm.adTypeName,
              appId: this.dataForm.appId,
              appName: this.dataForm.appName,
              systemName: this.dataForm.systemName,
              adPositionId: this.dataForm.adPositionId,
              adPositionName: this.dataForm.adPositionName,
              view: this.dataForm.view,
              income: this.dataForm.income,
              click: this.dataForm.click,
              ecpm: this.dataForm.ecpm,
              ctr: this.dataForm.ctr,
              cpc: this.dataForm.cpc,
              fillratio: this.dataForm.fillratio,
              request: this.dataForm.request,
              effectRequest: this.dataForm.effectRequest,
              createdAt: this.dataForm.createdAt,
              day: this.dataForm.day,
              time: this.dataForm.time,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
