<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item>
        <el-input
          v-model="dataForm.key"
          placeholder="参数名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          v-if="isAuth('stat:subscriptionkeep:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('stat:subscriptionkeep:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label=""
      ></el-table-column>
      <el-table-column
        prop="dt"
        header-align="center"
        align="center"
        label="日期"
      ></el-table-column>
      <el-table-column
        prop="subNum"
        header-align="center"
        align="center"
        label="订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week2"
        header-align="center"
        align="center"
        label="week2订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week3"
        header-align="center"
        align="center"
        label="week3订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week4"
        header-align="center"
        align="center"
        label="week4订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week5"
        header-align="center"
        align="center"
        label="week5订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week6"
        header-align="center"
        align="center"
        label="week6订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week7"
        header-align="center"
        align="center"
        label="week7订阅人数"
      ></el-table-column>
      <el-table-column
        prop="week8"
        header-align="center"
        align="center"
        label="week8订阅人数"
      ></el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      ></el-table-column>
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      ></el-table-column>
      <!-- <el-table-column
        prop="cost"
        header-align="center"
        align="center"
        label="成本"
      ></el-table-column>
      <el-table-column
        prop="amount"
        header-align="center"
        align="center"
        label="累计金额"
      ></el-table-column>
      <el-table-column
        prop="roi"
        header-align="center"
        align="center"
        label="roi"
      ></el-table-column> -->
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './subscriptionkeep-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'
export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
    }
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/subscriptionkeep/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalPage = data.page.totalCount
          let list = data.page.list
          if (list && list.length) {
            list.forEach(v => {
              for (let i = 2; i <= 8; i++) {
                let key = `week${i}`
                v[key] &&
                  (v[key] = `${v[key]}/${((v[key] / v.subNum) * 100).toFixed(
                    2
                  )}%`)
              }
            })
          }
          this.dataList = list
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/subscriptionkeep/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
