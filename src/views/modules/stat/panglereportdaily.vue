<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="选择应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
          :is-store="true"
        />
      </el-form-item>
      <el-form-item label="是否总收入">
        <el-select v-model="dataForm.isSummary" clearable>
          <el-option
            v-for="[key, label] in isSummaryStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="selectTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyyMMdd"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:panglereportdaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('stat:panglereportdaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      class="adapter-height"
      :max-height="tableHeight"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="id"
        width="50"
      />
      <!--<el-table-column-->
      <!--  prop="appCode"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="系统应用code"-->
      <!--/>-->
      <el-table-column
        prop="startDate"
        header-align="center"
        align="center"
        label="开始时间"
        width="100"
      />
      <el-table-column
        prop="endDate"
        header-align="center"
        align="center"
        label="结束时间"
        width="100"
      />
      <el-table-column
        prop="revenue"
        header-align="center"
        align="center"
        label="预估收益"
      />
      <el-table-column
        prop="clkCnt"
        header-align="center"
        align="center"
        label="点击量"
      />
      <el-table-column
        prop="isSummary"
        header-align="center"
        align="center"
        label="是否总收入"
        width="100"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.isSummary === 1 ? 'danger' : ''">
            {{ isSummaryStatus.get(row.isSummary) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="clkRate"
        header-align="center"
        align="center"
        label="点击率"
      />
      <el-table-column
        prop="ecpm"
        header-align="center"
        align="center"
        label="Ecpm"
      />
      <el-table-column
        prop="ipmCnt"
        header-align="center"
        align="center"
        label="展现量"
      />
      <el-table-column
        prop="siteName"
        header-align="center"
        align="center"
        label="应用名称"
        width="160"
      />
      <el-table-column
        prop="siteId"
        header-align="center"
        align="center"
        label="应用id"
      />
      <el-table-column
        prop="currency"
        header-align="center"
        align="center"
        label="Currency"
        width="100"
      />
      <el-table-column
        prop="os"
        header-align="center"
        align="center"
        label="系统类型"
      />
      <el-table-column
        prop="codeName"
        header-align="center"
        align="center"
        label="CodeName"
        width="100"
      />
      <el-table-column
        prop="codeId"
        header-align="center"
        align="center"
        label="CodeId"
      />
      <el-table-column
        prop="codeType"
        header-align="center"
        align="center"
        label="CodeType"
        width="100"
      />
      <el-table-column
        prop="biddingType"
        header-align="center"
        align="center"
        label="BiddingType"
        width="110"
      />
      <!--<el-table-column-->
      <!--  prop="day"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="数值日期"-->
      <!--/>-->
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建日期"
        width="160"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            查看
          </el-button>
          <!--<el-button-->
          <!--  type="text"-->
          <!--  size="small"-->
          <!--  @click="deleteHandle(scope.row.id)"-->
          <!--&gt;-->
          <!--  删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './panglereportdaily-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'

import { isSummaryStatus } from '@/map/common'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
        isSummary: 1,
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      isSummaryStatus,
      selectTime: '',
    }
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)

      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/panglereportdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: res.code, // 选择全部应用时，有些地方需要传0，有些却要空字符串
          startTime: this.selectTime ? this.selectTime[0] ?? '' : '',
          endTime: this.selectTime ? this.selectTime[1] ?? '' : '',
          isSummary: this.dataForm.isSummary ?? '',
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/panglereportdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
