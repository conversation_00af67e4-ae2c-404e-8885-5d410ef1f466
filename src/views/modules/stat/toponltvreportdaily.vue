<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    operate-width="180"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appId"
        :is-show-all="false"
        clearable
        :app-filter="appFilter"
      />
    </template>
    <template #form_day>
      <el-date-picker
        v-model="selectFormData.dayRange"
        type="daterange"
        placeholder="选择日期"
        value-format="yyyyMMdd"
      />
    </template>
    <template #form_country>
      <country-select
        v-model="selectFormData.country"
        :filter="countryFilter"
        :has-all="false"
      />
    </template>
    <template #form_channel>
      <arr-select
        :list="channelList"
        value-key="value"
        label-key="label"
        v-model="selectFormData.channel"
      />
    </template>
    <template #table_item_appCode="{row}">
      <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
    </template>
    <template #table_item_day="{row}">
      <span>{{ row.day | formatDate }}</span>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>
  </page-table>
</template>

<script>
import { topOnLtvReportDailyRequest as request } from '@/api/stat'
import CountrySelect from '@/components/country-select'
import { appFilter } from '@/utils/bussiness'
import { countryList } from '@/map/sat'

const ltvDayKeys = []

for (let i = 1; i < 60; i++) {
  const keyLtv = 'ltvDay' + i
  const roiLtv = 'roiDay' + i
  ltvDayKeys.push({
    field: 'ltvDay' + i,
    title: keyLtv + '/' + roiLtv,
    minWidth: 150,
    formatter: ({ row }) => {
      const roi = row[roiLtv] ? (row[roiLtv] * 1000) / 10 + '%' : ''
      return [row[keyLtv], roi].map(it => it || '-').join('/')
    },
  })
}

export default {
  components: {
    CountrySelect,
  },
  data() {
    return {
      channelList: [
        { label: 'Mintegral-oppo', value: 'Mintegral-oppo' },
        { label: 'Mintegral-vivo', value: 'Mintegral-vivo' },
        { label: 'huawei', value: 'huawei' },
        { label: 'Mintegral-amzon', value: 'Mintegral-amzon' },
      ],
      fishList: [],
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 160,
            slots: {
              default: 'table_item_appCode',
            },
          },
          {
            field: 'day',
            title: '日期',
            minWidth: 100,
            slots: {
              default: 'table_item_day',
            },
          },
          {
            field: 'country',
            title: '国家',
            minWidth: 60,
            formatter({ cellValue }) {
              const item = countryList.find(it => it.country === cellValue)
              return item ? item.country_name : ''
            },
          },
          { field: 'cost', title: '消耗', minWidth: 90 },
          { field: 'adConversion', title: '新增媒体', minWidth: 90 },
          { field: 'cpi', title: 'cpi', minWidth: 60 },
          { field: 'newUser', title: 'topOn新增', minWidth: 100 },
          { field: 'retentionDay2', title: '次留', minWidth: 60 },
          { field: 'retentionDay3', title: '3留', minWidth: 60 },
          { field: 'retentionDay7', title: '7留', minWidth: 60 },
          // { field: 'countryName', title: '国家中文名称', minWidth: 120 },
          ...ltvDayKeys,
          // ...roiDayKeys,
          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          { field: 'updatedAt', title: '更新时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              title: '日器',
              slots: {
                default: 'form_day',
              },
            },
            {
              title: '国家',
              slots: {
                default: 'form_country',
              },
            },
            {
              title: '渠道',
              slots: {
                default: 'form_channel',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
      },
      selectFormData: {
        appId: null,
        day: null,
        dayRange: [],
        country: null,
        channel: 'Mintegral-oppo',
      },
      beforeSelect: () => {
        const day =
          this.selectFormData.dayRange && this.selectFormData.dayRange[0]
        const dayEnd =
          this.selectFormData.dayRange && this.selectFormData.dayRange[1]
        return {
          appId: this.selectFormData.appId,
          day: day,
          dayEnd: dayEnd,
          country: this.selectFormData.country,
          channel: this.selectFormData.channel,
        }
      },
      appFilter: appFilter,
    }
  },
  methods: {
    countryFilter(country) {
      return [
        '菲律宾',
        '墨西哥',
        '德国',
        '法国',
        '泰国',
        '哥伦比亚',
        '罗马尼亚',
        '英国',
        '智利',
        '秘鲁',
        '俄罗斯',
        '波兰',
        '土耳其',
        '马来西亚',
        '西班牙',
      ].includes(country.country_name)
    },
  },
}
</script>
