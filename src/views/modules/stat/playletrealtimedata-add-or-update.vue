<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="日期" prop="dt">
      <el-input v-model="dataForm.dt" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="1:tt,2:kwai" prop="channel">
      <el-input v-model="dataForm.channel" placeholder="1:tt,2:kwai"></el-input>
    </el-form-item>
    <el-form-item label="剧名" prop="playletName">
      <el-input v-model="dataForm.playletName" placeholder="剧名"></el-input>
    </el-form-item>
    <el-form-item label="总观看人数（UV）" prop="watchUv">
      <el-input v-model="dataForm.watchUv" placeholder="总观看人数（UV）"></el-input>
    </el-form-item>
    <el-form-item label="总观看剧集数（UV）" prop="serialWatchUv">
      <el-input v-model="dataForm.serialWatchUv" placeholder="总观看剧集数（UV）"></el-input>
    </el-form-item>
    <el-form-item label="付款人数" prop="payNum">
      <el-input v-model="dataForm.payNum" placeholder="付款人数"></el-input>
    </el-form-item>
    <el-form-item label="总付费金额" prop="payAmount">
      <el-input v-model="dataForm.payAmount" placeholder="总付费金额"></el-input>
    </el-form-item>
    <el-form-item label="终身付费人数" prop="permentNum">
      <el-input v-model="dataForm.permentNum" placeholder="终身付费人数"></el-input>
    </el-form-item>
    <el-form-item label="终身付费金额" prop="permentAmount">
      <el-input v-model="dataForm.permentAmount" placeholder="终身付费金额"></el-input>
    </el-form-item>
    <el-form-item label="金币付费人数" prop="coinPayNum">
      <el-input v-model="dataForm.coinPayNum" placeholder="金币付费人数"></el-input>
    </el-form-item>
    <el-form-item label="金币付费金额" prop="coinPayAmount">
      <el-input v-model="dataForm.coinPayAmount" placeholder="金币付费金额"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          dt: '',
          channel: '',
          playletName: '',
          watchUv: '',
          serialWatchUv: '',
          payNum: '',
          payAmount: '',
          permentNum: '',
          permentAmount: '',
          coinPayNum: '',
          coinPayAmount: ''
        },
        dataRule: {
          dt: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          channel: [
            { required: true, message: '1:tt,2:kwai不能为空', trigger: 'blur' }
          ],
          playletName: [
            { required: true, message: '剧名不能为空', trigger: 'blur' }
          ],
          watchUv: [
            { required: true, message: '总观看人数（UV）不能为空', trigger: 'blur' }
          ],
          serialWatchUv: [
            { required: true, message: '总观看剧集数（UV）不能为空', trigger: 'blur' }
          ],
          payNum: [
            { required: true, message: '付款人数不能为空', trigger: 'blur' }
          ],
          payAmount: [
            { required: true, message: '总付费金额不能为空', trigger: 'blur' }
          ],
          permentNum: [
            { required: true, message: '终身付费人数不能为空', trigger: 'blur' }
          ],
          permentAmount: [
            { required: true, message: '终身付费金额不能为空', trigger: 'blur' }
          ],
          coinPayNum: [
            { required: true, message: '金币付费人数不能为空', trigger: 'blur' }
          ],
          coinPayAmount: [
            { required: true, message: '金币付费金额不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/playletrealtimedata/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.dt = data.playletRealtimeData.dt
                this.dataForm.channel = data.playletRealtimeData.channel
                this.dataForm.playletName = data.playletRealtimeData.playletName
                this.dataForm.watchUv = data.playletRealtimeData.watchUv
                this.dataForm.serialWatchUv = data.playletRealtimeData.serialWatchUv
                this.dataForm.payNum = data.playletRealtimeData.payNum
                this.dataForm.payAmount = data.playletRealtimeData.payAmount
                this.dataForm.permentNum = data.playletRealtimeData.permentNum
                this.dataForm.permentAmount = data.playletRealtimeData.permentAmount
                this.dataForm.coinPayNum = data.playletRealtimeData.coinPayNum
                this.dataForm.coinPayAmount = data.playletRealtimeData.coinPayAmount
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/playletrealtimedata/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'dt': this.dataForm.dt,
                'channel': this.dataForm.channel,
                'playletName': this.dataForm.playletName,
                'watchUv': this.dataForm.watchUv,
                'serialWatchUv': this.dataForm.serialWatchUv,
                'payNum': this.dataForm.payNum,
                'payAmount': this.dataForm.payAmount,
                'permentNum': this.dataForm.permentNum,
                'permentAmount': this.dataForm.permentAmount,
                'coinPayNum': this.dataForm.coinPayNum,
                'coinPayAmount': this.dataForm.coinPayAmount
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
