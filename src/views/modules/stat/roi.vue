<template>
  <div>
    <el-form ref="form" :inline="true" :model="formInline" :rules="rules">
      <el-form-item label="应用" prop="appId">
        <app-select-component v-model="formInline.appId" @change="onSubmit" />
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <country-select
          v-model="formInline.country"
          :has-all="false"
          @change="onSubmit"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channel">
        <ad-platform-select
          v-model="formInline.channel"
          version="2"
          @change="onSubmit"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="onSubmit"
          icon="el-icon-search"
          :loading="loading"
        >
          查询
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel(false)"
        >
          下载Excel
        </el-button>
      </el-form-item>
      <el-form-item>
        <span style="color: #2b2b2b">cpi=cost/库里用户，dnu为渠道归因用户</span>
      </el-form-item>
    </el-form>
    <div class="table-box" ref="table-box">
      <el-table
        :data="tableData"
        style="width: 100%;"
        v-loading="loading"
        :cell-style="cellStyle"
        :height="tableHeight"
        border
      >
        <af-table-column prop="appId" label="应用名称" fixed align="center">
          <template slot-scope="{ row }">
            {{ row.appId | getAppName }}
          </template>
        </af-table-column>
        <af-table-column prop="dt" label="日期" fixed align="center" />
        <af-table-column prop="news" label="新增用户" fixed align="center" />
        <af-table-column prop="cpi" label="当日CPI" fixed align="center" />
        <af-table-column prop="arpu" label="当日ARPU" fixed align="center" />
        <af-table-column
          :prop="`roi${index}`"
          :label="`ROI${index}`"
          v-for="index in 30"
          :key="index"
          align="center"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
import { roiRequest } from '@/api/stat'
import CountrySelect from '@/components/country-select/index.vue'
import dayjs from '@/dayjs'

export default {
  components: { CountrySelect },
  data() {
    return {
      loading: false,
      formInline: {
        appId: 10090,
        channel: '',
        country: '',
      },
      tableData: [],
      rules: {
        appId: { required: true, message: '请选择', trigger: 'blur' },
        // country: { required: true, message: '请选择', trigger: 'blur' },
        // channel: { required: true, message: '请选择', trigger: 'blur' },
      },
      tableHeight: null,
    }
  },
  activated() {
    this.onSubmit()
  },
  mounted() {
    const viewHeight = document.documentElement.offsetHeight
    const tableCTop = this.$refs['table-box'].getBoundingClientRect().top
    this.tableHeight = viewHeight - tableCTop - 50
    console.log('tableHeight', this.tableHeight)
  },
  methods: {
    cellStyle({ row, column }) {
      if (column.property.toLowerCase().startsWith('roi')) {
        if (row[column.property] === '-') {
          return
        }

        if (row[column.property] > 1) {
          return {
            background: '#fa8e8e',
            color: '#fff',
          }
        }

        return {
          background: '#e4f2fd',
        }
      }
    },
    async onSubmit() {
      try {
        await this.$refs.form.validate()

        this.loading = true

        const res = await roiRequest.selectAll(this.formInline)
        if (res && res.code === 0) {
          this.tableData = res.list.map(it => {
            let arpu = it.roi1 && it.cpi ? it.roi1 * it.cpi : '-'
            arpu = arpu !== '-' && arpu.toFixed(4)

            const result = { ...it, arpu }
            let diffDay = dayjs().diff(it.dt, 'day')

            for (let i = 1; i <= 30; i++) {
              const key = `roi${i}`
              if (i <= diffDay) {
                result[key] = it[key]
              } else {
                result[key] = '-'
              }
            }
            return result
          })
        }
      } finally {
        this.loading = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.table-box {
}
</style>
