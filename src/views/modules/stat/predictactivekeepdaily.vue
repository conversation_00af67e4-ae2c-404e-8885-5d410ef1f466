<template>
  <div class="mod-config">
    <base-form :inline="true" :model.sync="dataForm" @submit="getDataList()">
      <el-form-item label="选择应用">
        <app-select-component
          v-model="dataForm.appCode"
          @change-app="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="selectTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyyMMdd"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          v-if="isAuth('stat:predictactivekeepdaily:save')"
          type="primary"
          icon="el-icon-plus"
          @click="addOrUpdateHandle()"
        >
          新增留存模型
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:predictactivekeepdaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </base-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
      :cell-style="{
        padding: 0,
      }"
      class="cus-table"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="id"
        width="50"
      />
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
        width="90"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="groupId"
        header-align="center"
        align="center"
        label="主体"
        width="60"
      >
        <template slot-scope="{ row }">
          {{ appGroupWithAll.get(row.groupId) }}
        </template>
      </el-table-column>
      <!--<el-table-column-->
      <!--  prop="roi"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="30天预估ROI"-->
      <!--  width="100px"-->
      <!--/>-->
      <el-table-column
        prop="cpa"
        header-align="center"
        align="center"
        label="CPA"
        width="60px"
      />
      <el-table-column
        prop="newAddNums"
        header-align="center"
        align="center"
        label="新增用户"
      />
      <el-table-column
        prop="activeNums"
        header-align="center"
        align="center"
        label="活跃用户"
      />
      <template v-for="item in 30">
        <el-table-column
          :prop="`keep${item}`"
          header-align="center"
          align="center"
          :label="
            item === 1
              ? `当日/ARPU/ROI`
              : `${item === 2 ? '次' : item}留/ARPU${item}/ROI`
          "
          :key="item"
          width="130px"
        >
          <template slot-scope="{ row }">
            <div
              class="kar-inner"
              :style="[
                {
                  background:
                    item > karStyle(row)
                      ? 'rgb(242,242,250)'
                      : 'rgb(255,255,255)',
                },
              ]"
            >
              {{ computedKeep(row, item) }}
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="140px"
      />
      <el-table-column
        prop="updateAt"
        header-align="center"
        align="center"
        label="更新时间"
        width="140px"
      />
      <!--<el-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.id)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.id)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      @closed="addOrUpdateVisible = false"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import BaseForm from '@/components/base-form'
import AddOrUpdate from './predictactivekeepdaily-add-or-update'
import AppSelectComponent from '@/components/app-select-component'
import { appGroupWithAll } from '@/map/common'
import { formatDuring } from '../../../utils/tools'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        appCode: '',
        // day: '',
        startDay: '',
        endDay: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      appGroupWithAll,
    }
  },
  computed: {
    selectTime: {
      set(time) {
        this.dataForm.startDay = time ? time[0] ?? '' : ''
        this.dataForm.endDay = time ? time[1] ?? '' : ''
      },
      get() {
        return [this.dataForm.startDay, this.dataForm.endDay]
      },
    },
  },
  components: {
    AddOrUpdate,
    AppSelectComponent,
    BaseForm,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/predictactivekeepdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.dataForm.appCode)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/predictactivekeepdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    computedKeep(row, index) {
      const keep = index === 1 ? 1 : row[`keep${index}`]
      const arpu = row[`arpu${index}`]
      const roi = row[`roi${index}`]
      // const ltv = row[`ltv${index}`]

      const format = n => (n * 1000) / 10 + '%'

      return [format(keep), arpu, format(roi)].map(it => it || '-').join(' / ')
    },
    //平均arpu = n留总ecpm /( 1000 * 新增数 * 留存率)
    // computedArpu(ecpm, newAddNums, keep) {
    //   try {
    //     const ret = (ecpm / (1000 * newAddNums * keep)).toFixed(2)
    //     return ret === 'NaN' || ret === '0.00' ? 0 : ret
    //   } catch (e) {
    //     return 0
    //   }
    // },

    karStyle(row) {
      const diffTime = Date.now() - this.$dayjs(row.day.toString()).valueOf()
      return formatDuring(diffTime).days
    },
  },
}
</script>

<style scoped lang="scss">
.cus-table {
  .kar-inner {
    padding: 8px 10px;
    height: 100%;
  }
  &::v-deep {
    .cell {
      height: 100%;
      padding: 0;
    }
  }
}
</style>
