<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="设备id" prop="uid">
      <el-input v-model="dataForm.uid" placeholder="设备id"></el-input>
    </el-form-item>
    <el-form-item label="应用id,用户注册在哪个app code" prop="appCode">
      <el-input v-model="dataForm.appCode" placeholder="应用id,用户注册在哪个app code"></el-input>
    </el-form-item>
    <el-form-item label="" prop="openId">
      <el-input v-model="dataForm.openId" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="微信头像" prop="wechatAvatar">
      <el-input v-model="dataForm.wechatAvatar" placeholder="微信头像"></el-input>
    </el-form-item>
    <el-form-item label="注册时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="注册时间"></el-input>
    </el-form-item>
    <el-form-item label="登录时间" prop="loginTime">
      <el-input v-model="dataForm.loginTime" placeholder="登录时间"></el-input>
    </el-form-item>
    <el-form-item label="微信昵称" prop="nickName">
      <el-input v-model="dataForm.nickName" placeholder="微信昵称"></el-input>
    </el-form-item>
    <el-form-item label="账户状态：1：正常，2：注销待审核，3：已注销，4：封禁，5：微信未登录" prop="status">
      <el-input v-model="dataForm.status" placeholder="账户状态：1：正常，2：注销待审核，3：已注销，4：封禁，5：微信未登录"></el-input>
    </el-form-item>
    <el-form-item label="注册信息" prop="registerInfo">
      <el-input v-model="dataForm.registerInfo" placeholder="注册信息"></el-input>
    </el-form-item>
    <el-form-item label="设备风险建议" prop="riskAdvice">
      <el-input v-model="dataForm.riskAdvice" placeholder="设备风险建议"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    <el-form-item label="风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险 6:风控校验失败（自定义）" prop="riskLevel">
      <el-input v-model="dataForm.riskLevel" placeholder="风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险 6:风控校验失败（自定义）"></el-input>
    </el-form-item>
    <el-form-item label="操作人" prop="updatedBy">
      <el-input v-model="dataForm.updatedBy" placeholder="操作人"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          uid: '',
          appCode: '',
          openId: '',
          wechatAvatar: '',
          createdAt: '',
          loginTime: '',
          nickName: '',
          status: '',
          registerInfo: '',
          riskAdvice: '',
          remark: '',
          riskLevel: '',
          updatedBy: ''
        },
        dataRule: {
          uid: [
            { required: true, message: '设备id不能为空', trigger: 'blur' }
          ],
          appCode: [
            { required: true, message: '应用id,用户注册在哪个app code不能为空', trigger: 'blur' }
          ],
          openId: [
            { required: true, message: '不能为空', trigger: 'blur' }
          ],
          wechatAvatar: [
            { required: true, message: '微信头像不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '注册时间不能为空', trigger: 'blur' }
          ],
          loginTime: [
            { required: true, message: '登录时间不能为空', trigger: 'blur' }
          ],
          nickName: [
            { required: true, message: '微信昵称不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '账户状态：1：正常，2：注销待审核，3：已注销，4：封禁，5：微信未登录不能为空', trigger: 'blur' }
          ],
          registerInfo: [
            { required: true, message: '注册信息不能为空', trigger: 'blur' }
          ],
          riskAdvice: [
            { required: true, message: '设备风险建议不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ],
          riskLevel: [
            { required: true, message: '风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险 6:风控校验失败（自定义）不能为空', trigger: 'blur' }
          ],
          updatedBy: [
            { required: true, message: '操作人不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/playletuser/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.uid = data.playletUser.uid
                this.dataForm.appCode = data.playletUser.appCode
                this.dataForm.openId = data.playletUser.openId
                this.dataForm.wechatAvatar = data.playletUser.wechatAvatar
                this.dataForm.createdAt = data.playletUser.createdAt
                this.dataForm.loginTime = data.playletUser.loginTime
                this.dataForm.nickName = data.playletUser.nickName
                this.dataForm.status = data.playletUser.status
                this.dataForm.registerInfo = data.playletUser.registerInfo
                this.dataForm.riskAdvice = data.playletUser.riskAdvice
                this.dataForm.remark = data.playletUser.remark
                this.dataForm.riskLevel = data.playletUser.riskLevel
                this.dataForm.updatedBy = data.playletUser.updatedBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/playletuser/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'uid': this.dataForm.uid,
                'appCode': this.dataForm.appCode,
                'openId': this.dataForm.openId,
                'wechatAvatar': this.dataForm.wechatAvatar,
                'createdAt': this.dataForm.createdAt,
                'loginTime': this.dataForm.loginTime,
                'nickName': this.dataForm.nickName,
                'status': this.dataForm.status,
                'registerInfo': this.dataForm.registerInfo,
                'riskAdvice': this.dataForm.riskAdvice,
                'remark': this.dataForm.remark,
                'riskLevel': this.dataForm.riskLevel,
                'updatedBy': this.dataForm.updatedBy
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
