<template>
  <div>
    <el-form :inline="true" :model="dataForm" :rules="rules">
      <el-form-item label="选择应用">
        <app-select @change="getDataList" @init-app-id="getDataList" />
      </el-form-item>
      <el-form-item label="日期" prop="endTime">
        <el-date-picker
          v-model="dataForm.endTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 20px">
      <el-select
        v-model="keepNumber"
        style="margin-right: 20px"
        placeholder="切换多少天"
      >
        <el-option
          v-for="item in 14"
          :value="item"
          :key="item"
          :label="item + 1 + '天'"
        />
      </el-select>
      <el-button type="primary" @click="isPercentage = !isPercentage">
        切换留存/留存率
      </el-button>
    </div>
    <el-table v-loading="dataListLoading" :data="dataList" style="width: 100%">
      <el-table-column prop="date" label="日期" width="150" align="center" />
      <el-table-column
        prop="newlyAdded"
        label="新增"
        width="150"
        align="center"
      />
      <el-table-column
        :label="isPercentage ? '留存率' : '留存数'"
        align="center"
      >
        <el-table-column
          align="center"
          v-for="(item, index) in keepNumber"
          :key="item"
          :label="index + 2 === 2 ? '次留' : index + 2 + '留'"
        >
          <template slot-scope="{ row }" v-if="keepP(row, index) > 0">
            <el-tag :type="tagType(keepP(row, index))">
              {{
                isPercentage
                  ? keepP(row, index) + '%'
                  : row['rank' + (index + 1)]
              }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import dayjs from 'dayjs'

export default {
  name: 'keep',
  data() {
    return {
      dataForm: {
        endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      },
      dataList: [],
      dataListLoading: false,
      appList: [],
      rules: {
        endTime: [{ required: true, message: '必填' }],
      },
      isPercentage: true,
      keepNumber: 14,
    }
  },
  components: {
    AppSelect,
  },
  activated() {
    this.getAppList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)
      if (!res || !res.code) {
        return this.$message.error('请先选择应用')
      }

      this.dataListLoading = true

      if (!this.dataForm.endTime) {
        return
      }

      this.$http({
        url: this.$http.adornUrl('/stat/statbusinessdaily/cpa_list'),
        method: 'get',
        params: this.$http.adornParams({
          appId: res.code, // 这个字段表面上看是交appId，但实际上却是code
          endTime: this.dataForm.endTime, // 2021-08-31 14:53:50
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    getAppList() {
      this.$store
        .dispatch('api/app/getAppList', {
          page: 1,
          limit: 10000,
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.page.list
            console.log(this.appList)
          }
        })
    },
    keepP(row, index) {
      return ((row['rank' + (index + 1)] / row.newlyAdded) * 100).toFixed(2)
    },
    tagType(p) {
      let result = ''
      p = Number(p)
      if (p > 50) {
        result = 'danger'
      } else if (p > 30) {
        result = 'warning'
      } else {
        result = ''
      }
      return result
    },
  },
}
</script>

<style scoped></style>
