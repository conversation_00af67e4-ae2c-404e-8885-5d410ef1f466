<!--
 华为成本 roas 表
-->
<template>
  <CostDaily
    :is-roas="true"
    :show-ad-platform="false"
    ad-platform="huawei"
    :features="['select']"
    :show-operate="false"
    :columns="columns"
    :row-style="rowStyle"
    :app-filter="appFilter"
  />
</template>

<script>
import CostDaily from '@/components/cost-daily/index.vue'
import { countryList } from '@/map/sat'
import { appFilter } from '@/utils/bussiness'

export default {
  components: {
    CostDaily,
  },
  data() {
    return {
      columns: [
        { type: 'seq', title: '序号', minWidth: 60 },
        {
          field: 'appCode',
          title: '应用',
          minWidth: 140,
          slots: { default: 'table_item_appCode_text' },
        },
        {
          field: 'day',
          title: '日期',
          minWidth: 100,
          slots: { default: 'table_item_day' },
        },
        {
          field: 'country',
          title: '国家',
          minWidth: 60,
          formatter({ cellValue }) {
            const item = countryList.find(it => it.country === cellValue)
            return item ? item.country_name : ''
          },
        },
        { field: 'cost', title: '消耗', minWidth: 60 },
        { field: 'cpi', title: 'cpi', minWidth: 60 },
        { field: 'conversion', title: '激活量', minWidth: 60 },
        { field: 'income', title: 'topon-api收入', minWidth: 60 },
        {
          field: 'roas',
          title: '实际roas',
          minWidth: 60,
          formatter: ({ cellValue }) => {
            return cellValue
              ? ((Number(cellValue) * 100 * 100) / 100).toFixed(1) + '%'
              : ''
          },
        },
      ],
    }
  },
  methods: {
    rowStyle({ row }) {
      if (row.roas) {
        if (row.roas <= 0.7) {
          return {
            color: '#11b600',
            // fontWeight: 'bolder',
            // fontStyle: 'italic',
          }
        } else if (row.roas > 0.8) {
          return {
            color: 'red',
            // fontWeight: 'bolder',
            // fontStyle: 'italic',
          }
        }
      }
    },
    appFilter: appFilter,
  },
}
</script>
