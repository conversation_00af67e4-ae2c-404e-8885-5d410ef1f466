<template>
  <div>
    <el-form inline>
      <el-form-item label="应用" prop="appCode">
        <app-select-component
          v-model="dataForm.appCode"
          :is-show-all="false"
          :app-filter="appFilter"
        />
      </el-form-item>
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="getData"
        :loading="loading"
      >
        搜索
      </el-button>
    </el-form>
    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <el-table-column prop="appId" label="应用">
        <template slot-scope="{ row }">
          {{ row.appId | getAppName }}
        </template>
      </el-table-column>
      <el-table-column prop="ruleName" label="策略名称" />
      <!--0:未启用，1：启用-->
      <el-table-column prop="status" label="状态">
        <template slot-scope="{ row }">
          <tag-status :status="row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="accountIds" label="账户列表" />
      <!--yyyyMMdd -->
      <el-table-column prop="day" label="最新日期" />
      <el-table-column prop="clickNum" label="点击数" />
      <el-table-column prop="exposureNum" label="曝光数" />
      <el-table-column prop="channel" label="渠道" />
      <el-table-column prop="brand" label="品牌" />
      <el-table-column prop="report" label="举报数" />
      <el-table-column prop="block" label="拉黑数" />
      <el-table-column prop="cost" label="成本" sortable>
        <template slot-scope="{ row }">
          {{ row.cost | fixed(3) }}
        </template>
      </el-table-column>
      <el-table-column prop="complaintRate" label="投诉率" sortable>
        <template slot-scope="{ row }">
          {{ row.complaintRate | toScale(10000, '‱') }}
        </template>
      </el-table-column>
      <el-table-column prop="upRate" label="吊起率" sortable>
        <template slot-scope="{ row }">
          {{ row.upRate | toScale(100, '%', 0) }}
        </template>
      </el-table-column>
      <el-table-column prop="avgIpu" label="人均IPU" sortable />
      <el-table-column prop="attributionRate" label="归因率" sortable>
        <template slot-scope="{ row }">
          {{ row.attributionRate | toScale(100, '%', 1) }}
        </template>
      </el-table-column>
      <el-table-column prop="dau" label="当日累计活跃" />
    </el-table>
  </div>
</template>

<script>
import { request } from '@/utils/request'

export default {
  data() {
    return {
      tableData: [],
      dataForm: {
        appCode: 10120,
      },
      loading: false,
      appFilter: ({ groupId }) => groupId === 11,
    }
  },
  created() {
    // this.getData()
  },
  filters: {
    fixed(value, fractionDigits = 2) {
      return typeof value === 'number' ? value.toFixed(fractionDigits) : value
    },
    toScale(value, scale = 100, unit = '%', fractionDigits = 2) {
      return typeof value === 'number'
        ? (value * scale).toFixed(fractionDigits) + unit
        : value
    },
  },
  methods: {
    getData() {
      if (!this.dataForm.appCode) {
        return
      }

      this.loading = true
      request({
        url: `/stat/quickappbranddatadaily/real_time/${this.dataForm.appCode}`,
      })
        .then(res => {
          this.tableData = res.data || []
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>
