<template>
  <v-chart class="chart" :option="option" :update-options="updateOptions" />
</template>

<script>
import { use } from 'echarts/core'
import VChart, { THEME_KEY } from 'vue-echarts'
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components'
import { LineChart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
])

export default {
  components: {
    VChart,
  },
  provide: {
    [THEME_KEY]: 'light',
  },
  props: {
    xAxisData: {
      type: Array,
    },
    series: {
      type: Array,
    },
    title: {
      type: String,
    },
  },
  computed: {
    option() {
      console.log('刷新。。。', this.series)
      const legend = this.series.map(it => it.name)
      return {
        title: {
          text: this.title,
        },
        tooltip: {
          trigger: 'axis',
          // formatter: function(params) {
          //   console.log('params ====>>>>>>', params)
          //   //x轴名称
          //   const name = params[0].name
          //   //图表title名称
          //   const seriesName = params[0].seriesName
          //   //值
          //   const value = params[0].value
          //   return name + '<br />' + value
          // },
        },
        legend: {
          // data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
          data: legend,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.xAxisData,
          axisLabel: {
            interval: 0, // 强制显示所有
            formatter: '{value} 时',
          },
        },
        yAxis: {
          type: 'value',
        },
        // series: [
        //   {
        //     name: 'Email',
        //     type: 'line',
        //     stack: 'Total',
        //     data: [120, 132, 101, 134, 90, 230, 210],
        //     smooth: true,
        //   },
        // ],
        series: this.series,
      }
    },
  },
  data() {
    return {
      updateOptions: {
        // 如果设置opts.notMerge为true，那么旧的组件会被完全移除，新的组件会根据option创建。
        // 如果设置opts.notMerge为false，或者没有设置 opts.notMerge：
        // 如果在opts.replaceMerge里指定组件类型，这类组件会进行替换合并。
        // 否则，会进行普通合并。
        notMerge: true,
      },
    }
  },
}
</script>

<style scoped>
.chart {
  height: 400px;
}
</style>
