<template>
  <div class="custom-tooltip">
    <span>{{ spanText }}</span>
    <el-tooltip
      class="item"
      effect="dark"
      :content="tooltipContent"
      :placement="placement"
    >
      <i class="el-icon-question"></i>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'Tooltip',
  props: {
    spanText: {
      type: String,
      required: true,
    },
    tooltipContent: {
      type: String,
      required: true,
    },
    placement: {
      type: String,
      default: 'top',
    },
  },
}
</script>

<style scoped>
.custom-tooltip {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
</style>
