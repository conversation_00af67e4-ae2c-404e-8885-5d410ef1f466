<template>
  <div style="margin-bottom: 16px; display: flex; align-items: center; gap: 12px;">
    <span style="margin-right: 10px;">数据筛选</span>
    <el-select v-model="filterField" placeholder="选择字段" style="width: 110px">
      <el-option label="账户 ID" value="accountId" />
      <el-option label="ROI" value="roi" />
      <el-option label="消耗" value="cost" />
      <el-option label="曝光" value="costExposureNum" />
      <el-option label="点击" value="adClickNum" />
      <el-option label="ECPM" value="costEcpm" />
      <el-option label="CPC" value="cpc" />
      <el-option label="CPA" value="cpa" />
      <el-option label="CVR" value="conversionRate" />
      <!-- 可按需扩展字段 -->
    </el-select>
    <el-select v-model="filterOp" placeholder="操作符" style="width: 90px">
      <el-option label=">=" value=">=" />
      <el-option label="<=" value="<=" />
      <el-option label=">" value=">" />
      <el-option label="<" value="<" />
      <el-option label="=" value="==" />
    </el-select>
    <el-input v-model="filterValue" placeholder="数值" style="width: 90px" />
    <el-button type="primary" size="mini" @click="addFilterCondition">添加筛选</el-button>
    <div v-if="filterConditions.length">
      <span style="margin-left: 10px;">当前筛选：</span>
      <el-tag
        v-for="(cond, idx) in filterConditions"
        :key="idx"
        type="info"
        closable
        @close="removeFilterCondition(idx)"
        style="margin-right: 4px;"
      >
        {{ fieldLabel(cond.field) }} {{ cond.op }} {{ cond.value }}
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataFilter',
  data() {
    return {
      filterField: '',
      filterOp: '',
      filterValue: '',
      filterConditions: []
    }
  },
  methods: {
    addFilterCondition() {
      if (!this.filterField || !this.filterOp || this.filterValue === '' || isNaN(Number(this.filterValue))) {
        this.$message.warning('请完整填写筛选条件');
        return;
      }
      this.filterConditions.push({
        field: this.filterField,
        op: this.filterOp,
        value: Number(this.filterValue)
      });
      this.filterField = '';
      this.filterOp = '';
      this.filterValue = '';
      this.emitFilters();
    },
    removeFilterCondition(idx) {
      this.filterConditions.splice(idx, 1);
      this.emitFilters();
    },
    emitFilters() {
      this.$emit('update-filters', this.filterConditions.slice());
    },
    fieldLabel(field) {
      switch (field) {
        case 'accountId': return '账户 ID';
        case 'roi': return 'ROI';
        case 'cost': return '消耗';
        case 'costExposureNum': return '曝光';
        case 'adClickNum': return '点击';
        case 'costEcpm': return 'ECPM';
        case 'cpc': return 'CPC';
        case 'cpa': return 'CPA';
        case 'conversionRate': return 'CVR';
        default: return field;
      }
    }
  }
}
</script>