<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-form-item label="应用id" prop="appCode">
        <el-input
          v-model="dataForm.appCode"
          :disabled="!!dataForm.id"
          placeholder="应用id"
        />
      </el-form-item>
      <el-form-item label="日期" prop="day">
        <el-input
          v-model="dataForm.day"
          :disabled="!!dataForm.id"
          placeholder="日期"
        />
      </el-form-item>
      <el-form-item label="花费" prop="adDspCost">
        <el-input v-model="dataForm.adDspCost" placeholder="花费" />
      </el-form-item>
      <el-form-item label="赠款消耗金额" prop="statGrantCost">
        <el-input v-model="dataForm.statGrantCost" placeholder="赠款消耗金额" />
      </el-form-item>
      <el-form-item label="现金消耗金额" prop="statCashCost">
        <el-input v-model="dataForm.statCashCost" placeholder="现金消耗金额" />
      </el-form-item>
      <el-form-item label="素材曝光数" prop="adItemImpression">
        <el-input
          v-model="dataForm.adItemImpression"
          placeholder="素材曝光数"
        />
      </el-form-item>
      <el-form-item label="行为数" prop="adItemClick">
        <el-input v-model="dataForm.adItemClick" placeholder="行为数" />
      </el-form-item>
      <el-form-item label="激活数" prop="eventConversion">
        <el-input v-model="dataForm.eventConversion" placeholder="激活数" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appCode: '',
        day: '',
        adDspCost: '',
        statGrantCost: '',
        statCashCost: '',
        adItemImpression: '',
        adItemClick: '',
        eventConversion: '',
      },
      dataRule: {
        appCode: [
          { required: true, message: '应用id不能为空', trigger: 'blur' },
        ],
        day: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        statGrantCost: [
          { required: true, message: '赠款消耗金额', trigger: 'blur' },
        ],
        adDspCost: [
          { required: true, message: '现金消耗金额', trigger: 'blur' },
        ],
        statCashCost: [
          { required: true, message: '花费不能为空', trigger: 'blur' },
        ],
        adItemImpression: [
          { required: true, message: '素材曝光数不能为空', trigger: 'blur' },
        ],
        adItemClick: [
          { required: true, message: '行为数不能为空', trigger: 'blur' },
        ],
        eventConversion: [
          { required: true, message: '激活数不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/costkuaishoureportdaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appCode = data.costKuaiShouReportDaily.appCode
              this.dataForm.day = data.costKuaiShouReportDaily.day
              this.dataForm.adDspCost = data.costKuaiShouReportDaily.adDspCost
              this.dataForm.adItemImpression =
                data.costKuaiShouReportDaily.adItemImpression
              this.dataForm.adItemClick =
                data.costKuaiShouReportDaily.adItemClick
              this.dataForm.eventConversion =
                data.costKuaiShouReportDaily.eventConversion
              this.dataForm.statGrantCost =
                data.costKuaiShouReportDaily.statGrantCost
              this.dataForm.statCashCost =
                data.costKuaiShouReportDaily.statCashCost
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/costkuaishoureportdaily/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appCode: this.dataForm.appCode,
              day: this.dataForm.day,
              adDspCost: this.dataForm.adDspCost,
              adItemImpression: this.dataForm.adItemImpression,
              adItemClick: this.dataForm.adItemClick,
              eventConversion: this.dataForm.eventConversion,
              statGrantCost: this.dataForm.statGrantCost,
              statCashCost: this.dataForm.statCashCost,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
