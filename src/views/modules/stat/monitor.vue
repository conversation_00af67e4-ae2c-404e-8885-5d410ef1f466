<template>
  <div>
    <el-tabs v-model="currentGenre" @tab-click="handleClick">
      <el-tab-pane
        v-if="showAll"
        :label="'全部'"
        name="0"
        :disabled="!canSwitch && currentGenre !== '0'"
      >
        <div class="tab-item-box">
          <div
            class="tab-item"
            v-for="[key, label] in appGroupWithAll"
            :key="key"
          >
            <el-button
              :type="currentAppGroupId === key ? 'primary' : ''"
              @click="handleChangeAppGroup(key)"
              :disabled="!canSwitch && currentAppGroupId !== key"
            >
              {{ label }}
            </el-button>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="快应用" name="quick-app">
        <QuickAppMonitor ref="quickAppMonitor" />
      </el-tab-pane>
      <el-tab-pane
        v-for="item in appTypeList"
        :label="item.genreName"
        :name="`${item.genre}`"
        :key="item.genre"
        :disabled="
          !canSwitch &&
            currentGenre != item.genre &&
            currentGenre !== 'quick-app'
        "
      />
    </el-tabs>
    <template v-if="currentGenre !== 'quick-app'">
      <el-form :inline="true" :model="dataForm">
        <el-form-item label="应用">
          <el-select v-model="currentAppCode">
            <el-option
              v-for="item in currentGenreAppList"
              :value="item.code"
              :label="item.name"
              :key="item.code"
            >
              <span style="float: left">{{ item.name }}</span>
              <span
                style="float: right; color: #8492a6; font-size: 13px; margin-left: 5px"
              >
                {{ item.code }}
                <template v-if="appClassification.get(item.genre)">
                  /
                  <i style="font-size: 12px">
                    {{ appClassification.get(item.genre) }}
                  </i>
                </template>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="currentGenre === '4'" label="海外渠道">
          <arr-select
            v-model="dataForm.channel"
            :list="channelNameList"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item v-else label="渠道">
          <map-select
            v-model="dataForm.mediaType"
            :list="channelNameListMap"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item label="品牌">
          <arr-select
            v-model="dataForm.brand"
            filterable
            clearable
            :list="brandList"
          />
        </el-form-item>
        <el-form-item v-if="currentGenre === '4'" label="国家">
          <country-select v-model="dataForm.country" />
        </el-form-item>
        <el-form-item v-if="currentGenre !== '4'" label="投放账户">
          <el-select v-model="dataForm.advertiseId" clearable filterable>
            <el-option
              v-for="(item, index) in advertiseIdList"
              :key="index"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="currentGenre !== '4'" label="渠道号">
          <el-select v-model="dataForm.marketCode" clearable filterable>
            <el-option
              v-for="(item, index) in marketCodeList"
              :key="index"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-row class="row-box" :gutter="20">
        <el-col :span="8" style="margin-top: 20px">
          <TimeKeep
            :request-params="requestParams"
            @request-finish="handleRequestFinish"
          />
        </el-col>
        <el-col :span="8" style="margin-top: 20px">
          <ChartsTimeNewlyAdd
            :request-params="requestParams"
            @request-finish="handleRequestFinish"
          />
        </el-col>
        <el-col :span="8" style="margin-top: 20px">
          <ChartsCommonTime
            title="ARPU"
            type="ARPU"
            :request-params="requestParams"
            @request-finish="handleRequestFinish"
          />
        </el-col>
        <el-col :span="8" style="margin-top: 20px">
          <ChartsCommonTime
            title="ECPM"
            type="ECPM"
            :request-params="requestParams"
            @request-finish="handleRequestFinish"
          />
        </el-col>
        <el-col :span="8" style="margin-top: 20px">
          <ChartsCommonTime
            title="AIPU"
            type="AIPU"
            :request-params="requestParams"
            @request-finish="handleRequestFinish"
          />
        </el-col>
        <el-col :span="8" style="margin-top: 20px">
          <ChartsCommonTime
            title="ImpRate"
            type="ImpRate"
            :request-params="requestParams"
            @request-finish="handleRequestFinish"
          />
        </el-col>
      </el-row>
    </template>
  </div>
</template>

<script>
import { unionBy, uniq } from 'lodash'
import {
  appClassification,
  appGroup,
  appGroupWithAll,
  channelNameListMap,
} from '@/map/common'
import ChartsTimeNewlyAdd from '@/components/charts/time-newly-add'
import TimeKeep from '@/components/charts/time-keep'
import ChartsCommonTime from '@/components/charts/common-time'
import MapSelect from '@/components/map-select'
import QuickAppMonitor from '@/views/modules/stat/components/quick-app-monitor.vue'
import CountrySelect from '@/components/country-select/index.vue'
import { channelNameList } from '@/map/common'

// 这些品牌必须写最前面
const firstBrandList = ['huawei', 'honor', 'vivo', 'oppo', 'xiaomi', 'redmi']

export default {
  name: 'monitor',
  components: {
    CountrySelect,
    MapSelect,
    ChartsTimeNewlyAdd,
    TimeKeep,
    ChartsCommonTime,
    QuickAppMonitor,
  },
  data() {
    return {
      channelNameList,
      channelNameListMap,
      dataForm: {
        mediaType: '', // 媒体类型：0APK 1穿山甲/2优量汇/3快手/4百度
        brand: '', // 设备品牌：
        advertiseId: '', // 广告组ID
        marketCode: '', //  市场代码
        country: '',
        channel: '', // 海外渠道
      },
      currentGenre: 'quick-app',
      appGroupWithAll,
      appGroup,
      appTypeList: [],
      allAppList: [],
      typeStyle: [
        'primary',
        'success',
        'warning',
        'danger',
        'info',
        'primary',
        'success',
        'warning',
        'danger',
        'info',
      ],
      currentAppCode: '',
      currentAppGroupId: '',
      chartsNum: 0,
      chartsTotal: 6, // 图表数量，得一直改，在取消请求未做之前
      advertiseIdList: [],
      brandList: [],
      marketCodeList: [],
      appClassification,
    }
  },
  computed: {
    currentGenreAppList() {
      return this.allAppList
        .filter(it => this.currentGenre == it.genre)
        .sort((a, b) => a.groupId - b.groupId)
    },
    canSwitch() {
      // 所有的图表请求完才能继续点击
      return this.chartsNum >= this.chartsTotal
    },
    requestParams() {
      return {
        ...this.dataForm,
        appCode: this.currentAppCode,
        groupId: this.currentAppGroupId,
        brand: this.dataForm.brand || '',
      }
    },
    showAll() {
      return !this.$store.state.global.outUserNameList.includes(
        this.$store.state.user.name
      )
    },
  },
  watch: {
    currentGenre: {
      immediate: true,
      handler() {
        if (this.currentGenre === 'quick-app') {
          this.$refs.quickAppMonitor.getData()
        } else {
          if (this.currentGenre === '0') {
            this.currentAppGroupId = Array.from(this.appGroupWithAll.keys())[0]
            this.currentAppCode = 0
          }

          // 重置
          this.chartsNum = 0
        }
      },
    },
    currentGenreAppList: {
      immediate: true,
      handler() {
        if (this.currentGenreAppList && this.currentGenreAppList[0]) {
          this.currentAppCode = this.currentGenreAppList[0].code
        }
      },
    },
    currentAppCode: {
      immediate: true,
      handler() {
        if (this.currentGenreAppList && this.currentGenreAppList.length) {
          this.currentAppGroupId = this.allAppList.find(
            it => this.currentAppCode === it.code
          ).groupId
        }

        this.dataForm.mediaType = null
        this.dataForm.brand = null
        this.dataForm.advertiseId = null
        this.dataForm.marketCode = null
        this.getSelectReApp()

        // 重置
        this.chartsNum = 0
      },
    },
  },
  activated() {
    this.getAppList()
  },
  methods: {
    handleChangeAppGroup(groupId) {
      this.currentAppGroupId = groupId
    },
    handleChangeApp(item) {
      this.currentAppCode = item.code
    },
    handleClick(tab) {
      console.log(tab)
    },
    getAppList() {
      this.$store.dispatch('api/app/getAppListWithRole').then(({ data }) => {
        if (data && data.code === 0) {
          const apps = data.apps
          this.allAppList = apps
          this.appTypeList = unionBy(
            apps.map(it => ({
              ...it,
              genreName: appClassification.get(it.genre),
            })),
            'genre'
          )
          if (this.appTypeList) {
            // this.currentGenre = this.appTypeList[0].genre.toString()
          }
        }
      })
    },
    handleRequestFinish() {
      this.chartsNum++
    },
    baseGet(url) {
      return this.$http({
        url: this.$http.adornUrl(url),
        method: 'get',
        params: this.$http.adornParams({
          appCode: this.currentAppCode,
        }),
      })
    },
    // 获取筛选项的值,app相关
    getSelectReApp() {
      // 投放账号
      const pAdvertiseInfo = this.baseGet(
        '/stat/activekeepdaily/advertise_info'
      )
      // #查询某个app 最近30天的品牌
      const pBrandList = this.baseGet('/stat/activekeepdaily/brand_list')
      // 查询某个app 最近30天的market_code
      const pMarketCodeList = this.baseGet(
        '/stat/activekeepdaily/market_code_list'
      )

      Promise.allSettled([pBrandList, pAdvertiseInfo, pMarketCodeList]).then(
        results => {
          results.forEach(({ value }, index) => {
            switch (index) {
              case 0:
                if (value && value.data.code === 0 && value.data.data) {
                  this.brandList = value.data.data.filter(it => it)
                  this.brandList = uniq([...firstBrandList, ...this.brandList])
                } else {
                  this.brandList = []
                }
                break
              case 1:
                if (
                  value &&
                  value.data.code === 0 &&
                  value.data.data &&
                  Array.isArray(value.data.data)
                ) {
                  this.advertiseIdList = uniq(
                    value.data.data.map(it => it.advertiseId)
                  )
                } else {
                  this.advertiseIdList = []
                }
                break
              case 2:
                if (value && value.data.code === 0 && value.data.data) {
                  this.marketCodeList = value.data.data.filter(it => it)
                } else {
                  this.marketCodeList = []
                }
                break
            }
          })
        }
      )
    },
  },
}
</script>

<style scoped lang="scss">
.row-box {
  margin-bottom: 20px;
}

.tab-item-box {
  margin-bottom: 20px;
}
.tab-item {
  position: relative;
  display: inline-block;
  padding: 5px;

  .group {
    position: absolute;
    right: -7px;
    top: 0;
    font-size: 12px;
    color: #fff;
    padding: 3px 7px;
    border-radius: 5px;
    background: #409eff;

    &.main,
    &.primary {
      background: #409eff;
    }
    &.success {
      background: #67c23a;
    }
    &.warning {
      background: #e6a23c;
    }
    &.warning-2 {
      background: #e6693c;
    }
    &.danger {
      background: #f56c6c;
    }
    &.danger-2 {
      background: #f59c6c;
    }
    &.info {
      background: #909399;
    }
  }
}
</style>
