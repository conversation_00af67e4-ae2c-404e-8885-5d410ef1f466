<template>
  <page-table
    ref="page-table"
    :grid-config="gridOptions"
    :request-collection="request"
    :features="['select']"
    operate-width="180"
    :show-operate="false"
    :before-select="beforeSelect"
    :adaptive-height="false"
    @query-success="handleQuerySuccess"
  >
    <!--使用 top 插槽-->
    <template #top>
      <el-alert>
        dnu为库里归因，cpi为拉到的cost/转化
      </el-alert>
      <div class="chars-box">
        <div style="flex: 1">
          <forecast-charts v-bind="charsOptions" />
        </div>
        <div class="operator">
          <h2>过滤条件</h2>
          <el-form label-position="left">
            <el-form-item label="国家">
              <country-select
                v-model="chartsCountry"
                :has-all="false"
                @change="setCharsOptions"
              />
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="chartsType" @change="setCharsOptions">
                <el-option value="cost" label="消耗" />
                <el-option value="dnu" label="dnu" />
                <el-option value="cpi" label="cpi" />
                <el-option value="ipu" label="ipu" />
                <el-option value="ipmRate" label="广告曝光比" />
                <el-option value="arpu" label="arpu" />
                <el-option value="ecpm" label="ecpm" />
                <el-option value="roi0" label="roi0" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </template>
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appCode"
        :is-show-all="false"
        multiple
        clearable
        collapse-tags
        width="240px"
        @change="reloadQuery"
      />
    </template>
    <template #form_channel>
      <arr-select
        :list="channelList"
        v-model="selectFormData.channel"
        value-key="value"
        label-key="label"
        clearable
        @change="reloadQuery"
      />
    </template>
    <template #form_date>
      <el-date-picker
        v-model="selectFormData.date"
        placeholder="选择日期时间"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
      />
    </template>
    <template #form_time>
      <el-time-select
        v-model="selectFormData.time"
        :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '23:00',
        }"
        :disabled="!selectFormData.date"
        placeholder="选择时间"
      />
    </template>
    <template #table_item_appCode="{row}">
      <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
    </template>
  </page-table>
</template>

<script>
import { hwRealtimePredictDataRequest as request } from '@/api/stat'
import { countryList } from '@/map/sat'
import ForecastCharts from '@/views/modules/stat/components/forecast-charts.vue'
import CountrySelect from '@/components/country-select/index.vue'
import dayjs from '@/dayjs'

export default {
  components: {
    CountrySelect,
    ForecastCharts,
  },
  data() {
    return {
      channelList: [
        { label: 'tiktok', value: 1 },
        { label: 'google', value: 2 },
        { label: 'mint', value: 3 },
        { label: 'facebook', value: 4 },
        { label: '快手', value: 5 },
        { label: 'bigo', value: 6 },
      ],
      request,
      gridOptions: {
        maxHeight: 1400,
        pagerConfig: {
          perfect: false,
          pageSize: 2000,
        },
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 120,
            slots: { default: 'table_item_appCode' },
          },
          {
            field: 'country',
            title: '国家',
            minWidth: 90,
            formatter: ({ row }) => this.getCountryName(row.country),
          },
          { field: 'cost', title: '消耗', minWidth: 60 },
          { field: 'dnu', title: 'dnu', minWidth: 60 },
          { field: 'cpi', title: '转化成本', minWidth: 80 },
          { field: 'ipu', title: 'ipu', minWidth: 60 },
          { field: 'ipmRate', title: '广告曝光比', minWidth: 80 },
          { field: 'arpu', title: 'arpu', minWidth: 60 },
          { field: 'ecpm', title: 'ecpm', minWidth: 60 },
          { field: 'roi0', title: 'roi0', minWidth: 60 },
          { field: 'day', title: '日期', minWidth: 80 },
          { field: 'hour', title: '小时', minWidth: 80 },
          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          { field: 'updatedAt', title: '更新时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            {
              title: '应用',
              field: 'appCode',
              slots: { default: 'form_appCode' },
            },
            {
              title: '媒体',
              field: 'channel',
              slots: { default: 'form_channel' },
            },
            {
              title: '日期',
              field: 'date',
              slots: { default: 'form_date' },
            },
            {
              title: '时间',
              field: 'time',
              slots: { default: 'form_time' },
            },
          ],
          // rules: {
          //   appCode: [
          //     {
          //       required: true,
          //     },
          //   ],
          // },
        },
      },
      modelConfig: {
        modelConfig: { width: '500px' },
        formConfig: { labelWidth: '140px' },
      },
      selectFormData: {
        appCode: [10090],
        channel: 1,
        date: '',
        time: '',
      },
      beforeSelect: () => {
        if (this.selectFormData.appCode && this.selectFormData.channel) {
          let date = ''
          if (this.selectFormData.date) {
            date = this.selectFormData.date
            if (this.selectFormData.time) {
              date = date + ' ' + this.selectFormData.time + ':00'
            }
          }
          return {
            appCode: this.selectFormData.appCode.join(),
            channel: this.selectFormData.channel,
            date: date,
          }
        } else {
          throw new Error('请先选择app和媒体')
        }
      },
      lastDay5Data: {},
      chartsCountry: 'ID',
      chartsType: 'roi0',
      charsOptions: {
        title: '',
        series: [],
        xAxisData: [
          '00',
          '01',
          '02',
          '03',
          '04',
          '05',
          '06',
          '07',
          '08',
          '09',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
        ],
      },
    }
  },
  watch: {
    'selectFormData.date'(value) {
      if (!value) {
        this.selectFormData.time = ''
      }
    },
  },
  methods: {
    handleQuerySuccess(res) {
      if (res && res.page && res.page.list) {
        this.lastDay5Data = this.getLastDay5Data(res.page.list)
        console.log('lastDay5Data', this.lastDay5Data)
        this.setCharsOptions()
      }
    },
    // 根据字段分组
    groupArray(list, field) {
      if (!field) {
        throw new Error('groupArray方法： field字段必传')
      }
      const result = {}
      list.forEach(it => {
        const key = it[field]
        if (!result[key]) {
          result[key] = []
        }
        result[key].push(it)
      })
      return result
    },
    sortAndFilterObj(obj, filterNum) {
      const keys = Object.keys(obj)
        .sort((a, b) => b - a)
        .filter((_, index) => index < filterNum)

      const result = {}
      keys.forEach(it => (result[it] = obj[it]))

      return result
    },
    // 获取最近5天的数据
    getLastDay5Data(list) {
      // 根据天分组
      const groupDay = this.groupArray(list, 'day')
      for (const groupDayKey in groupDay) {
        const item = groupDay[groupDayKey]
        // 内部还需要根据国家分组
        groupDay[groupDayKey] = this.groupArray(item, 'country')
      }
      /**
       * 此时数据形式为：
       * {
       *   '20230508': {
       *     'AR': [{
       *       hour: '02:00:00'
       *       arpu: 1,
       *       ecpm: 3,
       *       ...
       *     }]
       *   }
       *   ...
       * }
       * 最终需要转变为:
       * [
       *  {
       *    name: '20230508',
       *    // data里的数据需要看具体字段
       *    // 也是必须为24个数据
       *    data: [1,2,3,...]
       *  }
       *  ...
       * ]
       * x轴的数据：[0~23]
       */
      return this.sortAndFilterObj(groupDay, 5)
    },
    setCharsOptions() {
      if (this.lastDay5Data && this.chartsCountry && this.chartsType) {
        this.charsOptions.series = []
        const countryName = this.getCountryName(this.chartsCountry)
        this.charsOptions.title = `${countryName}/${this.chartsType}`
        for (const key in this.lastDay5Data) {
          const item = this.lastDay5Data[key]
          // 某个国家数据
          // 理论上来说，这里面的数据会是 0-23 小时的数据，只会丢失后面的数据
          // 例如只有 0-12
          // 但是测试环境会存在 [0,1,3] 这种情况
          const countryData = item[this.chartsCountry]
          if (countryData) {
            // 某个分类的数据,
            // 此时不确定是否有24个小时,会缺失数据
            // 这里补0
            const typeData = this.charsOptions.xAxisData.map(it => {
              const res = countryData.find(item => item.hour.startsWith(it))
              return res ? res[this.chartsType] : undefined
            })
            this.charsOptions.series.push({
              name: dayjs(key).format('YYYY-MM-DD'),
              data: typeData,
              type: 'line',
              // stack: 'Total',
              smooth: true,
              showSymbol: false,
            })
          }
        }
      }
    },
    getCountryName(key) {
      const res = countryList.find(it => it.country === key)
      return res ? res.country_name : ''
    },
    reloadQuery() {
      this.$refs['page-table'].reloadQuery()
    },
  },
}
</script>

<style lang="scss" scoped>
.chars-box {
  display: flex;
  padding-top: 30px;
  padding-bottom: 20px;

  .operator {
    width: 300px;
    padding-left: 30px;
    box-sizing: border-box;
    border: 1px solid #eee;
    padding-top: 30px;
    border-radius: 8px;
    margin-left: 20px;

    h2 {
      margin-top: 0;
    }
  }
}
</style>
