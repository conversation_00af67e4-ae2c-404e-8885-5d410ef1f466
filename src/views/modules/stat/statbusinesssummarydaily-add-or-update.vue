<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
    </el-form-item>
    <el-form-item label="月份" prop="month">
      <el-input v-model="dataForm.month" placeholder="月份"></el-input>
    </el-form-item>
    <el-form-item label="年" prop="year">
      <el-input v-model="dataForm.year" placeholder="年"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="新增统计" prop="newCount">
      <el-input v-model="dataForm.newCount" placeholder="新增统计"></el-input>
    </el-form-item>
    <el-form-item label="活跃统计" prop="aliveCount">
      <el-input v-model="dataForm.aliveCount" placeholder="活跃统计"></el-input>
    </el-form-item>
    <el-form-item label="新用户首页到达统计" prop="newArriveFrontCount">
      <el-input v-model="dataForm.newArriveFrontCount" placeholder="新用户首页到达统计"></el-input>
    </el-form-item>
    <el-form-item label="新用户首页到达率" prop="newArriveFrontRate">
      <el-input v-model="dataForm.newArriveFrontRate" placeholder="新用户首页到达率"></el-input>
    </el-form-item>
    <el-form-item label="活跃用户首页到达统计" prop="aliveArriveFrontCount">
      <el-input v-model="dataForm.aliveArriveFrontCount" placeholder="活跃用户首页到达统计"></el-input>
    </el-form-item>
    <el-form-item label="活跃用户首页达到率" prop="aliveArriveFrontRate">
      <el-input v-model="dataForm.aliveArriveFrontRate" placeholder="活跃用户首页达到率"></el-input>
    </el-form-item>
    <el-form-item label="外部场景触发人数" prop="externalSceneCount">
      <el-input v-model="dataForm.externalSceneCount" placeholder="外部场景触发人数"></el-input>
    </el-form-item>
    <el-form-item label="外部场景触发率" prop="externalSceneRate">
      <el-input v-model="dataForm.externalSceneRate" placeholder="外部场景触发率"></el-input>
    </el-form-item>
    <el-form-item label="穿山甲收入" prop="csjIncome">
      <el-input v-model="dataForm.csjIncome" placeholder="穿山甲收入"></el-input>
    </el-form-item>
    <el-form-item label="优量汇收入" prop="ylhIncome">
      <el-input v-model="dataForm.ylhIncome" placeholder="优量汇收入"></el-input>
    </el-form-item>
    <el-form-item label="快手收入" prop="ksIncome">
      <el-input v-model="dataForm.ksIncome" placeholder="快手收入"></el-input>
    </el-form-item>
    <el-form-item label="百度收入" prop="baiduIncome">
      <el-input v-model="dataForm.baiduIncome" placeholder="百度收入"></el-input>
    </el-form-item>
    <el-form-item label="百度锁屏收入" prop="externalBaiduInfoIncome">
      <el-input v-model="dataForm.externalBaiduInfoIncome" placeholder="百度锁屏收入"></el-input>
    </el-form-item>
    <el-form-item label="成本" prop="cost">
      <el-input v-model="dataForm.cost" placeholder="成本"></el-input>
    </el-form-item>
    <el-form-item label="总收入" prop="totalRevenue">
      <el-input v-model="dataForm.totalRevenue" placeholder="总收入"></el-input>
    </el-form-item>
    <el-form-item label="广告展示量" prop="adImpressions">
      <el-input v-model="dataForm.adImpressions" placeholder="广告展示量"></el-input>
    </el-form-item>
    <el-form-item label="百度广告展示量" prop="baiduImpressions">
      <el-input v-model="dataForm.baiduImpressions" placeholder="百度广告展示量"></el-input>
    </el-form-item>
    <el-form-item label="快手广告展示量" prop="ksImpressions">
      <el-input v-model="dataForm.ksImpressions" placeholder="快手广告展示量"></el-input>
    </el-form-item>
    <el-form-item label="优量汇广告展示量" prop="ylhImpressions">
      <el-input v-model="dataForm.ylhImpressions" placeholder="优量汇广告展示量"></el-input>
    </el-form-item>
    <el-form-item label="穿山甲广告展示量" prop="csjImpressions">
      <el-input v-model="dataForm.csjImpressions" placeholder="穿山甲广告展示量"></el-input>
    </el-form-item>
    <el-form-item label="锁屏广告展示量(百度资讯广告展示量)" prop="lockScreenImpressions">
      <el-input v-model="dataForm.lockScreenImpressions" placeholder="锁屏广告展示量(百度资讯广告展示量)"></el-input>
    </el-form-item>
    <el-form-item label="人均广告展示次数广告AIPU" prop="aiPu">
      <el-input v-model="dataForm.aiPu" placeholder="人均广告展示次数广告AIPU"></el-input>
    </el-form-item>
    <el-form-item label="锁屏AIPU" prop="lockScreenAiPu">
      <el-input v-model="dataForm.lockScreenAiPu" placeholder="锁屏AIPU"></el-input>
    </el-form-item>
    <el-form-item label="每用户平均收入" prop="arPu">
      <el-input v-model="dataForm.arPu" placeholder="每用户平均收入"></el-input>
    </el-form-item>
    <el-form-item label="广告ecpm" prop="ecpm">
      <el-input v-model="dataForm.ecpm" placeholder="广告ecpm"></el-input>
    </el-form-item>
    <el-form-item label="锁屏ecpm" prop="lockScreenEcpm">
      <el-input v-model="dataForm.lockScreenEcpm" placeholder="锁屏ecpm"></el-input>
    </el-form-item>
    <el-form-item label="累计ROI" prop="roi">
      <el-input v-model="dataForm.roi" placeholder="累计ROI"></el-input>
    </el-form-item>
    <el-form-item label="新增cpa" prop="cpaByNew">
      <el-input v-model="dataForm.cpaByNew" placeholder="新增cpa"></el-input>
    </el-form-item>
    <el-form-item label="活跃cpa" prop="cpaByArrive">
      <el-input v-model="dataForm.cpaByArrive" placeholder="活跃cpa"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updatedBy">
      <el-input v-model="dataForm.updatedBy" placeholder="更新者"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          month: '',
          year: '',
          day: '',
          newCount: '',
          aliveCount: '',
          newArriveFrontCount: '',
          newArriveFrontRate: '',
          aliveArriveFrontCount: '',
          aliveArriveFrontRate: '',
          externalSceneCount: '',
          externalSceneRate: '',
          csjIncome: '',
          ylhIncome: '',
          ksIncome: '',
          baiduIncome: '',
          externalBaiduInfoIncome: '',
          cost: '',
          totalRevenue: '',
          adImpressions: '',
          baiduImpressions: '',
          ksImpressions: '',
          ylhImpressions: '',
          csjImpressions: '',
          lockScreenImpressions: '',
          aiPu: '',
          lockScreenAiPu: '',
          arPu: '',
          ecpm: '',
          lockScreenEcpm: '',
          roi: '',
          cpaByNew: '',
          cpaByArrive: '',
          createdAt: '',
          updatedAt: '',
          updatedBy: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用id不能为空', trigger: 'blur' }
          ],
          month: [
            { required: true, message: '月份不能为空', trigger: 'blur' }
          ],
          year: [
            { required: true, message: '年不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          newCount: [
            { required: true, message: '新增统计不能为空', trigger: 'blur' }
          ],
          aliveCount: [
            { required: true, message: '活跃统计不能为空', trigger: 'blur' }
          ],
          newArriveFrontCount: [
            { required: true, message: '新用户首页到达统计不能为空', trigger: 'blur' }
          ],
          newArriveFrontRate: [
            { required: true, message: '新用户首页到达率不能为空', trigger: 'blur' }
          ],
          aliveArriveFrontCount: [
            { required: true, message: '活跃用户首页到达统计不能为空', trigger: 'blur' }
          ],
          aliveArriveFrontRate: [
            { required: true, message: '活跃用户首页达到率不能为空', trigger: 'blur' }
          ],
          externalSceneCount: [
            { required: true, message: '外部场景触发人数不能为空', trigger: 'blur' }
          ],
          externalSceneRate: [
            { required: true, message: '外部场景触发率不能为空', trigger: 'blur' }
          ],
          csjIncome: [
            { required: true, message: '穿山甲收入不能为空', trigger: 'blur' }
          ],
          ylhIncome: [
            { required: true, message: '优量汇收入不能为空', trigger: 'blur' }
          ],
          ksIncome: [
            { required: true, message: '快手收入不能为空', trigger: 'blur' }
          ],
          baiduIncome: [
            { required: true, message: '百度收入不能为空', trigger: 'blur' }
          ],
          externalBaiduInfoIncome: [
            { required: true, message: '百度锁屏收入不能为空', trigger: 'blur' }
          ],
          cost: [
            { required: true, message: '成本不能为空', trigger: 'blur' }
          ],
          totalRevenue: [
            { required: true, message: '总收入不能为空', trigger: 'blur' }
          ],
          adImpressions: [
            { required: true, message: '广告展示量不能为空', trigger: 'blur' }
          ],
          baiduImpressions: [
            { required: true, message: '百度广告展示量不能为空', trigger: 'blur' }
          ],
          ksImpressions: [
            { required: true, message: '快手广告展示量不能为空', trigger: 'blur' }
          ],
          ylhImpressions: [
            { required: true, message: '优量汇广告展示量不能为空', trigger: 'blur' }
          ],
          csjImpressions: [
            { required: true, message: '穿山甲广告展示量不能为空', trigger: 'blur' }
          ],
          lockScreenImpressions: [
            { required: true, message: '锁屏广告展示量(百度资讯广告展示量)不能为空', trigger: 'blur' }
          ],
          aiPu: [
            { required: true, message: '人均广告展示次数广告AIPU不能为空', trigger: 'blur' }
          ],
          lockScreenAiPu: [
            { required: true, message: '锁屏AIPU不能为空', trigger: 'blur' }
          ],
          arPu: [
            { required: true, message: '每用户平均收入不能为空', trigger: 'blur' }
          ],
          ecpm: [
            { required: true, message: '广告ecpm不能为空', trigger: 'blur' }
          ],
          lockScreenEcpm: [
            { required: true, message: '锁屏ecpm不能为空', trigger: 'blur' }
          ],
          roi: [
            { required: true, message: '累计ROI不能为空', trigger: 'blur' }
          ],
          cpaByNew: [
            { required: true, message: '新增cpa不能为空', trigger: 'blur' }
          ],
          cpaByArrive: [
            { required: true, message: '活跃cpa不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          updatedBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/statbusinesssummarydaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.statBusinessSummaryDaily.appId
                this.dataForm.month = data.statBusinessSummaryDaily.month
                this.dataForm.year = data.statBusinessSummaryDaily.year
                this.dataForm.day = data.statBusinessSummaryDaily.day
                this.dataForm.newCount = data.statBusinessSummaryDaily.newCount
                this.dataForm.aliveCount = data.statBusinessSummaryDaily.aliveCount
                this.dataForm.newArriveFrontCount = data.statBusinessSummaryDaily.newArriveFrontCount
                this.dataForm.newArriveFrontRate = data.statBusinessSummaryDaily.newArriveFrontRate
                this.dataForm.aliveArriveFrontCount = data.statBusinessSummaryDaily.aliveArriveFrontCount
                this.dataForm.aliveArriveFrontRate = data.statBusinessSummaryDaily.aliveArriveFrontRate
                this.dataForm.externalSceneCount = data.statBusinessSummaryDaily.externalSceneCount
                this.dataForm.externalSceneRate = data.statBusinessSummaryDaily.externalSceneRate
                this.dataForm.csjIncome = data.statBusinessSummaryDaily.csjIncome
                this.dataForm.ylhIncome = data.statBusinessSummaryDaily.ylhIncome
                this.dataForm.ksIncome = data.statBusinessSummaryDaily.ksIncome
                this.dataForm.baiduIncome = data.statBusinessSummaryDaily.baiduIncome
                this.dataForm.externalBaiduInfoIncome = data.statBusinessSummaryDaily.externalBaiduInfoIncome
                this.dataForm.cost = data.statBusinessSummaryDaily.cost
                this.dataForm.totalRevenue = data.statBusinessSummaryDaily.totalRevenue
                this.dataForm.adImpressions = data.statBusinessSummaryDaily.adImpressions
                this.dataForm.baiduImpressions = data.statBusinessSummaryDaily.baiduImpressions
                this.dataForm.ksImpressions = data.statBusinessSummaryDaily.ksImpressions
                this.dataForm.ylhImpressions = data.statBusinessSummaryDaily.ylhImpressions
                this.dataForm.csjImpressions = data.statBusinessSummaryDaily.csjImpressions
                this.dataForm.lockScreenImpressions = data.statBusinessSummaryDaily.lockScreenImpressions
                this.dataForm.aiPu = data.statBusinessSummaryDaily.aiPu
                this.dataForm.lockScreenAiPu = data.statBusinessSummaryDaily.lockScreenAiPu
                this.dataForm.arPu = data.statBusinessSummaryDaily.arPu
                this.dataForm.ecpm = data.statBusinessSummaryDaily.ecpm
                this.dataForm.lockScreenEcpm = data.statBusinessSummaryDaily.lockScreenEcpm
                this.dataForm.roi = data.statBusinessSummaryDaily.roi
                this.dataForm.cpaByNew = data.statBusinessSummaryDaily.cpaByNew
                this.dataForm.cpaByArrive = data.statBusinessSummaryDaily.cpaByArrive
                this.dataForm.createdAt = data.statBusinessSummaryDaily.createdAt
                this.dataForm.updatedAt = data.statBusinessSummaryDaily.updatedAt
                this.dataForm.updatedBy = data.statBusinessSummaryDaily.updatedBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/statbusinesssummarydaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'month': this.dataForm.month,
                'year': this.dataForm.year,
                'day': this.dataForm.day,
                'newCount': this.dataForm.newCount,
                'aliveCount': this.dataForm.aliveCount,
                'newArriveFrontCount': this.dataForm.newArriveFrontCount,
                'newArriveFrontRate': this.dataForm.newArriveFrontRate,
                'aliveArriveFrontCount': this.dataForm.aliveArriveFrontCount,
                'aliveArriveFrontRate': this.dataForm.aliveArriveFrontRate,
                'externalSceneCount': this.dataForm.externalSceneCount,
                'externalSceneRate': this.dataForm.externalSceneRate,
                'csjIncome': this.dataForm.csjIncome,
                'ylhIncome': this.dataForm.ylhIncome,
                'ksIncome': this.dataForm.ksIncome,
                'baiduIncome': this.dataForm.baiduIncome,
                'externalBaiduInfoIncome': this.dataForm.externalBaiduInfoIncome,
                'cost': this.dataForm.cost,
                'totalRevenue': this.dataForm.totalRevenue,
                'adImpressions': this.dataForm.adImpressions,
                'baiduImpressions': this.dataForm.baiduImpressions,
                'ksImpressions': this.dataForm.ksImpressions,
                'ylhImpressions': this.dataForm.ylhImpressions,
                'csjImpressions': this.dataForm.csjImpressions,
                'lockScreenImpressions': this.dataForm.lockScreenImpressions,
                'aiPu': this.dataForm.aiPu,
                'lockScreenAiPu': this.dataForm.lockScreenAiPu,
                'arPu': this.dataForm.arPu,
                'ecpm': this.dataForm.ecpm,
                'lockScreenEcpm': this.dataForm.lockScreenEcpm,
                'roi': this.dataForm.roi,
                'cpaByNew': this.dataForm.cpaByNew,
                'cpaByArrive': this.dataForm.cpaByArrive,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt,
                'updatedBy': this.dataForm.updatedBy
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
