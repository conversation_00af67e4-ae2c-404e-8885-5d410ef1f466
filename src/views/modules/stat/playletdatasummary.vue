<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="APPID" prop="status">
        <el-select v-model="dataForm.appId" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期选择">
        <el-date-picker
          clearable
          v-model="dataForm.date"
          :type="dateType"
          :format="dateFormat"
          :value-format="dateValueFormat"
          placeholder="选择日期，右侧切换日期格式"
        />
        <el-select
          v-model="dateType"
          @change="changeDateFormat"
          style="width: 70px"
        >
          <el-option
            v-for="item in dateTypeList"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:playletdatasummary:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <el-button
          v-if="isAuth('stat:playletdatasummary:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="appId"
      />
      <el-table-column
        prop="dt"
        header-align="center"
        align="center"
        label="日期"
      >
        <template slot-scope="{ row }">
          <span v-if="dateType === 'date'">{{ row.dt }}</span>
          <span v-if="dateType === 'month'">
            {{ formatDateMonth(row.month) }}
          </span>
          <span v-if="dateType === 'year'">{{ row.year }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="newNum"
        header-align="center"
        align="center"
        label="新增人数"
      />
      <el-table-column
        prop="cost"
        header-align="center"
        align="center"
        label="新增成本"
      />
      <el-table-column
        prop="payNum"
        header-align="center"
        align="center"
        label="新增付费人数"
      />
      <el-table-column
        prop="payRate"
        header-align="center"
        align="center"
        label="新增付费率"
      />
      <el-table-column
        prop="payAmount"
        header-align="center"
        align="center"
        label="新增付费金额"
      />
      <el-table-column
        prop="clickRate"
        header-align="center"
        align="center"
        label="新增点击率"
      />
      <el-table-column
        prop="roi"
        header-align="center"
        align="center"
        label="新增roi"
      />
      <el-table-column
        prop="subNum"
        header-align="center"
        align="center"
        label="新增订阅人数"
      />
      <el-table-column
        prop="subAmount"
        header-align="center"
        align="center"
        label="新增订阅金额"
      />
      <el-table-column
        prop="renewalNum"
        header-align="center"
        align="center"
        label="总续费人数"
      />
      <el-table-column
        prop="renewalAmount"
        header-align="center"
        align="center"
        label="总续费金额"
      />
      <el-table-column
        prop="totalAmount"
        header-align="center"
        align="center"
        width="180px"
        label="当日总付费金额(新增+续费)"
      />
      <!--<el-table-column-->
      <!--  prop="channel"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="1:tt,2:kwai"-->
      <!--/>-->
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './playletdatasummary-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        // key: '',
        date: '',
        appId: '',
      },
      options: [
        { value: 113, label: '113' },
        { value: 116, label: '116' },
        { value: 117, label: '117' },
        { value: 118, label: '118' },
        { value: '', label: 'all' },
      ],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      timeRange: '',
      dateType: 'date',
      dateValueFormat: 'yyyyMMdd',
      dateFormat: 'yyyy-MM-dd',
      columnDateFormat: 'YYYY-MM-DD',
      apiUrl: '/stat/playletdatasummary/list?day=',
      dateTypeList: [
        {
          label: '天',
          value: 'date',
          valueFormat: 'yyyyMMdd',
          format: 'yyyy-MM-dd',
          apiUrl: '/stat/playletdatasummary/list?day=',
        },
        {
          label: '月',
          value: 'month',
          valueFormat: 'yyyyMM',
          format: 'yyyy-MM',
          apiUrl: '/stat/playletdatasummary/month_list?month=',
        },
        {
          label: '年',
          value: 'year',
          valueFormat: 'yyyy',
          format: 'yyyy',
          apiUrl: '/stat/playletdatasummary/year_list?year=',
        },
      ],
      allDataList: [],
    }
  },
  watch: {
    timeRange(t) {
      if (t) {
        this.dataForm.beginTime = t[0]
        this.dataForm.endTime = t[1]
      } else {
        this.dataForm.beginTime = ''
        this.dataForm.endTime = ''
      }
    },
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    formatDateMonth(value) {
      // 将数值转换为字符串
      if (!value) return ''
      const valueStr = value.toString()
      // 提取年份和月份
      const year = valueStr.slice(0, 4)
      const month = valueStr.slice(4, 6)

      // 格式化为 YYYY-MM
      const formattedDate = `${year}-${month}`

      return formattedDate
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl(this.apiUrl + (this.dataForm.date || 0)),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appId: this.dataForm.appId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          if (this.dateType === 'date') {
            this.dataList = data.page.list.map(item => {
              return {
                ...item,
                payRate: item.payRate
                  ? (item.payRate * 100).toFixed(2) + '%'
                  : 0,
                clickRate: item.clickRate
                  ? (item.clickRate * 100).toFixed(2) + '%'
                  : 0,
              }
            })
            this.totalPage = data.page.totalCount
          } else {
            this.allDataList = data.data
            // this.allDataList = new Array(36).fill(data.data).flat() // 模拟数据
            this.getCurrentDataList()
            this.totalPage = this.allDataList.length
          }
        } else {
          // this.allDataList = data.data
          // // this.dataList = data.page
          // this.getCurrentDataList()
          // this.totalPage = data.page.length
          this.dataList = []
          this.totalPage = 0
          this.$message.error(data.msg || '服务器错误')
        }
        this.dataListLoading = false
      })
    },
    getCurrentDataList() {
      this.dataList = this.allDataList.slice(
        (this.pageIndex - 1) * this.pageSize,
        (this.pageIndex - 1) * this.pageSize + this.pageSize
      )
      console.log(this.allDataList, this.pageIndex, this.dataList)
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/playletdatasummary/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    changeDateFormat(v) {
      console.log(v)
      this.pageIndex = 1
      const res = this.dateTypeList.find(it => it.value === v)
      console.log(res)
      this.dateValueFormat = res.valueFormat
      this.dateFormat = res.format
      this.apiUrl = res.apiUrl
      this.dataForm.date = ''
    },
  },
}
</script>
