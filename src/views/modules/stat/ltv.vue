<template>
  <div>
    <base-form :inline="true" :model.sync="dataForm" @submit="getDataList">
      <el-form-item label="选择应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="selectTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyyMMdd"
        />
      </el-form-item>
    </base-form>
    <el-table
      class="cus-table"
      :data="dataList"
      border
      style="width: 100%"
      v-loading="dataListLoading"
    >
      <!--<af-table-column prop="id" label="ID" align="center" width="50" />-->
      <af-table-column prop="day" label="日期" align="center" width="80">
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </af-table-column>
      <af-table-column prop="appCode" label="应用" align="center">
        <template slot-scope="{ row }">
          <el-tag>
            {{ row.appCode | getAppName }}
          </el-tag>
        </template>
      </af-table-column>
      <af-table-column prop="cpa" label="CPA" align="center" />
      <af-table-column prop="newAddNums" label="新增用户" align="center" />
      <af-table-column prop="activeNums" label="活跃用户" align="center" />
      <af-table-column
        v-for="item in 30"
        :key="item"
        :label="`LTV${item}/累计ROI${item}`"
        width="130"
        align="center"
      >
        <!--:style="{-->
        <!--background: `rgba(0,162,212, ${row[`roi${item}`] || 0})`,-->
        <!--color:-->
        <!--row[`roi${item}`] && row[`roi${item}`] > 0.8-->
        <!--? 'white'-->
        <!--: 'black',-->
        <!--}"-->
        <template slot-scope="{ row }">
          <div class="tem-col">
            {{ computedCol(row, item) }}
          </div>
        </template>
      </af-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script>
import BaseForm from '@/components/base-form'
import AppSelect from '@/components/app-select'
export default {
  data() {
    return {
      dataForm: {
        startTime: '',
        endTime: '',
      },
      dataListLoading: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
    }
  },
  components: {
    BaseForm,
    AppSelect,
  },
  computed: {
    selectTime: {
      set(time) {
        this.dataForm.startTime = time ? time[0] ?? '' : ''
        this.dataForm.endTime = time ? time[1] ?? '' : ''
      },
      get() {
        return [this.dataForm.startTime, this.dataForm.endTime]
      },
    },
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    getDataList() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)

      this.dataListLoading = true

      this.$http({
        url: this.$http.adornUrl('/stat/activekeepdaily/ltv_list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: res.code || '',
          ...this.dataForm,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    computedCol(row, index) {
      return [
        row[`ltv${index}`] || '-',
        row[`roi${index}`] ? (row[`roi${index}`] * 1000) / 10 + '%' : '-',
      ].join(' / ')
    },
  },
}
</script>

<style scoped lang="scss">
.cus-table {
  &::v-deep {
    .el-table__body-wrapper {
      .el-table__cell {
        padding: 0;
      }

      .cell {
        padding: 0;
      }
    }
  }
}

.tem-col {
  text-align: center;
  padding: 10px;
}
</style>
