<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="选择应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
          :is-store="true"
        />
      </el-form-item>
      <el-form-item label="是否总收入">
        <el-select v-model="dataForm.isSummary" clearable>
          <el-option
            v-for="[key, label] in isSummaryStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="selectTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyyMMdd"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:adnetreportdaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('stat:adnetreportdaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel(false)"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
        width="80"
      />
      <el-table-column
        prop="memberId"
        header-align="center"
        align="center"
        label="开发者账号"
        width="120"
      />
      <el-table-column
        prop="mediumName"
        header-align="center"
        align="center"
        label="	媒体名称"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="媒体id"
        width="120"
      />
      <el-table-column
        prop="placementId"
        header-align="center"
        align="center"
        label="广告位ID"
        width="160"
      />
      <el-table-column
        prop="placementName"
        header-align="center"
        align="center"
        label="广告位名称"
        width="240"
      />
      <el-table-column
        prop="placementType"
        header-align="center"
        align="center"
        label="广告位类型"
        width="120"
      />
      <el-table-column
        prop="date"
        header-align="center"
        align="center"
        label="报表日期"
        width="100"
      />
      <el-table-column
        prop="isSummary"
        header-align="center"
        align="center"
        label="是否总收入"
        width="120"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.isSummary === 1 ? 'danger' : ''">
            {{ isSummaryStatus.get(row.isSummary) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="revenue"
        header-align="center"
        align="center"
        label="收入 ￥"
        width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ row.revenue ? row.revenue.toLocaleString() : '-' }}元</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="revenueUsd"
        header-align="center"
        align="center"
        label="收入 $"
        width="120"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.revenueUsd ? row.revenueUsd.toLocaleString() : '-' }}美元
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="ecpmUsd"
        header-align="center"
        align="center"
        width="120"
      >
        <template slot="header">
          <span>千次展示收入</span>
          <el-tooltip
            content=" (收入 / 曝光量 * 1000) (单位：美元)"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template slot-scope="{ row }">
          <span>{{ row.ecpmUsd }}美元</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="cpcUsd"
        header-align="center"
        align="center"
        width="100"
      >
        <template slot="header">
          <span>点击成本</span>
          <el-tooltip content="(收入 / 点击量) (单位：美元)" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template slot-scope="{ row }">
          <span>{{ row.cpcUsd }}美元</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="usdExchangeRate"
        header-align="center"
        align="center"
        width="110"
      >
        <template slot="header">
          <span>汇率中间价</span>
          <el-tooltip
            content="美元/人民币汇率中间价(天级别更新)"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="ecpm"
        header-align="center"
        align="center"
        width="120"
      >
        <template slot="header">
          <span>千次展示收入</span>
          <el-tooltip
            content="(收入 / 曝光量 * 1000) (单位：元)"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="cpc"
        header-align="center"
        align="center"
        width="100"
      >
        <template slot="header">
          <span>点击成本</span>
          <el-tooltip content=" (收入 / 点击量) (单位：元)" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="requestCount"
        header-align="center"
        align="center"
        label="广告位请求量"
        width="110"
      />
      <el-table-column
        prop="returnCount"
        header-align="center"
        align="center"
        label="广告位返回量"
        width="110"
      />
      <el-table-column
        prop="adRequestCount"
        header-align="center"
        align="center"
        label="广告请求量"
        width="110"
      />
      <el-table-column
        prop="adReturnCount"
        header-align="center"
        align="center"
        label="广告返回量"
        width="110"
      />
      <el-table-column
        prop="pv"
        header-align="center"
        align="center"
        label="曝光量"
      />
      <el-table-column
        prop="click"
        header-align="center"
        align="center"
        label="点击量"
      />
      <el-table-column
        prop="fillRate"
        header-align="center"
        align="center"
        width="130"
      >
        <template slot="header">
          <span>广告位填充率</span>
          <el-tooltip
            content="广告位返回量 / 广告位请求量 * 100%"
            placement="top"
          >
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="exposureRate"
        header-align="center"
        align="center"
        width="130"
      >
        <template slot="header">
          <span>广告位曝光率</span>
          <el-tooltip content="曝光量/广告位返回量 * 100%" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="adFillRate"
        header-align="center"
        align="center"
        width="130"
      >
        <template slot="header">
          <span>广告填充率</span>
          <el-tooltip content="广告返回量/广告请求量 * 100%" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="adExposureRate"
        header-align="center"
        align="center"
        width="130"
      >
        <template slot="header">
          <span>广告曝光率</span>
          <el-tooltip content="曝光量 / 广告返回量 * 100%" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="clickRate"
        header-align="center"
        align="center"
        width="100"
      >
        <template slot="header">
          <span>点击率</span>
          <el-tooltip content="点击量 / 曝光量 * 100%" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="priceStrategyType"
        header-align="center"
        align="center"
        label="价格策略类型"
        width="140"
      />
      <el-table-column
        prop="price"
        header-align="center"
        align="center"
        label="设价"
      />
      <el-table-column
        prop="priceLevel"
        header-align="center"
        align="center"
        label="分层标签"
      />

      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建日期"
        width="100"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.createdAt)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.id)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.id)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './adnetreportdaily-add-or-update'

import { isSummaryStatus } from '@/map/common'
import { mixinElTableAdapterHeight } from '@/mixins'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        isSummary: 0, // 是否总收入
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      isSummaryStatus,
      selectTime: '',
    }
  },

  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)

      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/adnetreportdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: res.code, // 选择全部应用时，有些地方需要传0，有些却要空字符串
          startTime: this.selectTime ? this.selectTime[0] ?? '' : '',
          endTime: this.selectTime ? this.selectTime[1] ?? '' : '',
          isSummary: this.dataForm.isSummary ?? '',
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/adnetreportdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
