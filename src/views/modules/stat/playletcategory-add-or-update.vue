<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px"
    @click="handleClose"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="分类名" prop="name">
        <el-input v-model="dataForm.name" placeholder="分类名"></el-input>
      </el-form-item>
      <el-form-item label="序号" prop="serialNo">
        <el-input v-model="dataForm.serialNo" placeholder="序号"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联短剧">
        <el-transfer
          class="my-transfer"
          v-model="selectPlayletList"
          :data="playletList"
          filterable
          target-order="push"
          :titles="['所有剧集', '分类剧集']"
        >
          <div slot-scope="{ option }">
            {{ option.label }}
          </div>
        </el-transfer>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Sortable from 'sortablejs'

export default {
  data() {
    return {
      playletList: [],
      selectPlayletList: [],
      visible: true,
      options: [
        { value: 1, label: '生效' },
        { value: 2, label: '无效' },
        { value: 3, label: '热门推荐配置' },
      ],
      dataForm: {
        id: 0,
        name: '',
        serialNo: '',
        playlets: '',
        status: '',
      },
      dataRule: {
        name: [{ required: true, message: '分类名不能为空', trigger: 'blur' }],
        serialNo: [
          { required: true, message: '序号不能为空', trigger: 'blur' },
        ],
        playlets: [
          { required: true, message: '关联短剧不能为空', trigger: 'blur' },
        ],

        status: [
          {
            required: true,
            message: '1:生效，2：无效,3:是热门推荐配置不能为空',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  mounted() {
    this.getPlayletList()
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/playletcategory/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              data.playletCategory.playlets.forEach((item, index) => {
                this.$set(this.selectPlayletList, index, item.id)
              })
              // this.selectPlayletList = data.playletCategory.playlets.map(
              //   item => item.id
              // )
              this.$forceUpdate()
              console.log(this.selectPlayletList)
              this.dataForm.name = data.playletCategory.name
              this.dataForm.serialNo = data.playletCategory.serialNo
              this.dataForm.playlets = data.playletCategory.playlets
              this.dataForm.status = data.playletCategory.status
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.dataForm.playlets = this.selectPlayletList.map((id, index) => {
            const item = this.playletList.find(playlet => playlet.id === id)
            return {
              id: id,
              name: item.name,
              order: index,
            }
          })

          console.log(
            '提交: this.dataForm.playlets',
            JSON.stringify(this.dataForm.playlets)
          )

          this.$http({
            url: this.$http.adornUrl(
              `/stat/playletcategory/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              serialNo: this.dataForm.serialNo,
              playlets: this.dataForm.playlets,
              status: this.dataForm.status,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getPlayletList() {
      this.$http({
        url: this.$http.adornUrl('/stat/playlet/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 10000,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.playletList = data.page.list.map(item =>
            Object.assign({}, item, {
              key: item.id,
              label: item.name,
            })
          )
        } else {
          this.playletList = []
        }

        console.log('111', this.playletList)

        this.$nextTick(() => {
          const ele = document
            .querySelectorAll('.my-transfer .el-transfer-panel')[1]
            .querySelector(' .el-transfer-panel__list')
          //  .el-transfer-panel__list
          console.log(ele)
          new Sortable(ele, {
            animation: 150,
            ghostClass: 'blue-background-class',
            // 结束拖拽
            onEnd: (/**Event*/ evt) => {
              console.log(this.selectPlayletList);
              this.selectPlayletList = this.arrJumpQueue(
                this.selectPlayletList,
                evt.oldIndex,
                evt.newIndex
              )
              console.log(this.selectPlayletList)
              console.log(evt.oldIndex, evt.newIndex)
              // var itemEl = evt.item;  // dragged HTMLElement
              // evt.to;    // target list
              // evt.from;  // previous list
              // evt.oldIndex;  // element's old index within old parent
              // evt.newIndex;  // element's new index within new parent
              // evt.clone // the clone element
              // evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
            },
          })
        })
      })
    },
    // 数组插队
    arrJumpQueue(arr, oldIndex, newIndex) {
      if (oldIndex === newIndex) {
        return arr
      }

      const jumpItem = [arr[oldIndex]]

      if (newIndex === 0) {
        const a1 = arr.slice(0, oldIndex)
        const a2 = arr.slice(oldIndex + 1)
        return [jumpItem, a1, a2].flat()
      }

      if (newIndex === arr.length - 1) {
        const a1 = arr.slice(0, oldIndex)
        const a2 = arr.slice(oldIndex + 1)
        return [a1, a2, jumpItem].flat()
      }

      if (oldIndex === 0) {
        const a1 = arr.slice(1, newIndex)
        const a2 = arr.slice(newIndex)
        return [a1, jumpItem, a2].flat()
      }

      if (oldIndex === arr.length - 1) {
        const a1 = arr.slice(0, newIndex)
        const a2 = arr.slice(newIndex, oldIndex)
        return [a1, jumpItem, a2].flat()
      }

      if (newIndex > oldIndex) {
        const a1 = arr.slice(0, oldIndex)
        const a2 = arr.slice(oldIndex + 1, newIndex + 1)
        const a3 = arr.slice(newIndex + 1)
        return [a1, a2, jumpItem, a3].flat()
      } else {
        const a1 = arr.slice(0, newIndex)
        const a2 = arr.slice(newIndex, oldIndex)
        const a3 = arr.slice(oldIndex + 1)

        return [a1, jumpItem, a2, a3].flat()
      }
    },
    handleClose() {
      this.$emit('close')
      this.selectPlayletList = []
    },
  },
}
</script>

<style scoped>
.my-transfer::v-deep {
  .el-transfer-panel {
    width: 250px;
  }
}
</style>
