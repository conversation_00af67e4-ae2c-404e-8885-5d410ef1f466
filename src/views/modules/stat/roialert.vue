<template>
  <page-table
    ref="page-table"
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    operate-width="180"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appCode"
        :is-show-all="false"
        @change="loadData"
      />
    </template>
    <template #form_channel>
      <ad-platform-select
        v-model="selectFormData.channel"
        version="2"
        clearable
        @change="loadData"
      />
    </template>
    <template #form_dt>
      <el-date-picker
        v-model="selectFormData.dt"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="日期"
        @change="loadData"
      />
    </template>
  </page-table>
</template>

<script>
import { roiAlert as request } from '@/api/stat'
import { getAppName } from '@/filters'
import { adPlatformList2, countryList } from '@/map/sat'

export default {
  data() {
    return {
      request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', minWidth: 60 },
          // { field: 'id', title: 'ID', minWidth: '140' },
          { field: 'dt', title: '日期', minWidth: '120' },
          {
            field: 'appId',
            title: '应用',
            minWidth: '120',
            formatter: ({ row }) => getAppName(row.appId),
          },

          {
            field: 'network',
            title: '渠道',
            minWidth: '120',
            formatter: ({ row }) => adPlatformList2.get(Number(row.network)),
          },
          {
            field: 'city',
            title: '国家',
            minWidth: '120',
            formatter: ({ row }) => {
              const res = countryList.find(it => it.country === row.city)
              return res ? res.country_name : '-'
            },
          },
          // { field: 'channel', title: '渠道详情', minWidth: '120' },
          { field: 'firstRoi', title: '首日roi', minWidth: '120' },
          { field: 'targetRoi', title: '目标roi', minWidth: '120' },
          { field: 'cost', title: '消耗', minWidth: '120' },
          {
            field: 'status',
            title: '状态',
            minWidth: '120',
            formatter: ({ row }) => {
              if (!row.status) {
                return '-'
              }
              if (row.status === 1) {
                return '合格'
              }
              if (row.status === 2) {
                return '不合格'
              }
              return '未知类型'
            },
          },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              field: 'appCode',
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              field: 'dt',
              title: '日期',
              slots: {
                default: 'form_dt',
              },
            },
            {
              field: 'channel',
              title: '渠道',
              slots: {
                default: 'form_channel',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '350px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          appId: '',
          network: '',
          city: '',
          threshold: '',
        },
      },
      selectFormData: {
        appCode: 10090,
        channel: '',
        dt: '',
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
    }
  },
  methods: {
    loadData() {
      this.$refs['page-table'].reloadQuery()
    },
  },
}
</script>
