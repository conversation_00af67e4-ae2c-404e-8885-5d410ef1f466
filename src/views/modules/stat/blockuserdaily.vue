<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          v-if="isAuth('stat:blockuserdaily:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('stat:blockuserdaily:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
      ></el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用id"
      ></el-table-column>
      <el-table-column
        prop="groupId"
        header-align="center"
        align="center"
        label="主体id:0:所有主体统计，1：沐春，2：沐林"
      ></el-table-column>
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      ></el-table-column>
      <el-table-column
        prop="beiJing"
        header-align="center"
        align="center"
        label="北京"
      ></el-table-column>
      <el-table-column
        prop="shangHai"
        header-align="center"
        align="center"
        label="上海"
      ></el-table-column>
      <el-table-column
        prop="wuHan"
        header-align="center"
        align="center"
        label="武汉"
      ></el-table-column>
      <el-table-column
        prop="chengDu"
        header-align="center"
        align="center"
        label="成都"
      ></el-table-column>
      <el-table-column
        prop="shenZhen"
        header-align="center"
        align="center"
        label="深圳"
      ></el-table-column>
      <el-table-column
        prop="hangZhou"
        header-align="center"
        align="center"
        label="杭州"
      ></el-table-column>
      <el-table-column
        prop="guangZhou"
        header-align="center"
        align="center"
        label="广州"
      ></el-table-column>
      <el-table-column
        prop="tianJin"
        header-align="center"
        align="center"
        label="天津"
      ></el-table-column>
      <el-table-column
        prop="fuZhou"
        header-align="center"
        align="center"
        label="福州"
      ></el-table-column>
      <el-table-column
        prop="xiaMen"
        header-align="center"
        align="center"
        label="厦门"
      ></el-table-column>
      <el-table-column
        prop="chongQing"
        header-align="center"
        align="center"
        label="重庆"
      ></el-table-column>
      <el-table-column
        prop="dongGuan"
        header-align="center"
        align="center"
        label="东莞"
      ></el-table-column>
      <el-table-column
        prop="nanJing"
        header-align="center"
        align="center"
        label="南京"
      ></el-table-column>
      <el-table-column
        prop="zhuHai"
        header-align="center"
        align="center"
        label="珠海"
      ></el-table-column>
      <el-table-column
        prop="suZhou"
        header-align="center"
        align="center"
        label="苏州"
      ></el-table-column>
      <el-table-column
        prop="cheatingType1"
        header-align="center"
        align="center"
        label="作弊类型：1001; //已root"
      ></el-table-column>
      <el-table-column
        prop="cheatingType2"
        header-align="center"
        align="center"
        label="作弊类型：1002; //已开启模拟定位"
      ></el-table-column>
      <el-table-column
        prop="cheatingType3"
        header-align="center"
        align="center"
        label="作弊类型：1003; //使用模拟器"
      ></el-table-column>
      <el-table-column
        prop="cheatingType4"
        header-align="center"
        align="center"
        label="作弊类型：1004; //无相机"
      ></el-table-column>
      <el-table-column
        prop="cheatingType5"
        header-align="center"
        align="center"
        label="作弊类型：1005; //未插SIM卡"
      ></el-table-column>
      <el-table-column
        prop="cheatingType6"
        header-align="center"
        align="center"
        label="作弊类型：1006; //已开启VPN"
      ></el-table-column>
      <el-table-column
        prop="cheatingType7"
        header-align="center"
        align="center"
        label="作弊类型：1007; //已连接代理"
      ></el-table-column>
      <el-table-column
        prop="cheatingType8"
        header-align="center"
        align="center"
        label="作弊类型：1008; //正在使用XPOSED"
      ></el-table-column>
      <el-table-column
        prop="cheatingType9"
        header-align="center"
        align="center"
        label="作弊类型：1009; //正在使用双开应用"
      ></el-table-column>
      <el-table-column
        prop="cheatingType10"
        header-align="center"
        align="center"
        label="作弊类型：1010; //已安装抓包证书"
      ></el-table-column>
      <el-table-column
        prop="cheatingType13"
        header-align="center"
        align="center"
        label="作弊类型：1013; //未安装微信"
      ></el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      ></el-table-column>
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      ></el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './blockuserdaily-add-or-update'
export default {
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
    }
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/blockuserdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/blockuserdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
