<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="选择应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
          :is-store="true"
        />
      </el-form-item>
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--        <el-button v-if="isAuth('stat:statdevicedaily:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <!--        <el-button v-if="isAuth('stat:statdevicedaily:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="appList && appList.length">
            {{ row.appId | getListLabel(appList, 'code', 'name') }}
          </el-tag>
          <br />
          <div style="color: #aaa;">{{ row.appId }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="startCount"
        header-align="center"
        align="center"
        label="启动次数"
      />
      <el-table-column
        prop="aliveCount"
        header-align="center"
        align="center"
        label="活跃数量"
      />
      <el-table-column
        prop="regCount"
        header-align="center"
        align="center"
        label="注册设备"
      />
      <el-table-column
        prop="regMkCount"
        header-align="center"
        align="center"
        label="市场新增"
      >
        <template slot-scope="{ row }">
          {{ row.regMkCount }}
          <template v-if="row.regMkCount / row.regCount">
            -
            <el-tag>
              {{ Math.round((row.regMkCount / row.regCount) * 100, 2) }}%
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="newCount"
        header-align="center"
        align="center"
        label="有效新增"
      >
        <template slot-scope="{ row }">
          {{ row.newCount }}
          <template v-if="row.newCount / row.regCount">
            -
            <el-tag>
              {{ Math.round((row.newCount / row.regCount) * 100, 2) }}%
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="alive1Count"
        header-align="center"
        align="center"
        label="新增次留数"
      >
        <template slot-scope="{ row }">
          {{ row.alive1Count }}
          <template v-if="row.alive1Count / row.newCount">
            -
            <el-tag>
              {{ Math.round((row.alive1Count / row.newCount) * 100, 2) }}%
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="activateCount"
        header-align="center"
        align="center"
        label="激活回传数"
      >
        <template slot-scope="{ row }">
          {{ row.activateCount }}
          <template v-if="row.activateCount / row.newCount">
            -
            <el-tag>
              {{ Math.round((row.activateCount / row.newCount) * 100, 2) }}%
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="secondCount"
        header-align="center"
        align="center"
        label="次留回传数"
      >
        <template slot-scope="{ row }">
          {{ row.secondCount }}
          <template v-if="row.secondCount / row.newCount">
            -
            <el-tag>
              {{ Math.round((row.secondCount / row.newCount) * 100, 2) }}%
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="hijackCount"
        header-align="center"
        align="center"
        label="市场劫持数"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="统计时间"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(row.createdAt).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="updatedAt"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="更新时间">-->
      <!--      </el-table-column>-->
      <!--      <el-table-column-->
      <!--        fixed="right"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="150"-->
      <!--        label="操作">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
      <!--          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './statdevicedaily-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      appList: [],
    }
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
    this.getAppList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      console.log('change', this.$store.state.ad.appId)
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/statdevicedaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
          app_id: this.$store.state.ad.appId || '',
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/statdevicedaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getAppList() {
      this.$store
        .dispatch('api/app/getAppList', {
          page: 1,
          limit: 10000,
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.page.list
            console.log(this.appList)
          }
        })
    },
  },
}
</script>
