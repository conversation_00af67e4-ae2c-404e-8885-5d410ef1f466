<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
    </el-form-item>
    <el-form-item label="日期yyyy-MM-dd" prop="dt">
      <el-input v-model="dataForm.dt" placeholder="日期yyyy-MM-dd"></el-input>
    </el-form-item>
    <el-form-item label="当日访问用户数" prop="userCount">
      <el-input v-model="dataForm.userCount" placeholder="当日访问用户数"></el-input>
    </el-form-item>
    <el-form-item label="当日访问用户次数" prop="visitCount">
      <el-input v-model="dataForm.visitCount" placeholder="当日访问用户次数"></el-input>
    </el-form-item>
    <el-form-item label="人均访问次数" prop="avgVisit">
      <el-input v-model="dataForm.avgVisit" placeholder="人均访问次数"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          dt: '',
          userCount: '',
          visitCount: '',
          avgVisit: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用id不能为空', trigger: 'blur' }
          ],
          dt: [
            { required: true, message: '日期yyyy-MM-dd不能为空', trigger: 'blur' }
          ],
          userCount: [
            { required: true, message: '当日访问用户数不能为空', trigger: 'blur' }
          ],
          visitCount: [
            { required: true, message: '当日访问用户次数不能为空', trigger: 'blur' }
          ],
          avgVisit: [
            { required: true, message: '人均访问次数不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/apppullupstat/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.appPullUpStat.appId
                this.dataForm.dt = data.appPullUpStat.dt
                this.dataForm.userCount = data.appPullUpStat.userCount
                this.dataForm.visitCount = data.appPullUpStat.visitCount
                this.dataForm.avgVisit = data.appPullUpStat.avgVisit
                this.dataForm.createdAt = data.appPullUpStat.createdAt
                this.dataForm.updatedAt = data.appPullUpStat.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/apppullupstat/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'dt': this.dataForm.dt,
                'userCount': this.dataForm.userCount,
                'visitCount': this.dataForm.visitCount,
                'avgVisit': this.dataForm.avgVisit,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
