<template>
  <div class="mod-config">
    <div style="display:flex; justify-content: space-between">
      <base-form
        ref="baseForm"
        :inline="true"
        :model.sync="dataForm"
        :rules="rules"
        @submit="currentChangeHandle(1)"
      >
        <el-form-item label="主体">
          <el-select v-model="dataForm.groupId" @change="changeGroupId">
            <!--<el-option-->
            <!--  v-for="groupId in $store.state.user.groupIdList"-->
            <!--  :value="groupId"-->
            <!--  :label="appGroupWithAll.get(groupId)"-->
            <!--  :key="groupId"-->
            <!--/>-->
            <el-option
              v-for="[groupId, label] in appGroupList"
              :value="groupId"
              :label="label"
              :key="groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择应用">
          <app-select
            ref="appSelect"
            @change="handleAppChange"
            @init-app-id="handleInitAppId"
          />
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            v-model="selectTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyyMMdd"
          />
        </el-form-item>
        <el-form-item label="留存类型">
          <el-select
            v-model="dataForm.queryType"
            @change="currentChangeHandle(1)"
          >
            <el-option label="心跳留存" :value="1" />
            <el-option label="UI留存" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelName">
          <el-select
            v-model="dataForm.channelName"
            placeholder="渠道"
            clearable
          >
            <el-option
              v-for="item in channelNameList"
              :key="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道号" prop="marketCode">
          <el-select
            v-model="dataForm.marketCode"
            placeholder="渠道号"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in marketCodeList"
              :key="index"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投放账户" prop="advertiseId">
          <el-select
            v-model="dataForm.advertiseId"
            placeholder="账号"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in advertiseIdList"
              :key="index"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="国家">
          <country-select v-model="dataForm.city" />
        </el-form-item>
        <el-form-item label="留存天数">
          <el-select v-model="keepNumber" placeholder="切换多少天">
            <el-option
              v-for="item in keepList"
              :value="item"
              :key="item"
              :label="`${item === 2 ? '次' : item}留`"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="isPercentage = !isPercentage">
            切换留存/留存率
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-download"
            @click="$downloadTableToExcel(false)"
          >
            下载Excel
          </el-button>
        </el-form-item>
        <!--<el-form-item>-->
        <!--  <el-input-->
        <!--    v-model="dataForm.key"-->
        <!--    placeholder="参数名"-->
        <!--    clearable-->
        <!--  ></el-input>-->
        <!--</el-form-item>-->
        <el-form-item>
          <!--<el-button @click="getDataList" icon="el-icon-search" type="primary">-->
          <!--  查询-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  v-if="isAuth('stat:activekeepdaily:save')"-->
          <!--  type="primary"-->
          <!--  @click="addOrUpdateHandle()"-->
          <!--&gt;-->
          <!--  新增-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  v-if="isAuth('stat:activekeepdaily:delete')"-->
          <!--  type="danger"-->
          <!--  @click="deleteHandle()"-->
          <!--  :disabled="dataListSelections.length <= 0"-->
          <!--&gt;-->
          <!--  批量删除-->
          <!--</el-button>-->
        </el-form-item>
      </base-form>
    </div>
    <!--<div>-->
    <!--  <el-form :inline="true">-->
    <!--    <el-form-item label="留存天数">-->
    <!--      <el-select v-model="keepNumber" placeholder="切换多少天">-->
    <!--        <el-option-->
    <!--          v-for="item in keepList"-->
    <!--          :value="item"-->
    <!--          :key="item"-->
    <!--          :label="`${item === 2 ? '次' : item}留`"-->
    <!--        />-->
    <!--      </el-select>-->
    <!--    </el-form-item>-->
    <!--    <el-form-item>-->
    <!--      <el-button type="primary" @click="isPercentage = !isPercentage">-->
    <!--        切换留存/留存率-->
    <!--      </el-button>-->
    <!--    </el-form-item>-->
    <!--    <el-form-item>-->
    <!--      <el-button-->
    <!--        type="primary"-->
    <!--        icon="el-icon-download"-->
    <!--        @click="$downloadTableToExcel(false)"-->
    <!--      >-->
    <!--        下载Excel-->
    <!--      </el-button>-->
    <!--    </el-form-item>-->
    <!--  </el-form>-->
    <!--</div>-->
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="id"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="id"-->
      <!--&gt;</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="appCode"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="应用id"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
        width="85"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="newAddNums"
        header-align="center"
        align="center"
        label="新增"
        width="65"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.newAddNums ? row.newAddNums.toLocaleString() : '-' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="showActiveNums"
        prop="activeNums"
        header-align="center"
        align="center"
        label="活跃"
        width="85"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.activeNums ? row.activeNums.toLocaleString() : '-' }}
          </span>
        </template>
      </el-table-column>
      <!--<el-table-column-->
      <!--  :label="isPercentage ? '留存率' : '留存数'"-->
      <!--  align="center"-->
      <!--&gt;-->
      <!--  -->
      <!--</el-table-column>-->
      <template v-for="item in keepList">
        <el-table-column
          v-if="keepNumber >= item"
          :key="item"
          :prop="`keep${item}`"
          header-align="center"
          align="center"
          :label="`${item === 2 ? '次' : item}留`"
          min-width="63"
        >
          <template slot-scope="{ row }" v-if="keepP(row, item) > 0">
            <div>
              {{ isPercentage ? keepP(row, item) + '%' : row['keep' + item] }}
            </div>
          </template>
        </el-table-column>
      </template>
      <!--<el-table-column-->
      <!--  prop="createdAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="创建时间"-->
      <!--  min-width="140"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="updateAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="更新时间"-->
      <!--  min-width="140"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.id)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.id)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './activekeepdaily-add-or-update'
import { appGroupWithAll, channelNameList } from '@/map/common'
import BaseForm from '@/components/base-form'
import { mixinElTableAdapterHeight } from '@/mixins'
import { uniq } from 'lodash'
import CountrySelect from '@/components/country-select/index.vue'

const keepList = Array.from(new Array(31).keys())
  .slice(2)
  .concat([60, 90, 120, 150, 180])

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    const mustAppId = (_, value, callback) => {
      if (value && !this.$store.state.ad.appId) {
        return callback(new Error('必须先选app'))
      }
      callback()
    }

    return {
      dataForm: {
        key: '',
        groupId: this.$store.state.user.groupIdList[0],
        queryType: 1,
        marketCode: '',
        channelName: '',
        advertiseId: '',
        city: '', // 国家
      },
      rules: {
        marketCode: [{ validator: mustAppId }],
        channelName: [{ validator: mustAppId }],
        advertiseId: [{ validator: mustAppId }],
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      keepList: keepList,
      keepNumber: 20,
      isPercentage: true,
      selectTime: '',
      appGroupWithAll,
      advertiseIdList: [],
      marketCodeList: [],
      channelNameList,
    }
  },
  computed: {
    showActiveNums() {
      return this.dataList.some(it => it.activeNums)
    },
    appGroupList() {
      const appGroupList = new Map([[0, '全部']])
      appGroupWithAll.forEach((value, key) => {
        const groupIdList = this.$store.state.user.groupIdList || []
        if (groupIdList.includes(key)) {
          appGroupList.set(key, value)
        }
      })
      return appGroupList
    },
  },
  components: {
    CountrySelect,
    BaseForm,
    AddOrUpdate,
    AppSelect,
  },
  activated() {},
  methods: {
    // 获取数据列表
    getDataList() {
      this.$nextTick(async () => {
        try {
          await this.$refs.baseForm.getElForm().validate()
        } catch (e) {
          // console.log(e)
          return
        }

        const appList = this.$store.state.ad.appList
        const res = appList.find(it => it.id === this.$store.state.ad.appId)

        this.dataListLoading = true

        // 兼容，如果 marketCode | channelName | advertiseId 存在，queryType需要修改值
        // market_code && queryType === 1  -> queryType = 11
        // market_code && queryType === 2  -> queryType = 22
        let queryType = this.dataForm.queryType
        // 选了国家 33
        if (this.dataForm.city) {
          queryType = 33
        } else if (
          this.dataForm.marketCode ||
          this.dataForm.channelName ||
          this.dataForm.advertiseId
        ) {
          queryType = this.dataForm.queryType === 1 ? 11 : 22
        }

        this.$http({
          url: this.$http.adornUrl('/stat/activekeepdaily/list'),
          method: 'get',
          params: this.$http.adornParams({
            page: this.pageIndex,
            limit: this.pageSize,
            // key: this.dataForm.key,
            // groupId: this.dataForm.groupId,
            appCode: res.code,
            startTime: this.selectTime ? this.selectTime[0] ?? '' : '',
            endTime: this.selectTime ? this.selectTime[1] ?? '' : '',
            ...this.dataForm,
            queryType,
          }),
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.dataList = data.page.list
              this.totalPage = data.page.totalCount
            } else {
              this.dataList = []
              this.totalPage = 0
              this.$message.error(data.msg || '服务器错误')
            }
          })
          .finally(() => {
            this.dataListLoading = false
          })
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    keepP(row, index) {
      return parseInt(String((row['keep' + index] / row.newAddNums) * 100))
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/activekeepdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    changeGroupId() {
      this.pageIndex = 1
      const appList = this.$store.state.ad.appList
      const listAfterChange = this.dataForm.groupId
        ? appList.filter(it => it.groupId === this.dataForm.groupId)
        : appList
      this.$refs.appSelect.changeAppList(listAfterChange)
    },
    handleAppChange() {
      this.dataForm.marketCode = ''
      this.dataForm.advertiseId = ''
      this.getAdvertiseInfo()
      console.log('handleAppChange', this.$store.state.ad.appId)
      this.currentChangeHandle(1)
    },
    handleInitAppId() {
      console.log('handleInitAppId', this.$store.state.ad.appId)
      setTimeout(() => {
        this.getAdvertiseInfo()
      })
      this.changeGroupId()
    },
    getAdvertiseInfo() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)
      if (!res.code) return

      this.$http({
        url: this.$http.adornUrl('/stat/activekeepdaily/advertise_info'),
        method: 'get',
        params: this.$http.adornParams({
          appCode: res.code,
        }),
      })
        .then(({ data }) => {
          if (
            data &&
            data.code === 0 &&
            data.data &&
            Array.isArray(data.data)
          ) {
            this.advertiseIdList = uniq(data.data.map(it => it.advertiseId))
            this.marketCodeList = uniq(data.data.map(it => it.marketCode))
          } else {
            this.advertiseIdList = []
            this.marketCodeList = []
          }
        })
        .catch(() => {
          this.advertiseIdList = []
          this.marketCodeList = []
        })
    },
  },
}
</script>
