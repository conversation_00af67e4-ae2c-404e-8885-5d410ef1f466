<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="选择应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
          :is-store="true"
        />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="selectTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyyMMdd"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:baidureportdaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('stat:baidureportdaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
      <el-button
        type="primary"
        icon="el-icon-download"
        @click="$downloadTableToExcel()"
      >
        下载Excel
      </el-button>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
        width="50"
      />
      <!--<el-table-column-->
      <!--  prop="appCode"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="后台应用app_code"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="appTypeName"
        header-align="center"
        align="center"
        label="应用类型"
      />
      <el-table-column
        prop="adTypeName"
        header-align="center"
        align="center"
        label="代码位类型"
        width="100"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="appsid"
        width="100"
      />
      <el-table-column
        prop="appName"
        header-align="center"
        align="center"
        label="应用"
        width="100"
      />
      <el-table-column
        prop="systemName"
        header-align="center"
        align="center"
        label="系统"
      />
      <el-table-column
        prop="adPositionId"
        header-align="center"
        align="center"
        label="代码位id"
      />
      <el-table-column
        prop="adPositionName"
        header-align="center"
        align="center"
        label="代码位名称"
        width="220"
      />
      <el-table-column
        prop="view"
        header-align="center"
        align="center"
        label="代码位展现量"
        width="120"
      />
      <el-table-column
        prop="income"
        header-align="center"
        align="center"
        label="预计收入"
      />
      <el-table-column
        prop="click"
        header-align="center"
        align="center"
        label="点击量"
      />
      <el-table-column
        prop="ecpm"
        header-align="center"
        align="center"
        label="eCPM"
      />
      <el-table-column
        prop="ctr"
        header-align="center"
        align="center"
        label="点击率"
      />
      <el-table-column
        prop="cpc"
        header-align="center"
        align="center"
        label="CPC"
      />
      <el-table-column
        prop="fillratio"
        header-align="center"
        align="center"
        label="填充率"
      />
      <el-table-column
        prop="request"
        header-align="center"
        align="center"
        label="请求"
      />
      <el-table-column
        prop="effectRequest"
        header-align="center"
        align="center"
        label="有效请求"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="120"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(row.createdAt).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
        width="120"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="time"
        header-align="center"
        align="center"
        label="日期"
        width="120"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(row.time).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="80"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            查看
          </el-button>
          <!--<el-button-->
          <!--  type="text"-->
          <!--  size="small"-->
          <!--  @click="deleteHandle(scope.row.id)"-->
          <!--&gt;-->
          <!--  删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './baidureportdaily-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      selectTime: '',
    }
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList: function() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)

      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/baidureportdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
          appCode: res.code || '',
          startTime: this.selectTime ? this.selectTime[0] ?? '' : '',
          endTime: this.selectTime ? this.selectTime[1] ?? '' : '',
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list.map(it => {
            let item = {}
            for (const itKey in it) {
              if (it[itKey] && it[itKey].toString().toLowerCase() !== 'null') {
                item[itKey] = it[itKey]
              } else {
                item[itKey] = '--'
              }
            }

            return item
          })
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/baidureportdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
