<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="640px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="日期" prop="dt">
            <el-input v-model="dataForm.dt" placeholder="日期" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="新增人数" prop="newNum">
            <el-input v-model="dataForm.newNum" placeholder="新增人数" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本" prop="cost">
            <el-input v-model="dataForm.cost" placeholder="成本" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付费人数" prop="payNum">
            <el-input v-model="dataForm.payNum" placeholder="付费人数" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付费率" prop="payRate">
            <el-input v-model="dataForm.payRate" placeholder="付费率" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付费金额" prop="payAmount">
            <el-input v-model="dataForm.payAmount" placeholder="付费金额" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="roi" prop="roi">
            <el-input v-model="dataForm.roi" placeholder="roi" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="渠道" prop="channel">
            <el-input v-model="dataForm.channel" placeholder="1:tt,2:kwai" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        dt: '',
        newNum: '',
        cost: '',
        payNum: '',
        payRate: '',
        payAmount: '',
        roi: '',
        channel: '',
      },
      dataRule: {
        dt: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        newNum: [
          { required: true, message: '新增人数不能为空', trigger: 'blur' },
        ],
        cost: [{ required: true, message: '成本不能为空', trigger: 'blur' }],
        payNum: [
          { required: true, message: '付费人数不能为空', trigger: 'blur' },
        ],
        payRate: [
          { required: true, message: '付费率不能为空', trigger: 'blur' },
        ],
        payAmount: [
          { required: true, message: '付费金额不能为空', trigger: 'blur' },
        ],
        roi: [{ required: true, message: 'roi不能为空', trigger: 'blur' }],
        channel: [
          { required: true, message: '1:tt,2:kwai不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/playletdatasummary/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.dt = data.playletDataSummary.dt
              this.dataForm.newNum = data.playletDataSummary.newNum
              this.dataForm.cost = data.playletDataSummary.cost
              this.dataForm.payNum = data.playletDataSummary.payNum
              this.dataForm.payRate = data.playletDataSummary.payRate
              this.dataForm.payAmount = data.playletDataSummary.payAmount
              this.dataForm.roi = data.playletDataSummary.roi
              this.dataForm.channel = data.playletDataSummary.channel
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/playletdatasummary/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              dt: this.dataForm.dt,
              newNum: this.dataForm.newNum,
              cost: this.dataForm.cost,
              payNum: this.dataForm.payNum,
              payRate: this.dataForm.payRate,
              payAmount: this.dataForm.payAmount,
              roi: this.dataForm.roi,
              channel: this.dataForm.channel,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
