<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="支付状态">
        <arr-select
          :list="statusList"
          label-key="label"
          value-key="value"
          v-model="dataForm.status"
        />
      </el-form-item>
      <el-form-item label="方案id">
        <arr-select
          :list="appList"
          label-key="label"
          value-key="value"
          v-model="dataForm.appId"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          v-if="isAuth('stat:rechargerecord:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('stat:rechargerecord:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
      ></el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="方案id"
      ></el-table-column>
      <el-table-column
        prop="userId"
        header-align="center"
        align="center"
        label="用户id"
      ></el-table-column>
      <el-table-column
        prop="rechargePrice"
        header-align="center"
        align="center"
        label="充值金额"
      ></el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="支付状态"
      >
        <template slot-scope="scope">
          <span>{{ getStatusText(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="payType"
        header-align="center"
        align="center"
        label="支付类型"
      >
        <template slot-scope="scope">
          <span>
            {{ getPayTypeText(scope.row.payType) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="configId"
        header-align="center"
        align="center"
        label="充值表id"
      ></el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      ></el-table-column>
      <el-table-column
        prop="sessionId"
        header-align="center"
        align="center"
        label="sessio标识"
      ></el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './rechargerecord-add-or-update'
// import { mixinElTableAdapterHeight } from '@/mixins'
const payTypeList = [
  { label: '金币', value: 0 },
  { label: '周卡', value: 1 },
  { label: '月卡', value: 2 },
  { label: '年卡', value: 3 },
]
const statusList = [
  { label: '支付成功', value: 1 },
  { label: '待支付', value: 2 },
  { label: '支付失败', value: 3 },
  { label: '续费成功', value: 10 },
  { label: '申请退款', value: 4 },
  { label: '退款成功', value: 5 },
  { label: '退款失败', value: 6 },
]
const appList = [
  {
    label: '113',
    value: 113,
  },
  {
    label: '116',
    value: 116,
  },
  {
    label: '117',
    value: 117,
  },
  {
    label: '118',
    value: 118,
  },
]
export default {
  // mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      statusList,
      appList,
      dataForm: { status: '', appId: '' },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
    }
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/rechargerecord/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
          status: this.dataForm.status,
          appId: this.dataForm.appId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/rechargerecord/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getStatusText(value) {
      const res = statusList.find(it => it.value == value)
      return res ? res.label : '-'
    },
    getPayTypeText(value) {
      const res = payTypeList.find(it => it.value == value)
      return res ? res.label : '-'
    },
  },
}
</script>
