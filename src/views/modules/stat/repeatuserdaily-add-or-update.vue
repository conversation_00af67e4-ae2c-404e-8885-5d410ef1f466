<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
    </el-form-item>
    <el-form-item label="主体id:0:所有主体统计，1：沐春，2：沐林" prop="groupId">
      <el-input v-model="dataForm.groupId" placeholder="主体id:0:所有主体统计，1：沐春，2：沐林"></el-input>
    </el-form-item>
    <el-form-item label="新增人数" prop="newNum">
      <el-input v-model="dataForm.newNum" placeholder="新增人数"></el-input>
    </el-form-item>
    <el-form-item label="重复人数" prop="repeatNum">
      <el-input v-model="dataForm.repeatNum" placeholder="重复人数"></el-input>
    </el-form-item>
    <el-form-item label="重复率" prop="repeatRate">
      <el-input v-model="dataForm.repeatRate" placeholder="重复率"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          groupId: '',
          newNum: '',
          repeatNum: '',
          repeatRate: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用id不能为空', trigger: 'blur' }
          ],
          groupId: [
            { required: true, message: '主体id:0:所有主体统计，1：沐春，2：沐林不能为空', trigger: 'blur' }
          ],
          newNum: [
            { required: true, message: '新增人数不能为空', trigger: 'blur' }
          ],
          repeatNum: [
            { required: true, message: '重复人数不能为空', trigger: 'blur' }
          ],
          repeatRate: [
            { required: true, message: '重复率不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/repeatuserdaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.repeatUserDaily.appId
                this.dataForm.groupId = data.repeatUserDaily.groupId
                this.dataForm.newNum = data.repeatUserDaily.newNum
                this.dataForm.repeatNum = data.repeatUserDaily.repeatNum
                this.dataForm.repeatRate = data.repeatUserDaily.repeatRate
                this.dataForm.createdAt = data.repeatUserDaily.createdAt
                this.dataForm.updatedAt = data.repeatUserDaily.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/repeatuserdaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'groupId': this.dataForm.groupId,
                'newNum': this.dataForm.newNum,
                'repeatNum': this.dataForm.repeatNum,
                'repeatRate': this.dataForm.repeatRate,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
