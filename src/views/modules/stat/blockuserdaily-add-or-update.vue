<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
    </el-form-item>
    <el-form-item label="主体id:0:所有主体统计，1：沐春，2：沐林" prop="groupId">
      <el-input v-model="dataForm.groupId" placeholder="主体id:0:所有主体统计，1：沐春，2：沐林"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="北京" prop="beiJing">
      <el-input v-model="dataForm.beiJing" placeholder="北京"></el-input>
    </el-form-item>
    <el-form-item label="上海" prop="shangHai">
      <el-input v-model="dataForm.shangHai" placeholder="上海"></el-input>
    </el-form-item>
    <el-form-item label="武汉" prop="wuHan">
      <el-input v-model="dataForm.wuHan" placeholder="武汉"></el-input>
    </el-form-item>
    <el-form-item label="成都" prop="chengDu">
      <el-input v-model="dataForm.chengDu" placeholder="成都"></el-input>
    </el-form-item>
    <el-form-item label="深圳" prop="shenZhen">
      <el-input v-model="dataForm.shenZhen" placeholder="深圳"></el-input>
    </el-form-item>
    <el-form-item label="杭州" prop="hangZhou">
      <el-input v-model="dataForm.hangZhou" placeholder="杭州"></el-input>
    </el-form-item>
    <el-form-item label="广州" prop="guangZhou">
      <el-input v-model="dataForm.guangZhou" placeholder="广州"></el-input>
    </el-form-item>
    <el-form-item label="天津" prop="tianJin">
      <el-input v-model="dataForm.tianJin" placeholder="天津"></el-input>
    </el-form-item>
    <el-form-item label="福州" prop="fuZhou">
      <el-input v-model="dataForm.fuZhou" placeholder="福州"></el-input>
    </el-form-item>
    <el-form-item label="厦门" prop="xiaMen">
      <el-input v-model="dataForm.xiaMen" placeholder="厦门"></el-input>
    </el-form-item>
    <el-form-item label="重庆" prop="chongQing">
      <el-input v-model="dataForm.chongQing" placeholder="重庆"></el-input>
    </el-form-item>
    <el-form-item label="东莞" prop="dongGuan">
      <el-input v-model="dataForm.dongGuan" placeholder="东莞"></el-input>
    </el-form-item>
    <el-form-item label="南京" prop="nanJing">
      <el-input v-model="dataForm.nanJing" placeholder="南京"></el-input>
    </el-form-item>
    <el-form-item label="珠海" prop="zhuHai">
      <el-input v-model="dataForm.zhuHai" placeholder="珠海"></el-input>
    </el-form-item>
    <el-form-item label="苏州" prop="suZhou">
      <el-input v-model="dataForm.suZhou" placeholder="苏州"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1001; //已root" prop="cheatingType1">
      <el-input v-model="dataForm.cheatingType1" placeholder="作弊类型：1001; //已root"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1002; //已开启模拟定位" prop="cheatingType2">
      <el-input v-model="dataForm.cheatingType2" placeholder="作弊类型：1002; //已开启模拟定位"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1003; //使用模拟器" prop="cheatingType3">
      <el-input v-model="dataForm.cheatingType3" placeholder="作弊类型：1003; //使用模拟器"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1004; //无相机" prop="cheatingType4">
      <el-input v-model="dataForm.cheatingType4" placeholder="作弊类型：1004; //无相机"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1005; //未插SIM卡" prop="cheatingType5">
      <el-input v-model="dataForm.cheatingType5" placeholder="作弊类型：1005; //未插SIM卡"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1006; //已开启VPN" prop="cheatingType6">
      <el-input v-model="dataForm.cheatingType6" placeholder="作弊类型：1006; //已开启VPN"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1007; //已连接代理" prop="cheatingType7">
      <el-input v-model="dataForm.cheatingType7" placeholder="作弊类型：1007; //已连接代理"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1008; //正在使用XPOSED" prop="cheatingType8">
      <el-input v-model="dataForm.cheatingType8" placeholder="作弊类型：1008; //正在使用XPOSED"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1009; //正在使用双开应用" prop="cheatingType9">
      <el-input v-model="dataForm.cheatingType9" placeholder="作弊类型：1009; //正在使用双开应用"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1010; //已安装抓包证书" prop="cheatingType10">
      <el-input v-model="dataForm.cheatingType10" placeholder="作弊类型：1010; //已安装抓包证书"></el-input>
    </el-form-item>
    <el-form-item label="作弊类型：1013; //未安装微信" prop="cheatingType13">
      <el-input v-model="dataForm.cheatingType13" placeholder="作弊类型：1013; //未安装微信"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          groupId: '',
          day: '',
          beiJing: '',
          shangHai: '',
          wuHan: '',
          chengDu: '',
          shenZhen: '',
          hangZhou: '',
          guangZhou: '',
          tianJin: '',
          fuZhou: '',
          xiaMen: '',
          chongQing: '',
          dongGuan: '',
          nanJing: '',
          zhuHai: '',
          suZhou: '',
          cheatingType1: '',
          cheatingType2: '',
          cheatingType3: '',
          cheatingType4: '',
          cheatingType5: '',
          cheatingType6: '',
          cheatingType7: '',
          cheatingType8: '',
          cheatingType9: '',
          cheatingType10: '',
          cheatingType13: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用id不能为空', trigger: 'blur' }
          ],
          groupId: [
            { required: true, message: '主体id:0:所有主体统计，1：沐春，2：沐林不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          beiJing: [
            { required: true, message: '北京不能为空', trigger: 'blur' }
          ],
          shangHai: [
            { required: true, message: '上海不能为空', trigger: 'blur' }
          ],
          wuHan: [
            { required: true, message: '武汉不能为空', trigger: 'blur' }
          ],
          chengDu: [
            { required: true, message: '成都不能为空', trigger: 'blur' }
          ],
          shenZhen: [
            { required: true, message: '深圳不能为空', trigger: 'blur' }
          ],
          hangZhou: [
            { required: true, message: '杭州不能为空', trigger: 'blur' }
          ],
          guangZhou: [
            { required: true, message: '广州不能为空', trigger: 'blur' }
          ],
          tianJin: [
            { required: true, message: '天津不能为空', trigger: 'blur' }
          ],
          fuZhou: [
            { required: true, message: '福州不能为空', trigger: 'blur' }
          ],
          xiaMen: [
            { required: true, message: '厦门不能为空', trigger: 'blur' }
          ],
          chongQing: [
            { required: true, message: '重庆不能为空', trigger: 'blur' }
          ],
          dongGuan: [
            { required: true, message: '东莞不能为空', trigger: 'blur' }
          ],
          nanJing: [
            { required: true, message: '南京不能为空', trigger: 'blur' }
          ],
          zhuHai: [
            { required: true, message: '珠海不能为空', trigger: 'blur' }
          ],
          suZhou: [
            { required: true, message: '苏州不能为空', trigger: 'blur' }
          ],
          cheatingType1: [
            { required: true, message: '作弊类型：1001; //已root不能为空', trigger: 'blur' }
          ],
          cheatingType2: [
            { required: true, message: '作弊类型：1002; //已开启模拟定位不能为空', trigger: 'blur' }
          ],
          cheatingType3: [
            { required: true, message: '作弊类型：1003; //使用模拟器不能为空', trigger: 'blur' }
          ],
          cheatingType4: [
            { required: true, message: '作弊类型：1004; //无相机不能为空', trigger: 'blur' }
          ],
          cheatingType5: [
            { required: true, message: '作弊类型：1005; //未插SIM卡不能为空', trigger: 'blur' }
          ],
          cheatingType6: [
            { required: true, message: '作弊类型：1006; //已开启VPN不能为空', trigger: 'blur' }
          ],
          cheatingType7: [
            { required: true, message: '作弊类型：1007; //已连接代理不能为空', trigger: 'blur' }
          ],
          cheatingType8: [
            { required: true, message: '作弊类型：1008; //正在使用XPOSED不能为空', trigger: 'blur' }
          ],
          cheatingType9: [
            { required: true, message: '作弊类型：1009; //正在使用双开应用不能为空', trigger: 'blur' }
          ],
          cheatingType10: [
            { required: true, message: '作弊类型：1010; //已安装抓包证书不能为空', trigger: 'blur' }
          ],
          cheatingType13: [
            { required: true, message: '作弊类型：1013; //未安装微信不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/blockuserdaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.blockUserDaily.appId
                this.dataForm.groupId = data.blockUserDaily.groupId
                this.dataForm.day = data.blockUserDaily.day
                this.dataForm.beiJing = data.blockUserDaily.beiJing
                this.dataForm.shangHai = data.blockUserDaily.shangHai
                this.dataForm.wuHan = data.blockUserDaily.wuHan
                this.dataForm.chengDu = data.blockUserDaily.chengDu
                this.dataForm.shenZhen = data.blockUserDaily.shenZhen
                this.dataForm.hangZhou = data.blockUserDaily.hangZhou
                this.dataForm.guangZhou = data.blockUserDaily.guangZhou
                this.dataForm.tianJin = data.blockUserDaily.tianJin
                this.dataForm.fuZhou = data.blockUserDaily.fuZhou
                this.dataForm.xiaMen = data.blockUserDaily.xiaMen
                this.dataForm.chongQing = data.blockUserDaily.chongQing
                this.dataForm.dongGuan = data.blockUserDaily.dongGuan
                this.dataForm.nanJing = data.blockUserDaily.nanJing
                this.dataForm.zhuHai = data.blockUserDaily.zhuHai
                this.dataForm.suZhou = data.blockUserDaily.suZhou
                this.dataForm.cheatingType1 = data.blockUserDaily.cheatingType1
                this.dataForm.cheatingType2 = data.blockUserDaily.cheatingType2
                this.dataForm.cheatingType3 = data.blockUserDaily.cheatingType3
                this.dataForm.cheatingType4 = data.blockUserDaily.cheatingType4
                this.dataForm.cheatingType5 = data.blockUserDaily.cheatingType5
                this.dataForm.cheatingType6 = data.blockUserDaily.cheatingType6
                this.dataForm.cheatingType7 = data.blockUserDaily.cheatingType7
                this.dataForm.cheatingType8 = data.blockUserDaily.cheatingType8
                this.dataForm.cheatingType9 = data.blockUserDaily.cheatingType9
                this.dataForm.cheatingType10 = data.blockUserDaily.cheatingType10
                this.dataForm.cheatingType13 = data.blockUserDaily.cheatingType13
                this.dataForm.createdAt = data.blockUserDaily.createdAt
                this.dataForm.updatedAt = data.blockUserDaily.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/blockuserdaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'groupId': this.dataForm.groupId,
                'day': this.dataForm.day,
                'beiJing': this.dataForm.beiJing,
                'shangHai': this.dataForm.shangHai,
                'wuHan': this.dataForm.wuHan,
                'chengDu': this.dataForm.chengDu,
                'shenZhen': this.dataForm.shenZhen,
                'hangZhou': this.dataForm.hangZhou,
                'guangZhou': this.dataForm.guangZhou,
                'tianJin': this.dataForm.tianJin,
                'fuZhou': this.dataForm.fuZhou,
                'xiaMen': this.dataForm.xiaMen,
                'chongQing': this.dataForm.chongQing,
                'dongGuan': this.dataForm.dongGuan,
                'nanJing': this.dataForm.nanJing,
                'zhuHai': this.dataForm.zhuHai,
                'suZhou': this.dataForm.suZhou,
                'cheatingType1': this.dataForm.cheatingType1,
                'cheatingType2': this.dataForm.cheatingType2,
                'cheatingType3': this.dataForm.cheatingType3,
                'cheatingType4': this.dataForm.cheatingType4,
                'cheatingType5': this.dataForm.cheatingType5,
                'cheatingType6': this.dataForm.cheatingType6,
                'cheatingType7': this.dataForm.cheatingType7,
                'cheatingType8': this.dataForm.cheatingType8,
                'cheatingType9': this.dataForm.cheatingType9,
                'cheatingType10': this.dataForm.cheatingType10,
                'cheatingType13': this.dataForm.cheatingType13,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
