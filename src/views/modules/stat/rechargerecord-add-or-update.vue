<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="充值金额" prop="rechargePrice">
      <el-input v-model="dataForm.rechargePrice" placeholder="充值金额"></el-input>
    </el-form-item>
    <el-form-item label="1：支付成功，2：待支付，3：支付失败" prop="status">
      <el-input v-model="dataForm.status" placeholder="1：支付成功，2：待支付，3：支付失败"></el-input>
    </el-form-item>
    <el-form-item label="充值表id" prop="configId">
      <el-input v-model="dataForm.configId" placeholder="充值表id"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="sessio标识" prop="sessionId">
      <el-input v-model="dataForm.sessionId" placeholder="sessio标识"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          userId: '',
          rechargePrice: '',
          status: '',
          configId: '',
          createdAt: '',
          sessionId: ''
        },
        dataRule: {
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          rechargePrice: [
            { required: true, message: '充值金额不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '1：支付成功，2：待支付，3：支付失败不能为空', trigger: 'blur' }
          ],
          configId: [
            { required: true, message: '充值表id不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          sessionId: [
            { required: true, message: 'sessio标识不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/rechargerecord/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.userId = data.rechargeRecord.userId
                this.dataForm.rechargePrice = data.rechargeRecord.rechargePrice
                this.dataForm.status = data.rechargeRecord.status
                this.dataForm.configId = data.rechargeRecord.configId
                this.dataForm.createdAt = data.rechargeRecord.createdAt
                this.dataForm.sessionId = data.rechargeRecord.sessionId
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/rechargerecord/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'userId': this.dataForm.userId,
                'rechargePrice': this.dataForm.rechargePrice,
                'status': this.dataForm.status,
                'configId': this.dataForm.configId,
                'createdAt': this.dataForm.createdAt,
                'sessionId': this.dataForm.sessionId
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
