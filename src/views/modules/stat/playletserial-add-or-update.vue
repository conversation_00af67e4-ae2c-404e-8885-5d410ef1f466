<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="短剧id" prop="playletId">
        <el-input v-model="dataForm.playletId" placeholder="短剧id"></el-input>
      </el-form-item>
      <el-form-item label="剧集名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="剧集名称"></el-input>
      </el-form-item>
      <el-form-item label="是否需要解锁：1：需要，0：不需要" prop="needLock">
        <el-input
          v-model="dataForm.needLock"
          placeholder="是否需要解锁：1：需要，0：不需要"
        ></el-input>
      </el-form-item>
      <el-form-item label="剧集视频地址" prop="video">
        <el-input
          v-model="dataForm.video"
          placeholder="剧集视频地址"
        ></el-input>
      </el-form-item>
      <el-form-item label="序号" prop="serialNo">
        <el-input v-model="dataForm.serialNo" placeholder="序号"></el-input>
      </el-form-item>
      <el-form-item label="每集封面" prop="cover">
        <el-input v-model="dataForm.cover" placeholder="每集封面"></el-input>
      </el-form-item>
      <el-form-item label="" prop="coins">
        <el-input v-model="dataForm.coins" placeholder=""></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        playletId: '',
        name: '',
        needLock: '',
        video: '',
        serialNo: '',
        cover: '',
        coins: '',
      },
      dataRule: {
        playletId: [
          { required: true, message: '短剧id不能为空', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '剧集名称不能为空', trigger: 'blur' },
        ],
        needLock: [
          {
            required: true,
            message: '是否需要解锁：1：需要，0：不需要不能为空',
            trigger: 'blur',
          },
        ],
        video: [
          { required: true, message: '剧集视频地址不能为空', trigger: 'blur' },
        ],
        serialNo: [
          { required: true, message: '序号不能为空', trigger: 'blur' },
        ],
        cover: [
          // { required: true, message: '每集封面不能为空', trigger: 'blur' }
        ],
        coins: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/playletserial/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.playletId = data.playletSerial.playletId
              this.dataForm.name = data.playletSerial.name
              this.dataForm.needLock = data.playletSerial.needLock
              this.dataForm.video = data.playletSerial.video
              this.dataForm.serialNo = data.playletSerial.serialNo
              this.dataForm.cover = data.playletSerial.cover
              this.dataForm.coins = data.playletSerial.coins
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/playletserial/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              playletId: this.dataForm.playletId,
              name: this.dataForm.name,
              needLock: this.dataForm.needLock,
              video: this.dataForm.video,
              serialNo: this.dataForm.serialNo,
              cover: this.dataForm.cover,
              coins: this.dataForm.coins,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
