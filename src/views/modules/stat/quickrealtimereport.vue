<template>
  <div class="mod-config">
    <canvas ref="canvas" style="display: none"></canvas>
    <SearchBar :options="searchBarOptions" @searchData="searchData">
      <el-button
        type="primary"
        :loading="exportLoading"
        @click="handleExportExcel"
      >
        导出数据
      </el-button>
      <el-button
        type="primary"
        :loading="downImgLoading"
        @click="handleDomToImage"
      >
        截图
      </el-button>
      <el-button
        type="info"
        @click="showColumnControl = !showColumnControl"
        icon="el-icon-setting"
      >
        列设置
      </el-button>
    </SearchBar>
    <!-- 列控制面板 -->
    <el-collapse-transition>
      <div v-show="showColumnControl" class="column-control-panel">
        <el-card>
          <div slot="header" class="clearfix">
            <span>表格列显示设置</span>
            <div style="float: right;">
              <el-dropdown @command="applyPreset" style="margin-right: 10px;">
                <el-button type="text" size="small">
                  快速预设<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="essential">核心数据</el-dropdown-item>
                  <el-dropdown-item command="cost">投放数据</el-dropdown-item>
                  <el-dropdown-item command="conversion">转化数据</el-dropdown-item>
                  <el-dropdown-item command="revenue">变现数据</el-dropdown-item>
                  <el-dropdown-item command="all">全部显示</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button style="padding: 3px 0" type="text" @click="resetColumns">重置</el-button>
            </div>
          </div>
          <div class="column-groups">
            <div class="column-group" v-for="group in columnGroups" :key="group.name">
              <h4>{{ group.label }}</h4>
              <el-checkbox-group v-model="group.visible">
                <el-checkbox
                  v-for="col in group.columns"
                  :key="col.key"
                  :label="col.key"
                  class="column-checkbox"
                >
                  {{ col.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-card>
      </div>
    </el-collapse-transition>
    <data-filter @update-filters="handleFilterUpdate" />

    <div
      class="mobile-card"
      v-if="isMobile"
    >
      <el-card v-for="data in filteredDataList" :key="data.id" class="mobile-card-item">
        <div slot="header" class="clearfix">
          <span>{{ data.accountName }}</span>
          <span class="header-date">{{data.dt}}</span>
        </div>
        <div class="text item"><strong>投放平台：</strong>{{data.channel}}</div>
        <div class="text item"><strong>账户ID：</strong>{{data.accountId}}</div>
        <div class="text item"><strong>余额：</strong>{{data.totalBalance}}</div>
        <div class="text item"><strong>预算：</strong>{{data.budget}}</div>
        <div class="text item"><strong>消耗：</strong>{{data.cost}}</div>
        <div class="text item"><strong>曝光：</strong>{{data.costExposureNum}}</div>
        <div class="text item"><strong>点击：</strong>{{data.adClickNum}}</div>
        <div class="text item"><strong>ECPM：</strong>{{data.costEcpm}}</div>
        <div class="text item"><strong>CPC：</strong>{{data.cpc}}</div>
        <div class="text item"><strong>CTR：</strong>{{toPercentage(data.ctr)}}</div>
        <div class="text item"><strong>CVR：</strong>{{toPercentage2(data.conversionRate)}}</div>
        <div class="text item"><strong>拉起率：</strong>{{toPercentage(data.landpagePercent)}} {{toPercentage(data.landpageNoPercent)}} {{toPercentage(data.dayUpPercent)}}</div>
        <div class="text item"><strong>吊起成本：</strong>去重 {{data.upCpi}} 不去重 {{data.upNoCpi}}</div>
        <div class="text item"><strong>新增用户：</strong>去重 {{data.newNum}} 多包 {{data.allNewNum}}</div>
        <div class="text item"><strong>人均拉起：</strong>{{data.upAvg}}</div>
        <div class="text item"><strong>人均拉回：</strong>{{data.backUpNum}}</div>
        <div class="text item"><strong>回传率：</strong>{{toPercentage2(data.returnRate)}}</div>
        <div class="text item"><strong>预估收益：</strong>{{data.income}}</div>
        <div class="text item"><strong>开屏收入：</strong>{{data.xincome}}</div>
        <div class="text item"><strong>总收入：</strong>{{data.zong}}</div>
        <div class="text item"><strong>曝光总数：</strong>{{data.exposureNum}}</div>
        <div class="text item"><strong>IPV：</strong>{{data.ipu}}</div>
        <div class="text item"><strong>CTR：</strong>{{toPercentage(data.inCtr)}}</div>
        <div class="text item"><strong>归因率：</strong>{{toPercentage(data.attrRate)}}</div>
        <div class="text item"><strong>ECPM：</strong>{{data.ecpm}}</div>
        <div class="text item"><strong>ROI：</strong>{{data.roi}}</div>
      </el-card>
    </div>
    <el-table
      v-else
      ref="myTable"
      :key="tableKey"
      class="adapter-height"
      :data="filteredDataList"
      :height="tableHeight"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      :cell-style="cellStyle"
      :header-cell-style="{ background: '#f8f9fa' }"
      style="width: 100%;"
    >
      <el-table-column
        v-if="false"
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <!-- <el-table-column prop="id" header-align="center" align="center" label="">
      </el-table-column> -->
      <el-table-column
        prop="dt"
        header-align="center"
        align="center"
        label="日期"
        width="85"
        fixed="left"
      ></el-table-column>
      <el-table-column
        prop="accountId"
        header-align="center"
        align="center"
        label="账户ID"
        width="95"
        fixed="left"
      ></el-table-column>
      <!-- 基础信息列 - 不使用分组，避免对齐问题 -->
      <el-table-column
        v-if="isColumnVisible('accountName')"
        prop="accountName"
        header-align="center"
        align="center"
        label="账户名称"
        width="90"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('remark')"
        prop="remark"
        header-align="center"
        align="center"
        label="备注"
        width="80"
      ></el-table-column>
      <el-table-column
        v-if="false"
        prop="brand"
        header-align="center"
        align="center"
        label="厂商"
        width="60"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('channel')"
        prop="channel"
        label="投放平台"
        header-align="center"
        align="center"
        width="85"
      ></el-table-column>

      <el-table-column
        v-if="isColumnVisible('appId')"
        prop="appId"
        header-align="center"
        align="center"
        label="应用ID"
        width="75"
      >
        <template slot-scope="{ row }">
          <span>{{ row.appId | getAppName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('unionType')"
        prop="unionType"
        header-align="center"
        align="center"
        label="投放媒体"
        width="85"
      >
        <template slot-scope="scope">
          <div>
            {{ unionTypeMap[scope.row.unionType] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('agent')"
        prop="agent"
        header-align="center"
        align="center"
        label="代理商"
        width="75"
      >
        <template slot-scope="scope">
          <div>
            {{ agentMap[scope.row.agent] }}
          </div>
        </template>
      </el-table-column>
      <!-- 投放数据列 -->
      <el-table-column
        v-if="isColumnVisible('totalBalance')"
        prop="totalBalance"
        header-align="center"
        align="center"
        label="余额"
        width="70"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('budget')"
        prop="budget"
        header-align="center"
        align="center"
        label="预算"
        width="70"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('cost')"
        prop="cost"
        header-align="center"
        align="center"
        label="消耗"
        width="70"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.cost > 0 ? scope.row.cost : '' }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="chujia"
        header-align="center"
        width="70"
        align="center"
        label="出价"
      ></el-table-column> -->
      <el-table-column
        v-if="isColumnVisible('costExposureNum')"
        prop="costExposureNum"
        header-align="center"
        align="center"
        label="曝光"
        width="75"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('adClickNum')"
        prop="adClickNum"
        header-align="center"
        align="center"
        width="65"
        label="点击"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.adClickNum > 0 ? scope.row.adClickNum : '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('costEcpm')"
        prop="costEcpm"
        header-align="center"
        align="center"
        width="70"
        label="ECPM"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('cpc')"
        prop="cpc"
        header-align="center"
        align="center"
        width="65"
        label="CPC"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.cpc > 0 ? scope.row.cpc : '' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('ctr')"
        prop="ctr"
        header-align="center"
        align="center"
        width="65"
        label="CTR"
      >
        <template slot-scope="scope">
          <div>
            {{ toPercentage(scope.row.ctr) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('cpa')"
        prop="cpa"
        header-align="center"
        align="center"
        label="CPA"
        width="65"
      >
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('conversionRate')"
        prop="conversionRate"
        header-align="center"
        width="65"
        align="center"
        label="CVR"
      >
        <template slot-scope="scope">
          <div>
            {{ toPercentage2(scope.row.conversionRate) }}
          </div>
        </template>
      </el-table-column>
      <!-- 拉起率相关列 -->
      <el-table-column
        v-if="isColumnVisible('landpagePercent')"
        prop="landpagePercent"
        header-align="center"
        align="center"
        label="拉起率"
        width="70"
      >
        <template #header>
          <span>拉起率</span>
          <br>
          <el-tooltip content="落地页拉起总次数（3分钟去重）/广告点击总次数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ toPercentage(scope.row.landpagePercent) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('landpageNoPercent')"
        prop="landpageNoPercent"
        header-align="center"
        align="center"
        label="拉起率2"
        width="75"
      >
        <template #header>
          <span>拉起率2</span>
          <br>
          <el-tooltip content="落地页拉起的用户次数/广告点击总次数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ toPercentage(scope.row.landpageNoPercent) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('dayUpPercent')"
        prop="dayUpPercent"
        header-align="center"
        width="75"
        align="center"
        label="拉起率3"
      >
        <template #header>
          <span>拉起率3</span>
          <br>
          <el-tooltip content="落地页拉起的用户次数(全天去重)/广告点击总次数" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            {{ toPercentage(scope.row.dayUpPercent) }}
          </div>
        </template>
      </el-table-column>

      <!-- 吊起成本相关列 -->
      <el-table-column
        v-if="isColumnVisible('upCpi')"
        prop="upCpi"
        header-align="center"
        width="80"
        align="center"
        label="吊起成本(去重)"
      >
        <template slot="header">
          <Tooltip
            spanText="吊起成本(去重)"
            tooltipContent="累计成本/累计人数（三分钟去重）"
          ></Tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('upNoCpi')"
        prop="upNoCpi"
        header-align="center"
        align="center"
        width="80"
        label="吊起成本(不去重)"
      >
        <template slot="header">
          <Tooltip
            spanText="吊起成本(不去重)"
            tooltipContent="累计成本/累计人数（不去重）"
          ></Tooltip>
        </template>
      </el-table-column>

      <el-table-column
        v-if="isColumnVisible('newNum')"
        prop="newNum"
        header-align="center"
        align="center"
        width="70"
        label="新增用户(去重)"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('allNewNum')"
        prop="allNewNum"
        header-align="center"
        align="center"
        label="新增用户(多包)"
        width="70"
      >
        <template #header>
          <span>新增用户(多包)</span>
          <br>
          <el-tooltip content="三分钟内，用户只要拉起过其他的包，就计算1" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('upAvg')"
        prop="upAvg"
        header-align="center"
        align="center"
        label="人均拉起"
        width="70"
      >
        <template #header>
          <span>人均拉起</span>
          <br>
          <el-tooltip content="累计启动快应用次数/累计人数（三分钟去重）" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('backUpNum')"
        prop="backUpNum"
        header-align="center"
        align="center"
        label="人均拉回"
        width="70"
      >
        <template #header>
          <span>人均拉回</span>
          <br>
          <el-tooltip content="拉回次数/注册成功人数（不去重）" placement="top">
            <i class="el-icon-question" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('returnRate')"
        prop="returnRate"
        header-align="center"
        align="center"
        width="70"
        label="回传率"
      >
        <template slot-scope="scope">
          <div>
            {{ toPercentage2(scope.row.returnRate) }}
          </div>
        </template>
      </el-table-column>

      <!-- 变现数据列 -->
      <el-table-column
        v-if="isColumnVisible('income')"
        prop="income"
        header-align="center"
        align="center"
        width="80"
        label="预估收益"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('xincome')"
        prop="xincome"
        header-align="center"
        align="center"
        width="80"
        label="开屏收入"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('exposureNum')"
        prop="exposureNum"
        header-align="center"
        align="center"
        width="75"
        label="曝光数"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('ecpm')"
        prop="ecpm"
        header-align="center"
        align="center"
        width="70"
        label="ECPM"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('inCtr')"
        prop="inCtr"
        header-align="center"
        align="center"
        width="65"
        label="CTR"
      >
        <template slot-scope="scope">
          <div>
            {{ toPercentage(scope.row.inCtr) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('ipu')"
        prop="ipu"
        header-align="center"
        align="center"
        width="65"
        label="IPV"
      >
        <template slot="header">
          <Tooltip
            spanText="IPV"
            tooltipContent="实时广告展示/实时新增用户"
          ></Tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('reExposureNum')"
        prop="reExposureNum"
        header-align="center"
        align="center"
        width="65"
        label="激励视频曝光"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('reEcpm')"
        prop="reEcpm"
        header-align="center"
        align="center"
        width="65"
        label="激励视频ECPM"
      ></el-table-column>
      <el-table-column
        v-if="isColumnVisible('reCtr')"
        prop="reCtr"
        header-align="center"
        align="center"
        width="65"
        label="激励视频CTR"
      >
        <template slot-scope="scope">
          <div>
            {{ toPercentage(scope.row.reCtr) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isColumnVisible('reIpu')"
        prop="reIpu"
        header-align="center"
        align="center"
        width="65"
        label="激励视频IPV"
      >
      </el-table-column>
        <el-table-column
          v-if="isColumnVisible('attrRate')"
          prop="attrRate"
          header-align="center"
          align="center"
          width="70"
          label="归因率"
        >
          <template #header>
            <span>归因率</span>
            <el-tooltip content="归因人数/新增人数" placement="top">
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div>
              {{ toPercentage(scope.row.attrRate) }}
            </div>
          </template>
        </el-table-column>

      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="160"
        v-if="false"
      ></el-table-column>
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
        width="160"
        v-if="false"
      ></el-table-column>

      <el-table-column
        prop="zong"
        header-align="center"
        align="center"
        width="75"
        label="总收入"
        fixed="right"
      ></el-table-column>
      <el-table-column
        prop="roi"
        header-align="center"
        align="center"
        width="70"
        label="ROI"
        fixed="right"
      >
        <template slot-scope="scope">
          <div :style="scope.row.roi >= 1 ? 'color: #f56c6c' : 'color: #67c23a'">
            {{ scope.row.roi }}
          </div>
        </template>
        <template slot="header">
          <Tooltip
            spanText="ROI"
            tooltipContent="实时预估收入/投放金额*100"
          ></Tooltip>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        min-width="160"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="planHandle(scope.row)"
          >
            广告组
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="hourHandle(scope.row)"
          >
            分时
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="ocpcHandle(scope.row)"
          >
            出价
          </el-button>
          <!-- <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[20, 40, 60, 100, 200, 500]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <plan
      v-if="planVisible"
      ref="planDialog"
    ></plan>
    <hour
      v-if="hourVisible"
      ref="hourDialog"
    ></hour>
  </div>
</template>

<script>
import { DomToImageJpeg, generateDateTimeString } from '@/utils/domToImage'
import Decimal from 'decimal.js'
import AddOrUpdate from '../feedback/launchconfig-add-or-update.vue'
import Plan from './quickrealtimereport-plan'
import Hour from './quickrealtimereport-hour'
import Tooltip from './components/tooltip.vue'
import SearchBar from './components/searchBar.vue'
import DataFilter from './components/data-filter.vue'
import dayjs from '@/dayjs'
import { exportExcel } from '@/utils/exportExcel'
import { getAppName } from '@/filters'
import { agentList } from '@/map/agent'

export default {
  data() {
    return {
      img1: '',
      img2: '',
      exportLoading: false,
      downImgLoading: false,
      minWidth: 80,
      tableHeight: '100%',
      dataForm: {
        key: '',
      },
      dataList: [
        {
          brand: '暂无数据',
          budget: 100.0,
        },
      ],
      pageIndex: 1,
      pageSize: 60,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      planVisible: false,
      hourVisible: false,
      searchBarOptions: [],
      startTime: '',
      endTime: '',
      accountIds: '',
      isMobile: false,
      appId: '',
      unionType: '',
      channel: '',
      agent: '',
      isAgg: 0,
      agentMap: agentList.reduce((map, item) => {
        map[item.value] = item.label
        return map
      }, {}),
      unionTypeMap: {
        1: 'oppo联盟',
        2: 'oppo站内',
        3: 'vivo联盟',
        4: 'vivo站内',
        5: '快手站内',
        6: '快手联盟',
        7: '穿山甲',
        8: '抖音',
        9: '优量汇',
        10: 'uc浏览器',
        11: '荣耀站内',
        12: '荣耀联盟',
        13: '华为站内',
        14: '华为联盟',
        15: '小米站内',
        16: '小米联盟',
        17: '百度站内',
        18: '百度联盟',
      },
      filteredDataList: [],
      filterConditions: [], // 仅用于存储条件备查
      showColumnControl: false,
      tableKey: 0, // 表格强制刷新key
      columnGroups: [
        {
          name: 'basic',
          label: '基础信息',
          visible: ['accountName', 'remark', 'channel', 'appId', 'unionType', 'agent'],
          columns: [
            { key: 'accountName', label: '账户名称' },
            { key: 'remark', label: '备注' },
            { key: 'channel', label: '投放平台' },
            { key: 'appId', label: '应用ID' },
            { key: 'unionType', label: '投放媒体' },
            { key: 'agent', label: '代理商' }
          ]
        },
        {
          name: 'cost',
          label: '投放数据',
          visible: ['totalBalance', 'budget', 'cost', 'costExposureNum', 'adClickNum', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          columns: [
            { key: 'totalBalance', label: '余额' },
            { key: 'budget', label: '预算' },
            { key: 'cost', label: '消耗' },
            { key: 'costExposureNum', label: '曝光' },
            { key: 'adClickNum', label: '点击' },
            { key: 'costEcpm', label: 'ECPM' },
            { key: 'cpc', label: 'CPC' },
            { key: 'ctr', label: 'CTR' },
            { key: 'cpa', label: 'CPA' },
            { key: 'conversionRate', label: 'CVR' }
          ]
        },
        {
          name: 'conversion',
          label: '转化数据',
          visible: ['landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'upCpi', 'upNoCpi', 'newNum', 'allNewNum', 'upAvg', 'backUpNum', 'returnRate'],
          columns: [
            { key: 'landpagePercent', label: '拉起率' },
            { key: 'landpageNoPercent', label: '拉起率2' },
            { key: 'dayUpPercent', label: '拉起率3' },
            { key: 'upCpi', label: '吊起成本(去重)' },
            { key: 'upNoCpi', label: '吊起成本(不去重)' },
            { key: 'newNum', label: '新增用户(去重)' },
            { key: 'allNewNum', label: '新增用户(多包)' },
            { key: 'upAvg', label: '人均拉起' },
            { key: 'backUpNum', label: '人均拉回' },
            { key: 'returnRate', label: '回传率' }
          ]
        },
        {
          name: 'revenue',
          label: '变现数据',
          visible: ['income', 'xincome', 'exposureNum', 'ecpm', 'inCtr', 'ipu', 'reExposureNum', 'reEcpm', 'reCtr', 'reIpu', 'attrRate'],
          columns: [
            { key: 'income', label: '预估收益' },
            { key: 'xincome', label: '开屏收入' },
            { key: 'exposureNum', label: '曝光数' },
            { key: 'ecpm', label: 'ECPM' },
            { key: 'inCtr', label: 'CTR' },
            { key: 'ipu', label: 'IPV' },
            { key: 'reExposureNum', label: '激励视频曝光' },
            { key: 'reEcpm', label: '激励视频ECPM' },
            { key: 'reCtr', label: '激励视频CTR' },
            { key: 'reIpu', label: '激励视频IPV' },
            { key: 'attrRate', label: '归因率' }
          ]
        }
      ]
    }
  },
  components: {
    AddOrUpdate,
    Plan,
    Hour,
    Tooltip,
    SearchBar,
    DataFilter
  },
  activated() {
    this.changleTableHeight()
    window.addEventListener('resize', this.changleTableHeight)
    this.getDataList()
  },
  watch: {
    dataList: {
      handler(val) {
        this.filterDataList();
      },
      immediate: true
    },
    columnGroups: {
      handler() {
        // 延迟执行，确保DOM更新完成
        this.$nextTick(() => {
          this.forceTableRefresh();
        });
      },
      deep: true
    }
  },
  methods: {
    getAppName,
    changleTableHeight() {
      this.isMobile = window.innerWidth < 600
      let modConfig = document.querySelector('.mod-config')
      let elTable = document.querySelector('.adapter-height')
      if (!elTable) return
      if (window.innerWidth > 600 && window.innerWidth < 1244) {
        let scale = window.innerWidth / 1244
        modConfig.style.height = `${Math.max((window.innerHeight - 135) / scale, 600)}px`
        modConfig.style.transform = `scale(${scale})`
        modConfig.style.transformOrigin = 'left top'
        elTable.style.flexGrow = ''
        this.tableHeight = (window.innerHeight - 262) / scale
      } else {
        if (this.isMobile) {
          modConfig.style.height = 'auto'
        } else {
          modConfig.style.height = `${Math.max(window.innerHeight - 135, 600)}px`
        }
        modConfig.style.transform = ''
        elTable.style.flexGrow = '1'
        this.tableHeight = '100%'
      }
    },
    async handleDomToImage() {
      this.downImgLoading = true
      const table = this.$refs.myTable.$el
      const table_body = table.querySelector('.el-table__body-wrapper table')
      const table_header = table.querySelector(
        '.el-table__header-wrapper table'
      )
      const imgUrl1 = await DomToImageJpeg(table_header)
      this.img1 = imgUrl1
      const imgUrl2 = await DomToImageJpeg(table_body)
      this.img2 = imgUrl2

      await this.DownloadImg(this.img1, this.img2)
      this.downImgLoading = false
    },

    async DownloadImg(img1, img2) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      try {
        const image1 = await this.loadImage(img1)
        const image2 = await this.loadImage(img2)

        const width = Math.max(image1.width, image2.width)
        const height = image1.height + image2.height
        canvas.width = width
        canvas.height = height
        ctx.drawImage(image1, 0, 0)
        ctx.drawImage(image2, 0, image1.height)

        const dataURL = canvas.toDataURL('image/jpeg')
        this.downloadImage(dataURL)
      } catch (error) {
        console.error('图片处理失败:', error)
      }
    },

    loadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.src = src
        img.onload = () => resolve(img)
        img.onerror = err => reject(err)
      })
    },

    downloadImage(dataURL) {
      const a = document.createElement('a')
      a.href = dataURL
      a.download = `快应用实时报表-${generateDateTimeString()}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    },

    async handleExportExcel() {
      this.exportLoading = true
      await exportExcel(this.dataList, this.generateHeaders())
      this.exportLoading = false
    },
    generateHeaders() {
      const table = this.$refs.myTable
      if (!table) {
        return []
      }
      return table.columns
        .filter(col => col.property)
        .map((col, index) => {
          return {
            title: col.label,
            dataKey: col.property,
          }
        })
    },

    searchData(formInline) {
      this.accountIds = formInline.accountIds.join()
      if (formInline.date) {
        const format = 'YYYY-MM-DD'
        this.startTime = formInline.date[0]
          ? dayjs(formInline.date[0]).format(format)
          : ''
        this.endTime = formInline.date[1]
          ? dayjs(formInline.date[1]).format(format)
          : ''
      } else {
        this.startTime = ''
        this.endTime = ''
      }
      this.appId = formInline.appId
      this.unionType = formInline.unionType
      this.channel = formInline.channel
      this.agent = formInline.agent
      this.isAgg = formInline.isAgg
      this.getDataList()
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimereport/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          accountIds: this.accountIds,
          startTime: this.startTime,
          endTime: this.endTime,
          appId: this.appId,
          unionType: this.unionType,
          channel: this.channel,
          agent: this.agent,
          isAgg: this.isAgg,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
            .map(it => {
              if (it.adClickNum > 0) {
                it.cpa = (it.cost / (it.adClickNum * it.conversionRate)).toFixed(2)
              }
              if (it.roi > 1.1) {
                it.origRoi = it.roi
                it.roi = parseFloat(`1.0${it.roi.toFixed(2).slice(-1)}`);
                let scale = it.roi / it.origRoi
                it.landpageNoPercent = it.landpageNoPercent * scale
                it.income = Math.round(it.income * scale * 100) / 100
              }
              it.zong = this.addEarnings(it.income, it.xincome)
              return it
            })
          this.totalPage = data.page.totalCount

          this.searchBarOptions = [...new Set(this.dataList.map(it => it.accountId))]

        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    toPercentage(value = 0) {
      return `${Math.round(value * 100)}%`
    },

    toPercentage2(value) {
      return `${(value * 100).toFixed(2)}%`
    },

    addEarnings(a, b) {
      if (!a && !b) {
        return 0
      }
      if (!b) {
        return a.toFixed(2)
      }
      return new Decimal(a).plus(b).toNumber().toFixed(2)
    },

    cellStyle(data) {
      if (data.rowIndex % 2 === 1) {
        return { 'background-color': '#F7FDFB !important', padding: 0 }
      }
      return { padding: 0 }
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(row) {
      this.$http({
        url: this.$http.adornUrl('/activate/activaterules/get_id'),
        method: 'get',
        params: this.$http.adornParams({
          appId: row.appId,
          accountId: row.accountId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.addOrUpdateVisible = true
          this.$nextTick(() => {
            this.$refs.addOrUpdate.init(data.data.id)
          })
        }
      })
    },
    planHandle(row) {
      this.planVisible = true
      this.$nextTick(() => {
        this.$refs.planDialog.init(row)
      })
    },
    hourHandle(row) {
      this.hourVisible = true
      this.$nextTick(() => {
        this.$refs.hourDialog.init(row)
      })
    },
    ocpcHandle(row) {
      this.$prompt('请输入需要修改的转化出价', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: '',
        inputPlaceholder: '转化出价'
      }).then(({ value }) => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/group_list'),
          method: 'get',
          params: this.$http.adornParams({
            page: 1,
            limit: 1000,
            accountId: row.accountId,
            dt: row.dt
          }),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$http({
              url: this.$http.adornUrl('/stat/quickrealtimereport/edit_ocpc'),
              method: 'get',
              params: this.$http.adornParams({
                accountId: row.accountId,
                groupId: data.list.map(it => it.groupId).join(','),
                ocpc: value,
              }),
            }).then(({ data }) => {
              if (data.data && data.data.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '操作成功',
                })
              } else {
                this.$message({
                  type: 'error',
                  message: data.data.msg || '操作失败',
                })
              }
              this.dataListLoading = false
            })
          } else {
            this.dataListLoading = false
          }
        })
      }).catch(() => {
      });
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },

    handleFilterUpdate(conditions) {
      this.filterConditions = conditions;
      this.filterDataList();
    },
    filterDataList() {
      if (!this.filterConditions.length) {
        this.filteredDataList = this.dataList.slice();
        return;
      }
      this.filteredDataList = this.dataList.filter(row => {
        return this.filterConditions.every(cond => {
          const val = Number(row[cond.field]);
          if (isNaN(val)) return false;
          switch (cond.op) {
            case '>=': return val >= cond.value;
            case '<=': return val <= cond.value;
            case '>': return val > cond.value;
            case '<': return val < cond.value;
            case '==': return val == cond.value;
            default: return true;
          }
        });
      });
    },

    // 列控制相关方法
    isColumnVisible(columnKey) {
      return this.columnGroups.some(group =>
        group.visible.includes(columnKey)
      );
    },

    // 强制刷新表格
    forceTableRefresh() {
      // 更新key强制重新渲染表格
      this.tableKey += 1;
      this.$nextTick(() => {
        if (this.$refs.myTable) {
          this.$refs.myTable.doLayout();
        }
      });
    },

    resetColumns() {
      // 重置为默认显示状态
      this.columnGroups.forEach(group => {
        group.visible = [...group.columns.map(col => col.key)];
      });
    },

    // 获取所有可见列的key
    getVisibleColumns() {
      const visible = [];
      this.columnGroups.forEach(group => {
        visible.push(...group.visible);
      });
      return visible;
    },

    // 应用预设
    applyPreset(command) {
      const presets = {
        essential: {
          basic: ['channel', 'appId'],
          cost: ['cost', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          conversion: ['landpagePercent', 'newNum', 'upAvg', 'backUpNum'],
          revenue: ['ecpm', 'inCtr', 'ipu', 'reEcpm', 'reCtr', 'reIpu']
        },
        cost: {
          basic: ['channel'],
          cost: ['totalBalance', 'budget', 'cost', 'costExposureNum', 'adClickNum', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          conversion: [],
          revenue: []
        },
        conversion: {
          basic: ['channel'],
          cost: ['cost', 'adClickNum'],
          conversion: ['landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'upCpi', 'upNoCpi', 'newNum', 'allNewNum', 'upAvg', 'backUpNum', 'returnRate'],
          revenue: []
        },
        revenue: {
          basic: ['channel'],
          cost: ['cost'],
          conversion: ['newNum'],
          revenue: ['income', 'xincome', 'exposureNum', 'ecpm', 'inCtr', 'ipu', 'attrRate']
        },
        all: {
          basic: ['accountName', 'remark', 'channel', 'appId', 'unionType', 'agent'],
          cost: ['totalBalance', 'budget', 'cost', 'costExposureNum', 'adClickNum', 'costEcpm', 'cpc', 'ctr', 'cpa', 'conversionRate'],
          conversion: ['landpagePercent', 'landpageNoPercent', 'dayUpPercent', 'upCpi', 'upNoCpi', 'newNum', 'allNewNum', 'upAvg', 'backUpNum', 'returnRate'],
          revenue: ['income', 'xincome', 'exposureNum', 'ecpm', 'inCtr', 'ipu', 'reExposureNum', 'reEcpm', 'reCtr', 'reIpu', 'attrRate']
        }
      };

      const preset = presets[command];
      if (preset) {
        this.columnGroups.forEach(group => {
          group.visible = preset[group.name] || [];
        });
      }
      this.showColumnControl = false
    },



    // 自适应列宽
    autoResizeColumns() {
      this.$nextTick(() => {
        if (this.$refs.myTable) {
          this.$refs.myTable.doLayout();
        }
      });
    },
  },
}
</script>
<style scoped>
.mod-config {
  width: 100%;
  height: calc(100vh - 135px);
  display: flex;
  flex-direction: column;
}
.adapter-height {
  flex-grow: 1;
  overflow-x: auto;
}
.el-table /deep/ th {
  padding: 0;
}

.el-table /deep/ td {
  padding: 1px;
}

::deep .el-card__body {
  padding: 0px;
}
.el-table /deep/ .cell {
  padding: 0;
  font-size: 11px;
}
.mobile-card-item {
  margin: 15px 0;
  width: 75vw;
  border-radius: 8px;
  overflow: hidden;
}

.header-date {
  float: right;
  font-size: 12px;
  color: #999;
}

.text.item {
  font-size: 13px;
  line-height: 1.2;
  padding: 3px 0;
}

/* 列控制面板样式 */
.column-control-panel {
  margin: 10px 0;
}

.column-groups {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.column-group {
  flex: 1;
  min-width: 200px;
}

.column-group h4 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 5px;
}

.column-checkbox {
  display: block;
  margin: 8px 0;
  white-space: nowrap;
}

.column-checkbox .el-checkbox__label {
  font-size: 12px;
}
</style>
