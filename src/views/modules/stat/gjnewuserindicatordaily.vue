<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="时间">
        <el-date-picker
          clearable
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :default-time="['00:00:00', '23:59:00']"
        />
      </el-form-item>
      <el-form-item label="选择应用">
        <app-select
          ref="appSelect"
          :has-all="false"
          :auto-init="false"
          @change="handleAppChange"
          @init-app-id="handleInitApp"
        />
      </el-form-item>
      <el-form-item label="投放账户" prop="advertiseId">
        <el-select-extend
          v-model="dataForm.advertiseIdList"
          placeholder="投放账户"
          clearable
          filterable
          multiple
          collapse-tags
          :data-list="advertiseIdList"
          style="width: 220px;"
        >
          <el-option
            v-for="(item, index) in advertiseIdList"
            :key="index"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="渠道" prop="channelName">
        <el-select-extend
          v-model="dataForm.channelNameList"
          placeholder="渠道"
          clearable
          multiple
          collapse-tags
          :data-list="channelNameList"
        >
          <el-option
            v-for="item in channelNameList"
            :key="item"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="渠道号">
        <el-select-extend
          v-model="dataForm.marketCodeList"
          multiple
          filterable
          clearable
          collapse-tags
          style="width: 260px;"
          :data-list="marketCodeList"
        >
          <el-option
            v-for="(item, index) in marketCodeList"
            :key="index"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="版本号">
        <el-select-extend
          v-model="dataForm.appVersionList"
          multiple
          clearable
          collapse-tags
          :data-list="appVersionList"
        >
          <el-option
            v-for="(item, index) in appVersionList"
            :key="index"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="品牌">
        <el-select-extend
          v-model="dataForm.brandList"
          multiple
          filterable
          clearable
          @change="getModelList"
          collapse-tags
          :data-list="brandList"
        >
          <el-option
            v-for="(item, index) in brandList"
            :key="index"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="机型">
        <el-select-extend
          v-model="dataForm.modelList"
          multiple
          filterable
          clearable
          collapse-tags
          :data-list="modelList"
        >
          <el-option
            v-for="(item, index) in modelList"
            :key="index"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="系统">
        <el-select-extend
          v-model="dataForm.androidVersionList"
          multiple
          clearable
          collapse-tags
          :data-list="androidVersionList"
        >
          <el-option
            v-for="(item, index) in androidVersionList"
            :key="index"
            :value="item"
          />
        </el-select-extend>
      </el-form-item>
      <el-form-item label="国家">
        <country-select v-model="dataForm.country" />
      </el-form-item>
      <el-form-item>
        <el-button
          @click="getDataList()"
          type="primary"
          icon="el-icon-search"
          :loading="dataListLoading"
        >
          查询
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel(false)"
        >
          下载Excel
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:gjnewuserindicatordaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('stat:gjnewuserindicatordaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="dt"
        header-align="center"
        align="center"
        label="日期"
        width="90"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
        width="120"
      >
        <template slot-scope="{ row }">
          <span>{{ row.appId | getAppName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="channelName"
        header-align="center"
        align="center"
        label="渠道名"
      />
      <el-table-column
        prop="marketCode"
        header-align="center"
        align="center"
        label="渠道号"
      />
      <el-table-column
        prop="advertiseId"
        header-align="center"
        align="center"
        label="投放账户"
      />
      <el-table-column
        prop="brand"
        header-align="center"
        align="center"
        label="品牌"
      />
      <el-table-column
        prop="model"
        header-align="center"
        align="center"
        label="机型"
      />
      <el-table-column
        prop="androidVersion"
        header-align="center"
        align="center"
        label="系统"
      />
      <el-table-column
        prop="appVersion"
        header-align="center"
        align="center"
        label="版本号"
      />
      <el-table-column
        prop="dnu"
        header-align="center"
        align="center"
        label="新增用户量"
        width="90"
      />
      <!--<el-table-column-->
      <!--  prop="impUv"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="总展示设备数"-->
      <!--  width="100"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="impCnt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="总展示次数"-->
      <!--  width="90"-->
      <!--/>-->

      <el-table-column
        prop="cost"
        header-align="center"
        align="center"
        label="成本"
      />
      <el-table-column
        prop="adConversion"
        header-align="center"
        align="center"
        label="投放新增"
      />
      <el-table-column
        prop="cpi"
        header-align="center"
        align="center"
        label="cpi"
      />

      <el-table-column
        prop="income"
        header-align="center"
        align="center"
        label="收入"
      />

      <el-table-column
        prop="newArpu"
        header-align="center"
        align="center"
        label="新增arpu"
      />
      <el-table-column
        prop="newIpu"
        header-align="center"
        align="center"
        label="新增ipu"
      />
      <el-table-column
        prop="showIpu"
        header-align="center"
        align="center"
        label="展示广告用户ipu"
        width="120"
      />
      <el-table-column
        prop="arvEcpm"
        header-align="center"
        align="center"
        label="ecpm"
      />
      <el-table-column
        prop="exposureRatio"
        header-align="center"
        align="center"
        label="广告曝光设备比"
        width="120"
      />

      <el-table-column
        prop="keep2"
        header-align="center"
        align="center"
        label="心跳/UI次留"
        width="110"
      />
      <el-table-column
        prop="keep3"
        header-align="center"
        align="center"
        label="心跳/UI3留"
        width="110"
      />
      <el-table-column
        prop="keep7"
        header-align="center"
        align="center"
        label="心跳/UI7留"
        width="110"
      />
      <el-table-column
        prop="keep14"
        header-align="center"
        align="center"
        label="心跳/UI14留"
        width="110"
      />
      <el-table-column
        prop="keep30"
        header-align="center"
        align="center"
        label="心跳/UI30留"
        width="110"
      />
      <!--<el-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.dt)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.dt)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000, 5000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AddOrUpdate from './gjnewuserindicatordaily-add-or-update'
import ElSelectExtend from '@/components/el-select-extend'
import { mixinElTableAdapterHeight } from '@/mixins'
import AppSelect from '@/components/app-select'
import androidVersionList from '@/map/androidVersion.json'
import { channelNameList } from '@/map/common'
import { uniq } from 'lodash'
import CountrySelect from '@/components/country-select'
// 这些品牌必须写最前面
const firstBrandList = ['huawei', 'honor', 'vivo', 'oppo', 'xiaomi', 'redmi']
import { countryList } from '@/map/sat'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        startTime: '',
        endTime: '',
        appVersionList: [],
        marketCodeList: [],
        brandList: [],
        modelList: [],
        androidVersionList: [],
        advertiseIdList: [],
        channelNameList: [],
        country: countryList[0].country,
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      marketCodeList: [],
      appVersionList: [],
      brandList: [],
      modelList: [],
      advertiseIdList: [],
      androidVersionList,
      channelNameList,
    }
  },
  computed: {
    timeRange: {
      set(t) {
        if (t) {
          this.dataForm.startTime = t[0] || ''
          this.dataForm.endTime = t[1] || ''
        } else {
          this.dataForm.startTime = ''
          this.dataForm.endTime = ''
        }
      },
      get() {
        return [this.dataForm.startTime, this.dataForm.endTime]
      },
    },
  },
  components: {
    CountrySelect,
    AddOrUpdate,
    AppSelect,
    ElSelectExtend,
  },
  activated() {
    // this.getDataList()
    this.$refs.appSelect.init()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      this.$http({
        url: this.$http.adornUrl('/stat/gjnewuserindicatordaily/key_list'),
        method: 'post',
        data: this.$http.adornData({
          ...this.dataForm,
          page: this.pageIndex,
          pageSize: this.pageSize,
          appCode: this.getAppCode(),
          brandList: this.dataForm.brandList.map(it => it.toLowerCase()),
          modelList: this.dataForm.modelList.map(it => it.toLowerCase()),
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          const adapterData = (attr, attr2, it) =>
            this.dataForm[attr] && this.dataForm[attr].length ? it[attr2] : '-'

          const adapterKeep = (attr, attr2, it) => {
            const comDay = it[attr] / it.dnu
            const comUI = it[attr2] / it.dnu

            const fn = n => (n === 0 ? '0' : (n * 100).toFixed(0) + '%')

            return fn(comDay) + ' / ' + fn(comUI)
          }

          this.dataList = data.page.list.map(it => {
            return {
              ...it,
              impCnt: it.impCnt || '-',
              income: it.income ? it.income.toFixed(2) : '-',
              newArpu: it.newArpu ? it.newArpu.toFixed(2) : '-',
              newIpu: it.newIpu ? it.newIpu.toFixed(2) : '-',
              showIpu: it.showIpu ? it.showIpu.toFixed(2) : '-',
              arvEcpm: it.arvEcpm ? it.arvEcpm.toFixed(2) : '-',
              keep2: adapterKeep('appDay2', 'uiDay2', it),
              keep3: adapterKeep('appDay3', 'uiDay3', it),
              keep7: adapterKeep('appDay7', 'uiDay7', it),
              keep14: adapterKeep('appDay14', 'uiDay14', it),
              keep30: adapterKeep('appDay30', 'uiDay30', it),
              exposureRatio: Number(it.exposureRatio.toFixed(2)) * 100 + '%',
              marketCode: adapterData('marketCodeList', 'marketCode', it),
              brand: adapterData('brandList', 'brand', it),
              model: adapterData('modelList', 'model', it),
              androidVersion: adapterData(
                'androidVersionList',
                'androidVersion',
                it
              ),
              appVersion: adapterData('appVersionList', 'appVersion', it),
            }
          })
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
          this.$message.error(data.msg || '服务器错误')
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.dt
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/gjnewuserindicatordaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 获取筛选项的值,app相关
    getSelectReApp() {
      this.dataForm.marketCodeList = []
      this.dataForm.appVersionList = []
      this.dataForm.brandList = []
      this.dataForm.modelList = []
      this.dataForm.androidVersionList = []
      this.dataForm.advertiseIdList = []

      // 查询某个app 最近30天的market_code
      const pMarketCodeList = this.baseGet(
        '/stat/activekeepdaily/market_code_list'
      )
      // #查询某个app 最近30天的 应用版本
      const pAppVersionList = this.baseGet(
        '/stat/activekeepdaily/app_version_list'
      )
      // #查询某个app 最近30天的品牌
      const pBrandList = this.baseGet('/stat/activekeepdaily/brand_list')
      // 投放账号
      const pAdvertiseInfo = this.baseGet(
        '/stat/activekeepdaily/advertise_info'
      )

      Promise.allSettled([
        pMarketCodeList,
        pAppVersionList,
        pBrandList,
        pAdvertiseInfo,
      ]).then(results => {
        results.forEach(({ value }, index) => {
          switch (index) {
            case 0:
              if (value && value.data.code === 0 && value.data.data) {
                this.marketCodeList = value.data.data.filter(it => it)
              } else {
                this.marketCodeList = []
              }
              break
            case 1:
              if (value && value.data.code === 0 && value.data.data) {
                this.appVersionList = value.data.data.filter(it => it)
              } else {
                this.appVersionList = []
              }
              break
            case 2:
              if (value && value.data.code === 0 && value.data.data) {
                this.brandList = value.data.data.filter(it => it)
                this.brandList = uniq([...firstBrandList, ...this.brandList])
              } else {
                this.brandList = []
              }
              break
            case 3:
              if (
                value &&
                value.data.code === 0 &&
                value.data.data &&
                Array.isArray(value.data.data)
              ) {
                this.advertiseIdList = uniq(
                  value.data.data.map(it => it.advertiseId)
                )
              } else {
                this.advertiseIdList = []
              }
              break
          }
        })
      })
    },
    getAppCode() {
      const res = this.$store.state.ad.appList.find(
        it => it.id === this.$store.state.ad.appId
      )
      return res && res.code ? res.code : ''
    },
    baseGet(url) {
      return this.$http({
        url: this.$http.adornUrl(url),
        method: 'get',
        params: this.$http.adornParams({
          appCode: this.getAppCode(),
        }),
      })
    },
    getModelList(brandList = this.dataForm.brandList) {
      console.log('change...')
      this.dataForm.modelList = []

      if (!brandList || !brandList.length) {
        this.modelList = []
        return
      }

      const pModelList = brandList.map(brand =>
        // 查询某个app 最近30天的机型
        this.baseGet(`/stat/activekeepdaily/model_list?brand=${brand}`)
      )

      Promise.all(pModelList).then(values => {
        this.modelList = values
          .filter(it => it.data.code === 0 && it.data.data)
          .map(it => it.data.data)
          .reduce(
            (previousValue, currentValue) => previousValue.concat(currentValue),
            []
          )
      })
    },
    handleInitApp() {
      this.getSelectReApp()
      this.currentChangeHandle(1)
    },
    handleAppChange() {
      this.getSelectReApp()
      this.currentChangeHandle(1)
    },
  },
}
</script>
