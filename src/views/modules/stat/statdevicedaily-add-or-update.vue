<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用ID" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用ID"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="启动次数" prop="startCount">
      <el-input v-model="dataForm.startCount" placeholder="启动次数"></el-input>
    </el-form-item>
    <el-form-item label="注册设备" prop="regCount">
      <el-input v-model="dataForm.regCount" placeholder="注册设备"></el-input>
    </el-form-item>
    <el-form-item label="注册市场用户量" prop="regMkCount">
      <el-input v-model="dataForm.regMkCount" placeholder="注册市场用户量"></el-input>
    </el-form-item>
    <el-form-item label="新增数量" prop="newCount">
      <el-input v-model="dataForm.newCount" placeholder="新增数量"></el-input>
    </el-form-item>
    <el-form-item label="活跃数量" prop="aliveCount">
      <el-input v-model="dataForm.aliveCount" placeholder="活跃数量"></el-input>
    </el-form-item>
    <el-form-item label="激活回传数" prop="activateCount">
      <el-input v-model="dataForm.activateCount" placeholder="激活回传数"></el-input>
    </el-form-item>
    <el-form-item label="次留回传数" prop="secondCount">
      <el-input v-model="dataForm.secondCount" placeholder="次留回传数"></el-input>
    </el-form-item>
    <el-form-item label="市场劫持数" prop="hijackCount">
      <el-input v-model="dataForm.hijackCount" placeholder="市场劫持数"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          day: '',
          startCount: '',
          regCount: '',
          regMkCount: '',
          newCount: '',
          aliveCount: '',
          activateCount: '',
          secondCount: '',
          hijackCount: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用ID不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          startCount: [
            { required: true, message: '启动次数不能为空', trigger: 'blur' }
          ],
          regCount: [
            { required: true, message: '注册设备不能为空', trigger: 'blur' }
          ],
          regMkCount: [
            { required: true, message: '注册市场用户量不能为空', trigger: 'blur' }
          ],
          newCount: [
            { required: true, message: '新增数量不能为空', trigger: 'blur' }
          ],
          aliveCount: [
            { required: true, message: '活跃数量不能为空', trigger: 'blur' }
          ],
          activateCount: [
            { required: true, message: '激活回传数不能为空', trigger: 'blur' }
          ],
          secondCount: [
            { required: true, message: '次留回传数不能为空', trigger: 'blur' }
          ],
          hijackCount: [
            { required: true, message: '市场劫持数不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/statdevicedaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.statDeviceDaily.appId
                this.dataForm.day = data.statDeviceDaily.day
                this.dataForm.startCount = data.statDeviceDaily.startCount
                this.dataForm.regCount = data.statDeviceDaily.regCount
                this.dataForm.regMkCount = data.statDeviceDaily.regMkCount
                this.dataForm.newCount = data.statDeviceDaily.newCount
                this.dataForm.aliveCount = data.statDeviceDaily.aliveCount
                this.dataForm.activateCount = data.statDeviceDaily.activateCount
                this.dataForm.secondCount = data.statDeviceDaily.secondCount
                this.dataForm.hijackCount = data.statDeviceDaily.hijackCount
                this.dataForm.createdAt = data.statDeviceDaily.createdAt
                this.dataForm.updatedAt = data.statDeviceDaily.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/statdevicedaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'day': this.dataForm.day,
                'startCount': this.dataForm.startCount,
                'regCount': this.dataForm.regCount,
                'regMkCount': this.dataForm.regMkCount,
                'newCount': this.dataForm.newCount,
                'aliveCount': this.dataForm.aliveCount,
                'activateCount': this.dataForm.activateCount,
                'secondCount': this.dataForm.secondCount,
                'hijackCount': this.dataForm.hijackCount,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
