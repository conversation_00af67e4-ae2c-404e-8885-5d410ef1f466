<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
      'copy',
    ]"
    operate-width="180"
    :before-select="beforeSelect"
    @model-submit="modelSubmit"
    @operate-add="operateAdd"
    @operate-edit="operateEdit"
    @operate-copy="operateCopy"
  >
    <template #form_appId>
      <app-select-component
        v-model="selectFormData.appId"
        :is-show-all="false"
        clearable
      />
    </template>
    <template #form_network>
      <ad-platform-select v-model="selectFormData.network" version="2" />
    </template>
    <template #form_city>
      <country-select v-model="selectFormData.city" :has-all="false" />
    </template>
    <template #model>
      <el-form-item label="应用" prop="appId">
        <app-select-component
          ref="appSelectComponent"
          v-model="modelConfig.formData.appId"
          :is-show-all="false"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="network">
        <ad-platform-select
          v-model="modelConfig.formData.network"
          version="2"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="国家" prop="city">
        <country-select
          v-model="modelConfig.formData.city"
          style="width: 100%"
          :has-all="false"
          :multiple="cityIsMultiple"
        />
      </el-form-item>
      <el-form-item label="首日ROI" prop="threshold">
        <el-input
          type="number"
          v-model.number="modelConfig.formData.threshold"
          placeholder="首日ROI"
          :min="0"
          :step="0.01"
          style="width: 100%"
        />
      </el-form-item>
    </template>
  </page-table>
</template>

<script>
import { roiThresh as request } from '@/api/stat'
import CountrySelect from '@/components/country-select/index.vue'
import { getAppName } from '@/filters'
import { adPlatformList2, countryList } from '@/map/sat'

export default {
  components: { CountrySelect },
  data() {
    return {
      // 国家是否可以多选
      cityIsMultiple: false,
      request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', minWidth: 60 },
          { field: 'id', title: 'ID', minWidth: '150' },
          {
            field: 'appId',
            title: '应用',
            minWidth: '120',
            formatter: ({ row }) => getAppName(row.appId),
          },

          {
            field: 'network',
            title: '渠道',
            minWidth: '120',
            formatter: ({ row }) => adPlatformList2.get(Number(row.network)),
          },
          {
            field: 'city',
            title: '国家',
            minWidth: '120',
            formatter: ({ row }) => this.getCountryName(row.city),
          },
          { field: 'threshold', title: '首日ROI', minWidth: '120' },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            { title: '应用', slots: { default: 'form_appId' } },
            { title: '渠道', slots: { default: 'form_network' } },
            { title: '国家', slots: { default: 'form_city' } },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '350px',
        },
        autoCommit: false,
        adapterResData: res => {
          return {
            ...res,
            network: Number(res.network),
          }
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          appId: '',
          network: '',
          city: '',
          threshold: '',
        },
      },
      selectFormData: {
        appId: '',
        network: '',
        city: '',
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
    }
  },
  methods: {
    modelSubmit({ commit }) {
      const submit = () => {
        this.modelConfig.formData.city.forEach(city => {
          const params = {
            ...this.modelConfig.formData,
            city,
          }
          commit(params)
        })
      }

      if (this.modelConfig.formData.city.length < 2) {
        submit()
        return
      }

      const countryList = this.modelConfig.formData.city.map(it =>
        this.getCountryName(it)
      )
      this.$confirm(
        `此操作将添加多个国家数据【${countryList}】，是否继续?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        submit()
      })
    },
    operateAdd() {
      this.setNotAutoCommitConfig()
    },
    operateEdit() {
      this.setAutoCommitConfig()
    },
    operateCopy() {
      this.setNotAutoCommitConfig()
    },
    setAutoCommitConfig() {
      this.modelConfig.autoCommit = true
      this.cityIsMultiple = false
    },
    setNotAutoCommitConfig() {
      this.$nextTick(() => {
        this.modelConfig.autoCommit = false
        this.cityIsMultiple = true
      })
    },
    getCountryName(cityCode) {
      const res = countryList.find(it => it.country === cityCode)
      return res ? res.country_name : '-'
    },
  },
}
</script>
