<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="短句名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="短句名称"></el-input>
    </el-form-item>
    <el-form-item label="序号" prop="serialNo">
      <el-input v-model="dataForm.serialNo" placeholder="序号"></el-input>
    </el-form-item>
    <el-form-item label="简介" prop="introduction">
      <el-input v-model="dataForm.introduction" placeholder="简介"></el-input>
    </el-form-item>
    <el-form-item label="封面" prop="cover">
      <el-input v-model="dataForm.cover" placeholder="封面"></el-input>
    </el-form-item>
    <el-form-item label="是否生效：1：生效，0：无效" prop="status">
      <el-input v-model="dataForm.status" placeholder="是否生效：1：生效，0：无效"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          name: '',
          serialNo: '',
          introduction: '',
          cover: '',
          status: ''
        },
        dataRule: {
          name: [
            { required: true, message: '短句名称不能为空', trigger: 'blur' }
          ],
          serialNo: [
            { required: true, message: '序号不能为空', trigger: 'blur' }
          ],
          introduction: [
            { required: true, message: '简介不能为空', trigger: 'blur' }
          ],
          cover: [
            { required: true, message: '封面不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '是否生效：1：生效，0：无效不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/playlet/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.name = data.playlet.name
                this.dataForm.serialNo = data.playlet.serialNo
                this.dataForm.introduction = data.playlet.introduction
                this.dataForm.cover = data.playlet.cover
                this.dataForm.status = data.playlet.status
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/playlet/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'name': this.dataForm.name,
                'serialNo': this.dataForm.serialNo,
                'introduction': this.dataForm.introduction,
                'cover': this.dataForm.cover,
                'status': this.dataForm.status
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
