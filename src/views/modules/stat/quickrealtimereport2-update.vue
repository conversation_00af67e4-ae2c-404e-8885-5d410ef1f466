<template>
  <el-dialog
    title="修改"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="500px"
    :modal-append-to-body="false"
    :append-to-body="false"
  >
    <el-form
      :model="dataForm"
      :rules="batch ? {} : dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="110px"
    >
      <el-form-item label="应用" prop="appId" v-if="!batch">
        <app-select-component
          v-model="dataForm.appId"
          :is-show-all="false"
          clearable
        />
      </el-form-item>
      <el-form-item label="策略名称" prop="ruleName" v-if="!batch">
        <el-input
          v-model="dataForm.ruleName"
          placeholder="策略名称"
        />
      </el-form-item>
      <el-form-item label="账户列表" prop="accountIds">
        <el-input
          type="textarea"
          v-model="dataForm.accountIds"
          :disabled="batch"
          placeholder="账户列表"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channel" v-if="!batch">
        <arr-select
          :list="channelList"
          v-model="dataForm.channel"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="品牌" prop="brand" v-if="!batch">
        <arr-select
          :list="brandList"
          v-model="dataForm.brand"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="回传比例" prop="callbackPercent" v-if="!batch">
        <el-input
          type="number"
          v-model="dataForm.callbackPercent"
          placeholder="回传比例 (0-1，1代表100%)"
          :max="1"
          :min="0"
          step="0.01"
        />
      </el-form-item>
      <el-form-item label="代理商" prop="agent" v-if="!batch">
        <arr-select
          :list="agentList"
          v-model="dataForm.agent"
          label-key="label"
          value-key="value"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="投放媒体" prop="unionType" v-if="!batch">
        <arr-select
          :list="unionTypeList"
          v-model="dataForm.unionType"
          label-key="label"
          value-key="value"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="最新日期" prop="day" v-if="!batch">
        <el-date-picker
          v-model="dataForm.day"
          type="date"
          placeholder="选择日期"
          value-format="yyyyMMdd"
          format="yyyy-MM-dd"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="点击数" prop="clickNum" v-if="!batch">
        <el-input
          type="number"
          v-model="dataForm.clickNum"
          placeholder="点击数"
        />
      </el-form-item>
      <el-form-item label="曝光数" prop="exposureNum" v-if="!batch">
        <el-input
          type="number"
          v-model="dataForm.exposureNum"
          placeholder="曝光数"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark" style="margin-bottom: 40px" v-if="!batch">
        <el-input
          type="textarea"
          v-model="dataForm.remark"
          placeholder="备注"
        />
      </el-form-item>

      <!-- 回传条件 -->
      <el-divider content-position="left">回传条件</el-divider>
      <el-form-item label="IPU下限" prop="actIpu">
        <el-input
          type="number"
          v-model="dataForm.actIpu"
          placeholder="激活IPU下限 (>=)"
        />
      </el-form-item>
      <el-form-item label="平均ECPM下限" prop="actAvgEcpm">
        <el-input
          type="number"
          v-model="dataForm.actAvgEcpm"
          placeholder="激活条件平均ECPM下限"
          step="0.01"
        />
        <span style="color: #0bb2d4; margin-left: 10px">
          {{ `(${(dataForm.actAvgEcpm / 100).toFixed(2)}元)` }}
        </span>
      </el-form-item>
      <el-form-item label="ARPU下限" prop="actMinArpu">
        <el-input
          type="number"
          v-model="dataForm.actMinArpu"
          placeholder="激活条件ARPU下限"
          step="0.01"
        />
        <span style="color: #0bb2d4; margin-left: 10px">
          {{ `(${(dataForm.actMinArpu / 100000).toFixed(2)}元)` }}
        </span>
      </el-form-item>
      <el-form-item label="ARPU上限" prop="actMaxArpu">
        <el-input
          type="number"
          v-model="dataForm.actMaxArpu"
          placeholder="激活条件ARPU上限"
          step="0.01"
        />
        <span style="color: #0bb2d4; margin-left: 10px">
          {{ `(${(dataForm.actMaxArpu / 100000).toFixed(2)}元)` }}
        </span>
      </el-form-item>

      <el-form-item label="状态" prop="status" style="margin-top: 40px" v-if="!batch">
        <radio-status v-model="dataForm.status" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { agentList } from '@/map/agent'
import { ctrCallbackRules as request } from '@/api/stat'

export default {
  data() {
    return {
      visible: false,
      batch: true,
      dataForm: {
        id: null,
        appId: '',
        ruleName: '',
        status: 0,
        accountIds: '',
        createdAt: '',
        updatedAt: '',
        createdBy: '',
        updatedBy: '',
        callbackPercent: '',
        remark: '',
        day: '',
        clickNum: '',
        exposureNum: '',
        channel: '',
        brand: '',
        agent: '',
        unionType: '',
        // 回传条件字段
        actIpu: 0,
        actAvgEcpm: 0,
        actMinArpu: 0,
        actMaxArpu: 0,
      },
      dataRule: {
        appId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        ruleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // day: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // clickNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
        channel: [{ required: true, message: '不能为空', trigger: 'blur' }],
        brand: [{ required: true, message: '不能为空', trigger: 'blur' }],
        agent: [{ required: true, message: '不能为空', trigger: 'blur' }],
        unionType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // exposureNum: [
        //   { required: true, message: '不能为空', trigger: 'blur' },
        // ],
        accountIds: [{ required: true, message: '不能为空', trigger: 'blur' }],
        callbackPercent: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === '' || value === null || value === undefined) {
                callback()
                return
              }
              const num = Number(value)
              if (isNaN(num)) {
                callback(new Error('请输入有效的数字'))
              } else if (num < 0 || num > 1) {
                callback(new Error('回传比例必须在0-1之间'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          },
        ],
      },
      channelList: [
        '快手',
        '头条',
        '广点通',
        '百度',
        'oppo信息流',
        'vivo信息流',
        'honor信息流',
        '小米信息流',
        '华为信息流',
        'uc',
      ],
      brandList: ['荣耀', '华为', '小米', 'oppo', 'vivo'],
      agentList: agentList,
      unionTypeList: [
        { label: 'oppo联盟', value: 1 },
        { label: 'oppo站内', value: 2 },
        { label: 'vivo联盟', value: 3 },
        { label: 'vivo站内', value: 4 },
        { label: '快手站内', value: 5 },
        { label: '快手联盟', value: 6 },
        { label: '穿山甲', value: 7 },
        { label: '抖音', value: 8 },
        { label: '优量汇', value: 9 },
        { label: 'uc浏览器', value: 10 },
        { label: '荣耀站内', value: 11 },
        { label: '荣耀联盟', value: 12 },
        { label: '华为站内', value: 13 },
        { label: '华为联盟', value: 14 },
        { label: '小米站内', value: 15 },
        { label: '小米联盟', value: 16 },
        { label: '百度站内', value: 17 },
        { label: '百度联盟', value: 18 },
      ],
    }
  },
  methods: {
    init(accountIds) {
      this.visible = true
      if (!accountIds.includes(',')) {
        this.batch = false
        request.getId({ accountId: accountIds}).then(res => {
          let id = res.data.id
          request.selectItem(id).then(res => {
            this.dataForm = res.__data__
          })
        })
      } else {
        this.dataForm = {
          accountIds: accountIds,
          actIpu: 0,
          actAvgEcpm: 0,
          actMinArpu: 0,
          actMaxArpu: 0,
        }
        this.batch = true
      }
    },
    // 表单提交
    dataFormSubmit() {
      if (this.batch) {
        let data = {
          accountIds: this.dataForm.accountIds,
          actIpu: this.dataForm.actIpu ? Number(this.dataForm.actIpu) : null,
          actAvgEcpm: this.dataForm.actAvgEcpm ? Number(this.dataForm.actAvgEcpm) : null,
          actMinArpu: this.dataForm.actMinArpu ? Number(this.dataForm.actMinArpu) : null,
          actMaxArpu: this.dataForm.actMaxArpu ? Number(this.dataForm.actMaxArpu) : null,
        }
        request.batchUpdate(data).then(res => {
          this.$message.success('操作成功')
          this.visible = false
          this.$emit('refreshDataList')
        })
      } else {
        let data = {
          ...this.dataForm,
          day: Number(this.dataForm.day),
          actIpu: this.dataForm.actIpu ? Number(this.dataForm.actIpu) : null,
          actAvgEcpm: this.dataForm.actAvgEcpm ? Number(this.dataForm.actAvgEcpm) : null,
          actMinArpu: this.dataForm.actMinArpu ? Number(this.dataForm.actMinArpu) : null,
          actMaxArpu: this.dataForm.actMaxArpu ? Number(this.dataForm.actMaxArpu) : null,
        }
        request.insertOrUpdate(this.dataForm.id, data).then(res => {
          this.$message.success('操作成功')
          this.visible = false
          this.$emit('refreshDataList')
        })
      }
    },
  },
}
</script>
