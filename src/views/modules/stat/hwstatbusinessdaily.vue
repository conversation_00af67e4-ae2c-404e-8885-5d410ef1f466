<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="['select']"
    operate-width="180"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appId"
        :is-show-all="false"
        clearable
      />
    </template>
    <template #form_day>
      <el-date-picker
        v-model="selectFormData.day"
        type="date"
        placeholder="选择日期"
        value-format="yyyyMMdd"
      />
    </template>
    <template #form_country>
      <country-select v-model="selectFormData.country" />
    </template>

    <template #table_item_appId="{row}">
      <color-tag :id="row.appCode">{{ row.appId | getAppName }}</color-tag>
    </template>
    <template #table_item_day="{row}">
      <span>{{ row.day | formatDate }}</span>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>
  </page-table>
</template>

<script>
import { hwStatBusinessDailyRequest as request } from '@/api/stat'
import CountrySelect from '@/components/country-select'

export default {
  components: {
    CountrySelect,
  },
  data() {
    return {
      fishList: [],
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'appId',
            title: '应用',
            minWidth: 120,
            slots: { default: 'table_item_appId' },
          },
          {
            field: 'day',
            title: '日期',
            minWidth: 100,
            slots: { default: 'table_item_day' },
          },
          { field: 'groupId', title: '主体', minWidth: 60 },
          { field: 'country', title: '国家', minWidth: 60 },
          { field: 'cpi', title: 'cpi', minWidth: 60 },
          { field: 'newCount', title: '新增统计', minWidth: 70 },
          { field: 'aliveCount', title: '活跃统计', minWidth: 70 },
          {
            field: 'newArriveFrontCount',
            title: '新用户首页到达统计',
            minWidth: 130,
          },
          // {
          //   field: 'eventCnt',
          //   title: '投放平台转化数',
          //   minWidth: 110,
          // },
          {
            field: 'newArriveFrontRate',
            title: '新用户首页到达率',
            minWidth: 120,
          },
          {
            field: 'aliveArriveFrontCount',
            title: '活跃用户首页到达统计',
            minWidth: 140,
          },
          {
            field: 'aliveArriveFrontRate',
            title: '活跃用户首页达到率',
            minWidth: 140,
          },
          // {
          //   field: 'externalSceneCount',
          //   title: '外部场景触发人数',
          //   minWidth: 140,
          // // },
          // {
          //   field: 'externalSceneRate',
          //   title: '外部场景触发率',
          //   minWidth: 140,
          // },
          {
            field: 'pangleIncome',
            title: 'pangle收入',
            minWidth: 90,
          },
          {
            field: 'mintIncome',
            title: 'mint收入',
            minWidth: 90,
          },
          {
            field: 'unityIncome',
            title: 'unity收入',
            minWidth: 90,
          },
          {
            field: 'toponIncome',
            title: 'topon收入',
            minWidth: 90,
          },

          {
            field: 'adImpressions',
            title: '广告展示量',
            minWidth: 100,
          },
          {
            field: 'pangleImpressions',
            title: 'pangle广告展示量',
            minWidth: 140,
          },
          {
            field: 'mintImpressions',
            title: 'mint广告展示量',
            minWidth: 140,
          },
          {
            field: 'unityImpressions',
            title: 'unity广告展示量',
            minWidth: 140,
          },
          {
            field: 'toponImpressions',
            title: 'topon广告展示量',
            minWidth: 140,
          },
          {
            field: 'aiPu',
            title: 'aiPu',
            minWidth: 60,
          },
          {
            field: 'arPu',
            title: 'arPu',
            minWidth: 60,
          },
          {
            field: 'ecpm',
            title: 'ecpm',
            minWidth: 60,
          },
          {
            field: 'roi',
            title: 'roi',
            minWidth: 60,
          },
          {
            field: 'cpaByNew',
            title: 'cpa按数据库新增计算',
            minWidth: 140,
          },

          // {
          //   field: 'fbCost',
          //   title: 'fb成本',
          //   minWidth: 140,
          // },
          // {
          //   field: 'ggCost',
          //   title: 'gg成本',
          //   minWidth: 140,
          // },
          // {
          //   field: 'ttCost',
          //   title: 'tt成本',
          //   minWidth: 140,
          // },
          // {
          //   field: 'mintCost',
          //   title: 'mint成本',
          //   minWidth: 140,
          // },

          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          { field: 'updatedAt', title: '更新时间', minWidth: 150 },
          {
            field: 'cost',
            title: '成本',
            minWidth: 80,
            fixed: 'right',
          },
          {
            field: 'totalRevenue',
            title: '总收入',
            minWidth: 60,
            fixed: 'right',
          },
          {
            title: '毛利',
            minWidth: 80,
            formatter: ({ row }) =>
              ((row.totalRevenue || 0) - (row.cost || 0)).toFixed(2),
            fixed: 'right',
          },
        ],
        formConfig: {
          items: [
            { title: '应用', slots: { default: 'form_appCode' } },
            { title: '日器', slots: { default: 'form_day' } },
            { title: '国家', slots: { default: 'form_country' } },
          ],
        },
      },
      modelConfig: {
        modelConfig: { width: '500px' },
        formConfig: { labelWidth: '140px' },
      },
      selectFormData: { appId: null, day: null, country: null },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
    }
  },
}
</script>
