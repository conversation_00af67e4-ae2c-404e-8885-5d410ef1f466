<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="requestCollection"
    :features="['select']"
    :show-operate="false"
    :before-select="beforeSelect"
    operate-width="0"
  >
    <template #form_appCode>
      <app-select-component
        v-model="appCode"
        :is-show-all="false"
        @change="getAdvertiserIdList"
        clearable
      />
    </template>

    <template #form_advertise_id_list>
      <arr-select
        v-model="advertiseId"
        :list="advertiseIdList"
        label-key="advertiser_id"
        value-key="advertiser_id"
        clearable
        filterable
      />
    </template>
    <template #form_range_time>
      <el-date-picker
        v-model="rangeTime"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
      ></el-date-picker>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
  </page-table>
</template>

<script>
import { statOceanPromotionRequest as requestCollection } from '@/api/stat'
import dayjs from '@/dayjs'

export default {
  components: {},

  data() {
    return {
      requestCollection,
      gridOptions: {
        columns: [
          { type: 'seq', title: '序号', width: 60 },
          { field: 'advertiseId', title: '投放账户' },
          { field: 'cost', title: '消耗' },
          { field: 'dnu', title: '激活' },
          { field: 'activateCount', title: '回传激活', width: 80 },
          { field: 'addictionCount', title: '关键行为', width: 80 },
          { field: 'activateRate', title: '激活回传率', width: 80 },
          { field: 'addictionRate', title: '回传关键行为率', width: 120 },
          { field: 'realAddictionRate', title: '实际关键行为率', width: 120 },
          { field: 'dnuCost', title: '激活成本', width: 80 },
          { field: 'activateCost', title: '回传激活成本', width: 120 },
          { field: 'addictionCost', title: '关键行为成本', width: 120 },
          { field: 'arpu', title: 'arpu', width: 80 },
          { field: 'roi', title: 'roi', width: 80 },
          { field: 'createdAt', title: '时间' },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'advertiseId',
            //   title: '投放账户',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              title: '投放账户',
              slots: {
                default: 'form_advertise_id_list',
              },
            },
            {
              title: '日期范围',
              slots: {
                default: 'form_range_time',
              },
            },
          ],
        },
      },
      beforeSelect: () => {
        if (this.rangeTime) {
          const format = 'YYYYMMDD'
          const startTime = this.rangeTime[0]
            ? dayjs(this.rangeTime[0]).format(format)
            : ''
          const endTime = this.rangeTime[1]
            ? dayjs(this.rangeTime[1]).format(format)
            : ''

          return {
            appCode: this.appCode,
            advertiseId: this.advertiseId,
            startTime: startTime,
            endTime: endTime,
          }
        }

        return {
          advertiseId: this.advertiseId,
          appCode: this.appCode,
        }
      },
      rangeTime: [],
      advertiseIdList: [],
      advertiseId: '',
      appCode: '',
    }
  },
  activated() {},
  methods: {
    getAdvertiserIdList() {
      this.requestCollection.getAdvertiserIdList(this.appCode).then(res => {
        if (res.code === 0) {
          this.advertiseIdList = res.data
        }
      })
    },
  },
}
</script>
