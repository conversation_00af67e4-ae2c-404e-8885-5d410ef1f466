<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    operate-width="180"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appId"
        :is-show-all="false"
        clearable
      />
    </template>
    <template #form_day>
      <el-date-picker
        v-model="selectFormData.day"
        type="date"
        placeholder="选择日期"
        value-format="yyyyMMdd"
      />
    </template>
    <template #form_country>
      <country-select v-model="selectFormData.country" />
    </template>

    <template #table_item_appCode="{row}">
      <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
    </template>
    <template #table_item_day="{row}">
      <span>{{ row.day | formatDate }}</span>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>
  </page-table>
</template>

<script>
import { hwUserReturnDailyRequest as request } from '@/api/stat'
import CountrySelect from '@/components/country-select'

export default {
  components: {
    CountrySelect,
  },
  data() {
    return {
      fishList: [],
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 120,
            slots: {
              default: 'table_item_appCode',
            },
          },
          {
            field: 'day',
            title: '日期',
            minWidth: 100,
            slots: {
              default: 'table_item_day',
            },
          },
          // { field: 'dt', title: 'dt', minWidth: 60 },
          { field: 'country', title: '国家', minWidth: 60 },
          { field: 'countryName', title: '国家中文名称', minWidth: 120 },
          { field: 'dnu', title: '新增', minWidth: 60 },
          { field: 'impUv', title: '曝光设备数', minWidth: 100 },
          { field: 'impRate', title: '曝光设备比', minWidth: 100 },
          { field: 'ipu', title: 'ipu', minWidth: 60 },
          { field: 't0Arpu', title: 't0_arpu', minWidth: 100 },
          { field: 'avgEcpm', title: 'avg_ecpm', minWidth: 100 },
          { field: 't1Arpu', title: '截止t1累计arpu', minWidth: 120 },
          { field: 't2Arpu', title: '截止t2累计arpu', minWidth: 120 },
          { field: 'appDay2', title: '次留', minWidth: 60 },
          { field: 'appDay3', title: '3留', minWidth: 60 },
          { field: 't0Income', title: '预估收入', minWidth: 100 },
          { field: 't1Income', title: 't1预估收入', minWidth: 100 },
          { field: 't2Income', title: 't2预估收入', minWidth: 100 },
          { field: 'cost', title: '成本', minWidth: 60 },
          { field: 't0Roi', title: 't0_roi', minWidth: 60 },
          { field: 't1Roi', title: 't1_roi', minWidth: 60 },
          { field: 't2Roi', title: 't2_roi', minWidth: 60 },

          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          { field: 'updatedAt', title: '更新时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              title: '日器',
              slots: {
                default: 'form_day',
              },
            },
            {
              title: '国家',
              slots: {
                default: 'form_country',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
      },
      selectFormData: {
        appId: null,
        day: null,
        country: null,
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
    }
  },
}
</script>
