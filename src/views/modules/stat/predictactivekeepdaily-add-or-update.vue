<template>
  <el-dialog
    class="adaptive_dialog"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    @closed="$emit('closed')"
    width="980px"
  >
    <el-form
      class="custom-form"
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用" prop="appCode">
            <app-select-component
              v-model.trim="dataForm.appCode"
              @change-app="changeApp"
              clearable
              :is-show-all="false"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据源填充">
            <app-select-component
              v-model.trim="dataSourceAppCode"
              @change-app="changeDataSource"
              clearable
              :is-show-all="false"
            />
          </el-form-item>
        </el-col>
        <!--<el-col :span="12">-->
        <!--  <el-form-item label="日期" prop="day">-->
        <!--    <el-date-picker-->
        <!--      v-model.trim.trim="dataForm.day"-->
        <!--      type="date"-->
        <!--      placeholder="选择日期"-->
        <!--      value-format="yyyyMMdd"-->
        <!--      :picker-options="pickerOptions"-->
        <!--    />-->
        <!--  </el-form-item>-->
        <!--</el-col>-->
      </el-row>
      <el-row
        v-for="item in Math.ceil(keepEnd / 2)"
        :key="item"
        :gutter="30"
        style="margin-bottom: 20px;"
      >
        <el-col :span="12">
          <el-row :gutter="10">
            <el-card class="box-card">
              <div slot="header">
                <b>{{ item * 2 === 2 ? '次' : item * 2 }}日</b>
              </div>
              <el-col :span="colSpan">
                <el-form-item :label="`留存衰减率`" :prop="`keep${item * 2}`">
                  <el-input
                    v-model.trim="dataForm[`keep${item * 2}`]"
                    type="number"
                    :placeholder="`留存衰减率`"
                    :min="0"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="colSpan">
                <el-form-item :label="`arpu衰减率`" :prop="`arpu${item * 2}`">
                  <el-input
                    v-model.trim="dataForm[`arpu${item * 2}`]"
                    type="number"
                    :placeholder="`arpu衰减率`"
                    :min="0"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="colSpan" v-if="showLtvItem">
                <el-form-item :label="`提现比例`" :prop="`ltv${item * 2}`">
                  <el-input
                    v-model.trim="dataForm[`ltv${item * 2}`]"
                    type="number"
                    :placeholder="`提现比例`"
                    :min="0"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-card>
          </el-row>
        </el-col>

        <el-col v-if="item * 2 + 1 < keepEnd" :span="12">
          <el-row :gutter="10">
            <el-card>
              <div slot="header">
                <b>{{ item * 2 + 1 }}日</b>
              </div>
              <el-col :span="colSpan">
                <el-form-item
                  :label="`留存衰减率`"
                  :prop="`keep${item * 2 + 1}`"
                >
                  <el-input
                    v-model.trim="dataForm[`keep${item * 2 + 1}`]"
                    :placeholder="`留存衰减率`"
                    type="number"
                    :min="0"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="colSpan">
                <el-form-item
                  :label="`arpu衰减率`"
                  :prop="`arpu${item * 2 + 1}`"
                >
                  <el-input
                    v-model.trim="dataForm[`arpu${item * 2 + 1}`]"
                    :placeholder="`arpu衰减率`"
                    type="number"
                    :min="0"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>

              <el-col :span="colSpan" v-if="showLtvItem">
                <el-form-item
                  :label="`提现比例`"
                  :prop="`ltv${item * 2 + 1}`"
                  :min="0"
                >
                  <el-input
                    v-model.trim="dataForm[`ltv${item * 2 + 1}`]"
                    :placeholder="`提现比例`"
                    type="number"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-card>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit" :loading="loading">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import AppSelectComponent from '@/components/app-select-component'

const rule = [
  {
    validator: (_, value, callback) => {
      const number = Number(value)
      if (!value) {
        return callback(new Error('必填'))
      }
      if (Number.isNaN(number)) {
        return callback(new Error('只能为数字'))
      }
      number > 0 && number < 100
        ? callback()
        : callback(new Error('必须大于0或者小于100'))
    },
  },
]

const keepStart = 2
const keepEnd = 30

const keepList = {}
const arpuList = {}
const ltvList = {
  ltv1: 0,
}

const keepRule = {}
const arpuRule = {}
const ltvRule = {}

for (let i = keepStart; i <= keepEnd; i++) {
  const keyKeep = `keep${i}`
  const keyArpu = `arpu${i}`
  const keyLtv = `ltv${i}`
  keepList[keyKeep] = ''
  arpuList[keyArpu] = ''
  ltvList[keyLtv] = ''

  keepRule[keyKeep] = rule
  arpuRule[keyArpu] = rule
  ltvRule[keyLtv] = rule
}

export default {
  components: {
    AppSelectComponent,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: undefined,
        appCode: '',
        groupId: '',
        roi: '',
        // day: '',
        newAddNums: '',
        activeNums: '',
        ...keepList,
        ...arpuList,
        ...ltvList,
      },
      dataRule: {
        appCode: [{ required: true, message: '不能为空', trigger: 'blur' }],
        groupId: [{ required: true, message: '主体不能为空', trigger: 'blur' }],
        day: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        ...keepRule,
        ...arpuRule,
        ...ltvRule,
      },
      keepStart,
      keepEnd,
      pickerOptions: {
        disabledDate(time) {
          return Date.now() <= time.getTime()
        },
      },
      appGenre: '',
      loading: false,
      dataFormAppName: '',
      dataSourceAppCode: '',
      dataSourceAppName: '',
    }
  },
  computed: {
    showLtvItem() {
      return this.appGenre === 5
    },
    colSpan() {
      return this.showLtvItem ? 8 : 12
    },
  },
  methods: {
    init(id, appCode) {
      this.dataForm.id = id || undefined
      this.dataForm.appCode = appCode || undefined
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        // this.getListItem()
        // this.getTemplateData()
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(async valid => {
        if (valid) {
          const data = { ...this.dataForm }
          for (let i = this.keepStart; i <= this.keepEnd; i++) {
            const keyKeep = `keep${i}`
            const keyArpu = `arpu${i}`
            const keyLtv = `ltv${i}`
            data[keyKeep] = Number((data[keyKeep] / 100).toFixed(4))
            data[keyArpu] = Number((data[keyArpu] / 100).toFixed(4))
            data[keyLtv] = Number((data[keyLtv] / 100).toFixed(4))
          }
          // ltv必须有1
          data['ltv1'] = 0

          console.log(this.dataForm)

          try {
            await this.$confirm(
              `<p>修改的APP为：<strong>${this.dataFormAppName}</strong>/${this.dataForm.appCode}</p>
              <p>数据源来自：<strong>${this.dataSourceAppName}</strong>/${this.dataSourceAppCode}</p>
              `,
              '注意核对',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true,
              }
            )
          } catch (e) {
            return
          }

          this.loading = true
          this.$http({
            url: this.$http.adornUrl(
              `/stat/predictactivekeepdaily/save_predict_keep`
            ),
            method: 'post',
            data: this.$http.adornData(data),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                })
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    changeDataSource(app) {
      if (app) {
        const { code, name } = app
        this.getTemplateData(code)
        this.dataSourceAppName = name
      }
    },
    changeApp(app) {
      console.log(app)
      if (app) {
        const { groupId, genre, name } = app
        this.dataForm.groupId = groupId
        this.dataFormAppName = name
        this.getTemplateData()
        this.appGenre = genre
      }
      console.log('groupId => ', this.dataForm.groupId)
    },
    getListItem() {
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(
            `/stat/predictactivekeepdaily/info/${this.dataForm.id}`
          ),
          method: 'get',
          params: this.$http.adornParams(),
        }).then(({ data }) => {
          this.handleRes(data)
        })
      }
    },
    getTemplateData(appCode = this.dataForm.appCode) {
      this.$http({
        url: this.$http.adornUrl(
          `/stat/predictactivekeepdaily/template/${appCode}`
        ),
        method: 'get',
        params: this.$http.adornParams(),
      }).then(({ data }) => {
        this.handleRes(data)
      })
    },
    handleRes(data) {
      const handleNoData = () => {
        for (const key in this.dataForm) {
          if (key !== 'appCode' && key !== 'groupId') {
            this.dataForm[key] = ''
          }
        }
      }

      if (data && data.code === 0) {
        if (data.predictActiveKeepDaily) {
          for (let i = keepStart; i <= keepEnd; i++) {
            // 直接乘以100数字会不准确
            this.dataForm[`keep${i}`] = Number(
              ((data.predictActiveKeepDaily[`keep${i}`] * 1000) / 10).toFixed(2)
            )
            // 直接乘以100数字会不准确
            this.dataForm[`arpu${i}`] = Number(
              ((data.predictActiveKeepDaily[`arpu${i}`] * 1000) / 10).toFixed(2)
            )
            // 直接乘以100数字会不准确
            this.dataForm[`ltv${i}`] = Number(
              ((data.predictActiveKeepDaily[`ltv${i}`] * 1000) / 10).toFixed(2)
            )
          }
          if (data.predictActiveKeepDaily.appCode) {
            this.dataForm.appCode = data.predictActiveKeepDaily.appCode
          }
          // this.dataForm.groupId = data.predictActiveKeepDaily.groupId
          this.dataForm.roi = data.predictActiveKeepDaily.roi
          // this.dataForm.day = data.predictActiveKeepDaily.day.toString()
          this.dataForm.newAddNums = data.predictActiveKeepDaily.newAddNums
          this.dataForm.activeNums = data.predictActiveKeepDaily.activeNums
        } else {
          handleNoData()
        }
      } else {
        handleNoData()
      }
    },
  },
}
</script>

<style scoped lang="scss">
.custom-form {
  &::v-deep {
    .el-form-item__label {
      padding: 0;
    }

    .el-form-item__error {
      margin-top: 5px;
    }

    .el-form-item {
      margin-bottom: 20px;
    }
  }
}
</style>
