<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '查看'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="1000px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      disabled
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="系统应用code" prop="appCode">
            <el-input v-model="dataForm.appCode" placeholder="系统应用code" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="系统应用code" prop="appCode">
            <el-input v-model="dataForm.appCode" placeholder="系统应用code" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开始时间" prop="startDate">
            <el-input v-model="dataForm.startDate" placeholder="开始时间" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="结束时间" prop="endDate">
            <el-input v-model="dataForm.endDate" placeholder="结束时间" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预估收益" prop="revenue">
            <el-input v-model="dataForm.revenue" placeholder="预估收益" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="点击量" prop="clkCnt">
            <el-input v-model="dataForm.clkCnt" placeholder="点击量" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="点击率" prop="clkRate">
            <el-input v-model="dataForm.clkRate" placeholder="点击率" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Ecpm" prop="ecpm">
            <el-input v-model="dataForm.ecpm" placeholder="Ecpm" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="展现量" prop="ipmCnt">
            <el-input v-model="dataForm.ipmCnt" placeholder="展现量" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="应用名称" prop="siteName">
            <el-input v-model="dataForm.siteName" placeholder="应用名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="应用id" prop="siteId">
            <el-input v-model="dataForm.siteId" placeholder="应用id" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="Currency" prop="currency">
            <el-input v-model="dataForm.currency" placeholder="Currency" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="系统类型" prop="os">
            <el-input v-model="dataForm.os" placeholder="系统类型" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="CodeName" prop="codeName">
            <el-input v-model="dataForm.codeName" placeholder="CodeName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="CodeId" prop="codeId">
            <el-input v-model="dataForm.codeId" placeholder="CodeId" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="CodeType" prop="codeType">
            <el-input v-model="dataForm.codeType" placeholder="CodeType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="BiddingType" prop="biddingType">
            <el-input
              v-model="dataForm.biddingType"
              placeholder="BiddingType"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数值日期" prop="day">
            <el-input v-model="dataForm.day" placeholder="数值日期" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="创建日期" prop="createdAt">
            <el-input v-model="dataForm.createdAt" placeholder="创建日期" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--<span slot="footer" class="dialog-footer">-->
    <!--  <el-button @click="visible = false">取消</el-button>-->
    <!--  <el-button type="primary" @click="dataFormSubmit()">确定</el-button>-->
    <!--</span>-->
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appCode: '',
        startDate: '',
        endDate: '',
        revenue: '',
        clkCnt: '',
        clkRate: '',
        ecpm: '',
        ipmCnt: '',
        siteName: '',
        siteId: '',
        currency: '',
        os: '',
        codeName: '',
        codeId: '',
        codeType: '',
        biddingType: '',
        day: '',
        createdAt: '',
      },
      dataRule: {
        appCode: [
          { required: true, message: '系统应用code不能为空', trigger: 'blur' },
        ],
        startDate: [
          { required: true, message: '开始时间不能为空', trigger: 'blur' },
        ],
        endDate: [
          { required: true, message: '结束时间不能为空', trigger: 'blur' },
        ],
        revenue: [
          { required: true, message: '预估收益不能为空', trigger: 'blur' },
        ],
        clkCnt: [
          { required: true, message: '点击量不能为空', trigger: 'blur' },
        ],
        clkRate: [
          { required: true, message: '点击率不能为空', trigger: 'blur' },
        ],
        ecpm: [{ required: true, message: 'Ecpm不能为空', trigger: 'blur' }],
        ipmCnt: [
          { required: true, message: '展现量不能为空', trigger: 'blur' },
        ],
        siteName: [
          { required: true, message: '应用名称不能为空', trigger: 'blur' },
        ],
        siteId: [
          { required: true, message: '应用id不能为空', trigger: 'blur' },
        ],
        currency: [
          { required: true, message: 'Currency不能为空', trigger: 'blur' },
        ],
        os: [{ required: true, message: '系统类型不能为空', trigger: 'blur' }],
        codeName: [
          { required: true, message: 'CodeName不能为空', trigger: 'blur' },
        ],
        codeId: [
          { required: true, message: 'CodeId不能为空', trigger: 'blur' },
        ],
        codeType: [
          { required: true, message: 'CodeType不能为空', trigger: 'blur' },
        ],
        biddingType: [
          { required: true, message: 'BiddingType不能为空', trigger: 'blur' },
        ],
        day: [{ required: true, message: '数值日期不能为空', trigger: 'blur' }],
        createdAt: [
          { required: true, message: '创建日期不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/panglereportdaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appCode = data.pangleReportDaily.appCode
              this.dataForm.startDate = data.pangleReportDaily.startDate
              this.dataForm.endDate = data.pangleReportDaily.endDate
              this.dataForm.revenue = data.pangleReportDaily.revenue
              this.dataForm.clkCnt = data.pangleReportDaily.clkCnt
              this.dataForm.clkRate = data.pangleReportDaily.clkRate
              this.dataForm.ecpm = data.pangleReportDaily.ecpm
              this.dataForm.ipmCnt = data.pangleReportDaily.ipmCnt
              this.dataForm.siteName = data.pangleReportDaily.siteName
              this.dataForm.siteId = data.pangleReportDaily.siteId
              this.dataForm.currency = data.pangleReportDaily.currency
              this.dataForm.os = data.pangleReportDaily.os
              this.dataForm.codeName = data.pangleReportDaily.codeName
              this.dataForm.codeId = data.pangleReportDaily.codeId
              this.dataForm.codeType = data.pangleReportDaily.codeType
              this.dataForm.biddingType = data.pangleReportDaily.biddingType
              this.dataForm.day = data.pangleReportDaily.day
              this.dataForm.createdAt = data.pangleReportDaily.createdAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/panglereportdaily/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appCode: this.dataForm.appCode,
              startDate: this.dataForm.startDate,
              endDate: this.dataForm.endDate,
              revenue: this.dataForm.revenue,
              clkCnt: this.dataForm.clkCnt,
              clkRate: this.dataForm.clkRate,
              ecpm: this.dataForm.ecpm,
              ipmCnt: this.dataForm.ipmCnt,
              siteName: this.dataForm.siteName,
              siteId: this.dataForm.siteId,
              currency: this.dataForm.currency,
              os: this.dataForm.os,
              codeName: this.dataForm.codeName,
              codeId: this.dataForm.codeId,
              codeType: this.dataForm.codeType,
              biddingType: this.dataForm.biddingType,
              day: this.dataForm.day,
              createdAt: this.dataForm.createdAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
