<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="开发者账号" prop="memberId">
      <el-input v-model="dataForm.memberId" placeholder="开发者账号"></el-input>
    </el-form-item>
    <el-form-item label="	媒体名称" prop="mediumName">
      <el-input v-model="dataForm.mediumName" placeholder="	媒体名称"></el-input>
    </el-form-item>
    <el-form-item label="媒体id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="媒体id"></el-input>
    </el-form-item>
    <el-form-item label="广告位ID" prop="placementId">
      <el-input v-model="dataForm.placementId" placeholder="广告位ID"></el-input>
    </el-form-item>
    <el-form-item label="广告位名称" prop="placementName">
      <el-input v-model="dataForm.placementName" placeholder="广告位名称"></el-input>
    </el-form-item>
    <el-form-item label="广告位类型" prop="placementType">
      <el-input v-model="dataForm.placementType" placeholder="广告位类型"></el-input>
    </el-form-item>
    <el-form-item label="	报表日期，yyyy-MM-dd" prop="date">
      <el-input v-model="dataForm.date" placeholder="	报表日期，yyyy-MM-dd"></el-input>
    </el-form-item>
    <el-form-item label="报表日期，yyyyMMdd" prop="reportDate">
      <el-input v-model="dataForm.reportDate" placeholder="报表日期，yyyyMMdd"></el-input>
    </el-form-item>
    <el-form-item label="是否是summary" prop="isSummary">
      <el-input v-model="dataForm.isSummary" placeholder="是否是summary"></el-input>
    </el-form-item>
    <el-form-item label="广告位请求量" prop="requestCount">
      <el-input v-model="dataForm.requestCount" placeholder="广告位请求量"></el-input>
    </el-form-item>
    <el-form-item label="广告位返回量" prop="returnCount">
      <el-input v-model="dataForm.returnCount" placeholder="广告位返回量"></el-input>
    </el-form-item>
    <el-form-item label="广告请求量" prop="adRequestCount">
      <el-input v-model="dataForm.adRequestCount" placeholder="广告请求量"></el-input>
    </el-form-item>
    <el-form-item label="广告返回量" prop="adReturnCount">
      <el-input v-model="dataForm.adReturnCount" placeholder="广告返回量"></el-input>
    </el-form-item>
    <el-form-item label="曝光量" prop="pv">
      <el-input v-model="dataForm.pv" placeholder="曝光量"></el-input>
    </el-form-item>
    <el-form-item label="点击量" prop="click">
      <el-input v-model="dataForm.click" placeholder="点击量"></el-input>
    </el-form-item>
    <el-form-item label="广告位填充率 (广告位返回量 / 广告位请求量 * 100%)" prop="fillRate">
      <el-input v-model="dataForm.fillRate" placeholder="广告位填充率 (广告位返回量 / 广告位请求量 * 100%)"></el-input>
    </el-form-item>
    <el-form-item label="广告位曝光率 (曝光量/广告位返回量 * 100%)" prop="exposureRate">
      <el-input v-model="dataForm.exposureRate" placeholder="广告位曝光率 (曝光量/广告位返回量 * 100%)"></el-input>
    </el-form-item>
    <el-form-item label="广告填充率 (广告返回量/广告请求量 * 100%)" prop="adFillRate">
      <el-input v-model="dataForm.adFillRate" placeholder="广告填充率 (广告返回量/广告请求量 * 100%)"></el-input>
    </el-form-item>
    <el-form-item label="广告曝光率 (曝光量 / 广告返回量 * 100%)" prop="adExposureRate">
      <el-input v-model="dataForm.adExposureRate" placeholder="广告曝光率 (曝光量 / 广告返回量 * 100%)"></el-input>
    </el-form-item>
    <el-form-item label="点击率 (点击量 / 曝光量 * 100%)" prop="clickRate">
      <el-input v-model="dataForm.clickRate" placeholder="点击率 (点击量 / 曝光量 * 100%)"></el-input>
    </el-form-item>
    <el-form-item label="收入 (单位：元)" prop="revenue">
      <el-input v-model="dataForm.revenue" placeholder="收入 (单位：元)"></el-input>
    </el-form-item>
    <el-form-item label="千次展示收入 (收入 / 曝光量 * 1000) (单位：元)" prop="ecpm">
      <el-input v-model="dataForm.ecpm" placeholder="千次展示收入 (收入 / 曝光量 * 1000) (单位：元)"></el-input>
    </el-form-item>
    <el-form-item label="点击成本 (收入 / 点击量) (单位：元)" prop="cpc">
      <el-input v-model="dataForm.cpc" placeholder="点击成本 (收入 / 点击量) (单位：元)"></el-input>
    </el-form-item>
    <el-form-item label="价格策略类型" prop="priceStrategyType">
      <el-input v-model="dataForm.priceStrategyType" placeholder="价格策略类型"></el-input>
    </el-form-item>
    <el-form-item label="设价" prop="price">
      <el-input v-model="dataForm.price" placeholder="设价"></el-input>
    </el-form-item>
    <el-form-item label="分层标签" prop="priceLevel">
      <el-input v-model="dataForm.priceLevel" placeholder="分层标签"></el-input>
    </el-form-item>
    <el-form-item label="收入 (单位：美元)" prop="revenueUsd">
      <el-input v-model="dataForm.revenueUsd" placeholder="收入 (单位：美元)"></el-input>
    </el-form-item>
    <el-form-item label="千次展示收入 (收入 / 曝光量 * 1000) (单位：美元)" prop="ecpmUsd">
      <el-input v-model="dataForm.ecpmUsd" placeholder="千次展示收入 (收入 / 曝光量 * 1000) (单位：美元)"></el-input>
    </el-form-item>
    <el-form-item label="点击成本 (收入 / 点击量) (单位：美元)" prop="cpcUsd">
      <el-input v-model="dataForm.cpcUsd" placeholder="点击成本 (收入 / 点击量) (单位：美元)"></el-input>
    </el-form-item>
    <el-form-item label="美元/人民币汇率中间价(天级别更新)" prop="usdExchangeRate">
      <el-input v-model="dataForm.usdExchangeRate" placeholder="美元/人民币汇率中间价(天级别更新)"></el-input>
    </el-form-item>
    <el-form-item label="创建日期" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建日期"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          memberId: '',
          mediumName: '',
          appId: '',
          placementId: '',
          placementName: '',
          placementType: '',
          date: '',
          reportDate: '',
          isSummary: '',
          requestCount: '',
          returnCount: '',
          adRequestCount: '',
          adReturnCount: '',
          pv: '',
          click: '',
          fillRate: '',
          exposureRate: '',
          adFillRate: '',
          adExposureRate: '',
          clickRate: '',
          revenue: '',
          ecpm: '',
          cpc: '',
          priceStrategyType: '',
          price: '',
          priceLevel: '',
          revenueUsd: '',
          ecpmUsd: '',
          cpcUsd: '',
          usdExchangeRate: '',
          createdAt: ''
        },
        dataRule: {
          memberId: [
            { required: true, message: '开发者账号不能为空', trigger: 'blur' }
          ],
          mediumName: [
            { required: true, message: '	媒体名称不能为空', trigger: 'blur' }
          ],
          appId: [
            { required: true, message: '媒体id不能为空', trigger: 'blur' }
          ],
          placementId: [
            { required: true, message: '广告位ID不能为空', trigger: 'blur' }
          ],
          placementName: [
            { required: true, message: '广告位名称不能为空', trigger: 'blur' }
          ],
          placementType: [
            { required: true, message: '广告位类型不能为空', trigger: 'blur' }
          ],
          date: [
            { required: true, message: '	报表日期，yyyy-MM-dd不能为空', trigger: 'blur' }
          ],
          reportDate: [
            { required: true, message: '报表日期，yyyyMMdd不能为空', trigger: 'blur' }
          ],
          isSummary: [
            { required: true, message: '是否是summary不能为空', trigger: 'blur' }
          ],
          requestCount: [
            { required: true, message: '广告位请求量不能为空', trigger: 'blur' }
          ],
          returnCount: [
            { required: true, message: '广告位返回量不能为空', trigger: 'blur' }
          ],
          adRequestCount: [
            { required: true, message: '广告请求量不能为空', trigger: 'blur' }
          ],
          adReturnCount: [
            { required: true, message: '广告返回量不能为空', trigger: 'blur' }
          ],
          pv: [
            { required: true, message: '曝光量不能为空', trigger: 'blur' }
          ],
          click: [
            { required: true, message: '点击量不能为空', trigger: 'blur' }
          ],
          fillRate: [
            { required: true, message: '广告位填充率 (广告位返回量 / 广告位请求量 * 100%)不能为空', trigger: 'blur' }
          ],
          exposureRate: [
            { required: true, message: '广告位曝光率 (曝光量/广告位返回量 * 100%)不能为空', trigger: 'blur' }
          ],
          adFillRate: [
            { required: true, message: '广告填充率 (广告返回量/广告请求量 * 100%)不能为空', trigger: 'blur' }
          ],
          adExposureRate: [
            { required: true, message: '广告曝光率 (曝光量 / 广告返回量 * 100%)不能为空', trigger: 'blur' }
          ],
          clickRate: [
            { required: true, message: '点击率 (点击量 / 曝光量 * 100%)不能为空', trigger: 'blur' }
          ],
          revenue: [
            { required: true, message: '收入 (单位：元)不能为空', trigger: 'blur' }
          ],
          ecpm: [
            { required: true, message: '千次展示收入 (收入 / 曝光量 * 1000) (单位：元)不能为空', trigger: 'blur' }
          ],
          cpc: [
            { required: true, message: '点击成本 (收入 / 点击量) (单位：元)不能为空', trigger: 'blur' }
          ],
          priceStrategyType: [
            { required: true, message: '价格策略类型不能为空', trigger: 'blur' }
          ],
          price: [
            { required: true, message: '设价不能为空', trigger: 'blur' }
          ],
          priceLevel: [
            { required: true, message: '分层标签不能为空', trigger: 'blur' }
          ],
          revenueUsd: [
            { required: true, message: '收入 (单位：美元)不能为空', trigger: 'blur' }
          ],
          ecpmUsd: [
            { required: true, message: '千次展示收入 (收入 / 曝光量 * 1000) (单位：美元)不能为空', trigger: 'blur' }
          ],
          cpcUsd: [
            { required: true, message: '点击成本 (收入 / 点击量) (单位：美元)不能为空', trigger: 'blur' }
          ],
          usdExchangeRate: [
            { required: true, message: '美元/人民币汇率中间价(天级别更新)不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建日期不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/adnetreportdaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.memberId = data.adnetReportDaily.memberId
                this.dataForm.mediumName = data.adnetReportDaily.mediumName
                this.dataForm.appId = data.adnetReportDaily.appId
                this.dataForm.placementId = data.adnetReportDaily.placementId
                this.dataForm.placementName = data.adnetReportDaily.placementName
                this.dataForm.placementType = data.adnetReportDaily.placementType
                this.dataForm.date = data.adnetReportDaily.date
                this.dataForm.reportDate = data.adnetReportDaily.reportDate
                this.dataForm.isSummary = data.adnetReportDaily.isSummary
                this.dataForm.requestCount = data.adnetReportDaily.requestCount
                this.dataForm.returnCount = data.adnetReportDaily.returnCount
                this.dataForm.adRequestCount = data.adnetReportDaily.adRequestCount
                this.dataForm.adReturnCount = data.adnetReportDaily.adReturnCount
                this.dataForm.pv = data.adnetReportDaily.pv
                this.dataForm.click = data.adnetReportDaily.click
                this.dataForm.fillRate = data.adnetReportDaily.fillRate
                this.dataForm.exposureRate = data.adnetReportDaily.exposureRate
                this.dataForm.adFillRate = data.adnetReportDaily.adFillRate
                this.dataForm.adExposureRate = data.adnetReportDaily.adExposureRate
                this.dataForm.clickRate = data.adnetReportDaily.clickRate
                this.dataForm.revenue = data.adnetReportDaily.revenue
                this.dataForm.ecpm = data.adnetReportDaily.ecpm
                this.dataForm.cpc = data.adnetReportDaily.cpc
                this.dataForm.priceStrategyType = data.adnetReportDaily.priceStrategyType
                this.dataForm.price = data.adnetReportDaily.price
                this.dataForm.priceLevel = data.adnetReportDaily.priceLevel
                this.dataForm.revenueUsd = data.adnetReportDaily.revenueUsd
                this.dataForm.ecpmUsd = data.adnetReportDaily.ecpmUsd
                this.dataForm.cpcUsd = data.adnetReportDaily.cpcUsd
                this.dataForm.usdExchangeRate = data.adnetReportDaily.usdExchangeRate
                this.dataForm.createdAt = data.adnetReportDaily.createdAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/adnetreportdaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'memberId': this.dataForm.memberId,
                'mediumName': this.dataForm.mediumName,
                'appId': this.dataForm.appId,
                'placementId': this.dataForm.placementId,
                'placementName': this.dataForm.placementName,
                'placementType': this.dataForm.placementType,
                'date': this.dataForm.date,
                'reportDate': this.dataForm.reportDate,
                'isSummary': this.dataForm.isSummary,
                'requestCount': this.dataForm.requestCount,
                'returnCount': this.dataForm.returnCount,
                'adRequestCount': this.dataForm.adRequestCount,
                'adReturnCount': this.dataForm.adReturnCount,
                'pv': this.dataForm.pv,
                'click': this.dataForm.click,
                'fillRate': this.dataForm.fillRate,
                'exposureRate': this.dataForm.exposureRate,
                'adFillRate': this.dataForm.adFillRate,
                'adExposureRate': this.dataForm.adExposureRate,
                'clickRate': this.dataForm.clickRate,
                'revenue': this.dataForm.revenue,
                'ecpm': this.dataForm.ecpm,
                'cpc': this.dataForm.cpc,
                'priceStrategyType': this.dataForm.priceStrategyType,
                'price': this.dataForm.price,
                'priceLevel': this.dataForm.priceLevel,
                'revenueUsd': this.dataForm.revenueUsd,
                'ecpmUsd': this.dataForm.ecpmUsd,
                'cpcUsd': this.dataForm.cpcUsd,
                'usdExchangeRate': this.dataForm.usdExchangeRate,
                'createdAt': this.dataForm.createdAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
