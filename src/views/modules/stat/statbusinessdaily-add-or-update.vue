<template>
  <el-dialog
    width="800px"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="170px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="新用户首页到达统计" prop="newArriveFrontCount">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.newArriveFrontCount"
              placeholder="新用户首页到达统计"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="活跃用户首页到达统计"
            prop="aliveArriveFrontCount"
          >
            <el-input-number
              :min="0"
              v-model.trim="dataForm.aliveArriveFrontCount"
              placeholder="活跃用户首页到达统计"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="新用户首页到达率" prop="newArriveFrontRate">
            <el-input
              v-model.trim="dataForm.newArriveFrontRate"
              placeholder="新用户首页到达率"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="活跃用户首页到达率" prop="aliveArriveFrontRate">
            <el-input
              v-model.trim="dataForm.aliveArriveFrontRate"
              placeholder="活跃用户首页到达率"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="外部场景触发人数" prop="externalSceneCount">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.externalSceneCount"
              placeholder="外部场景触发人数"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="外部场景触发率" prop="externalSceneRate">
            <el-input
              v-model.trim="dataForm.externalSceneRate"
              placeholder="外部场景触发率"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成本" prop="cost">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.cost"
              placeholder="成本"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="穿山甲收入" prop="csjIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.csjIncome"
              placeholder="穿山甲收入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优量汇收入" prop="ylhIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.ylhIncome"
              placeholder="优量汇收入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="快手收入" prop="ksIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.ksIncome"
              placeholder="快手收入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="百度收入" prop="baiduIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.baiduIncome"
              placeholder="百度收入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="外部百度资讯收入" prop="externalBaiduInfoIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.externalBaiduInfoIncome"
              placeholder="外部百度资讯收入"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="华为 " prop="huaweiIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.huaweiIncome"
              placeholder="华为 "
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="mint收入" prop="mintIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.mintIncome"
              placeholder="mintIncome"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="yandex收入" prop="yandexIncome">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.yandexIncome"
              placeholder="yandexIncome"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="总收入" prop="totalRevenue">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.totalRevenue"
              placeholder="总收入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="广告展示量" prop="adImpressions">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.adImpressions"
              placeholder="广告展示量"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="锁屏展示量" prop="lockScreenImpressions">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.lockScreenImpressions"
              placeholder="锁屏展示量"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="广告AIPU" prop="aiPu">
            <el-input-number
              :min="0"
              :disabled="!!dataForm.id"
              v-model.trim="dataForm.aiPu"
              placeholder="广告AIPU"
            />
            <!--<el-tag>{{ dataForm.aiPu }}</el-tag>-->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="锁屏AIPU" prop="lockScreenAiPu">
            <el-input-number
              :min="0"
              :disabled="!!dataForm.id"
              v-model.trim="dataForm.lockScreenAiPu"
              placeholder="lockScreenAiPu"
            />
            <!--<el-tag>{{ dataForm.ecpm }}</el-tag>-->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="ARPU" prop="arPu">
            <el-input-number
              :min="0"
              :disabled="!!dataForm.id"
              v-model.trim="dataForm.arPu"
              placeholder="ARPU"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="广告ECPM" prop="ecpm">
            <el-input-number
              :min="0"
              :disabled="!!dataForm.id"
              v-model.trim="dataForm.ecpm"
              placeholder="广告ECPM"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="锁屏ECPM" prop="lockScreenEcpm">
            <el-input-number
              :min="0"
              :disabled="!!dataForm.id"
              v-model.trim="dataForm.lockScreenEcpm"
              placeholder="锁屏ECPM"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="新增CPA" prop="cpaByNew">
            <el-input
              v-model.trim="dataForm.cpaByNew"
              :disabled="!!dataForm.id"
              placeholder="新增CPA"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="激活CPA" prop="cpaByArrive">
            <el-input
              v-model.trim="dataForm.cpaByArrive"
              :disabled="!!dataForm.id"
              placeholder="激活CPA"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="累计ROI" prop="roi">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.roi"
              :disabled="!!dataForm.id"
              placeholder="累计ROI"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="活跃" prop="aliveCount">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.aliveCount"
              placeholder="活跃"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="新增" prop="newCount">
            <el-input-number
              :min="0"
              v-model.trim="dataForm.newCount"
              placeholder="新增"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20"></el-row>
      <!--<el-row :gutter="20">-->
      <!--  <el-col :span="12">-->
      <!--    <el-form-item label="金币发放比例" prop="coinGrantRatio">-->
      <!--      <el-input-number-->
      <!--        :min="0"-->
      <!--        v-model.trim="dataForm.coinGrantRatio"-->
      <!--        placeholder="金币发放比例"-->
      <!--      />-->
      <!--    </el-form-item>-->
      <!--  </el-col>-->
      <!--  <el-col :span="12">-->
      <!--    <el-form-item label="提现比例" prop="withdrawRatio">-->
      <!--      <el-input-number-->
      <!--        :min="0"-->
      <!--        v-model.trim="dataForm.withdrawRatio"-->
      <!--        placeholder="提现比例"-->
      <!--      />-->
      <!--    </el-form-item>-->
      <!--  </el-col>-->
      <!--</el-row>-->
      <!--<el-row :gutter="20">-->
      <!--  <el-col :span="12">-->
      <!--    <el-form-item label="每日提现金额" prop="withdrawAmount">-->
      <!--      <el-input-number-->
      <!--        :min="0"-->
      <!--        v-model.trim="dataForm.withdrawAmount"-->
      <!--        placeholder="每日提现金额"-->
      <!--      />-->
      <!--    </el-form-item>-->
      <!--  </el-col>-->
      <!--</el-row>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
    <!--<span v-show="false">{{ compAIPU }}|{{ compARPU }}|{{ compECPM }}</span>-->
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appId: '',
        day: '',
        newCount: '',
        aliveCount: '',
        newArriveFrontCount: '',
        aliveArriveFrontCount: '',
        externalSceneCount: '',
        csjIncome: '',
        ylhIncome: '',
        ksIncome: '',
        baiduIncome: '',
        externalBaiduInfoIncome: '',
        huaweiIncome: '',
        cost: '',
        totalRevenue: '',
        adImpressions: '',
        aiPu: '',
        arPu: '',
        ecpm: '',
        lockScreenAiPu: '',
        lockScreenImpressions: '',
        roi: '',

        newArriveFrontRate: '',
        aliveArriveFrontRate: '',
        externalSceneRate: '',

        cpaByNew: '',
        cpaByArrive: '',
        lockScreenEcpm: '',
        eventCnt: '',
        mintIncome: '',
        yandexIncome: '',

        // coinGrantRatio: '',
        // withdrawRatio: '',
        // withdrawAmount: '',
      },
      dataRule: {
        appId: [
          {
            required: true,
            message: '应用id不能为空',
            trigger: 'blur',
          },
        ],
        day: [
          {
            required: true,
            message: '日期不能为空',
            trigger: 'blur',
          },
        ],
        newCount: [
          {
            required: true,
            message: '新增统计不能为空',
            trigger: 'blur',
          },
        ],
        aliveCount: [
          {
            required: true,
            message: '活跃统计不能为空',
            trigger: 'blur',
          },
        ],
        newArriveFrontCount: [
          {
            required: true,
            message: '新用户首页到达统计不能为空',
            trigger: 'blur',
          },
        ],
        aliveArriveFrontCount: [
          {
            required: true,
            message: '活跃用户首页到达统计不能为空',
            trigger: 'blur',
          },
        ],
        externalSceneCount: [
          {
            required: true,
            message: '外部场景触发人数不能为空',
            trigger: 'blur',
          },
        ],
        csjIncome: [
          {
            required: true,
            message: '穿山甲收入不能为空',
            trigger: 'blur',
          },
        ],
        ylhIncome: [
          {
            required: true,
            message: '优量汇收入不能为空',
            trigger: 'blur',
          },
        ],
        ksIncome: [
          {
            required: true,
            message: '快手收入不能为空',
            trigger: 'blur',
          },
        ],
        baiduIncome: [
          {
            required: true,
            message: '百度收入不能为空',
            trigger: 'blur',
          },
        ],
        externalBaiduInfoIncome: [
          {
            required: true,
            message: '外部百度资讯收入不能为空',
            trigger: 'blur',
          },
        ],
        cost: [
          {
            required: true,
            message: '成本不能为空',
            trigger: 'blur',
          },
        ],
        totalRevenue: [
          {
            required: true,
            message: '总收入不能为空',
            trigger: 'blur',
          },
        ],
        adImpressions: [
          { required: true, message: '广告展示量不能为空', trigger: 'blur' },
        ],
        aiPu: [
          {
            required: true,
            message: '人均广告展示次数不能为空',
            trigger: 'blur',
          },
        ],
        arPu: [
          {
            required: true,
            message: '每用户平均收入不能为空',
            trigger: 'blur',
          },
        ],
        ecpm: [
          {
            required: true,
            message: '每一千次展示可以获得的广告收入不能为空',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  computed: {
    // AIPU = 广告展示量/活跃数字
    // compAIPU() {
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   this.dataForm.aiPu =
    //     this.dataForm.adImpressions / this.dataForm.aliveCount
    //
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   this.dataForm.aiPu = Number.isNaN(this.dataForm.aiPu)
    //     ? 0
    //     : this.dataForm.aiPu
    //
    //   return this.dataForm.aiPu
    // },
    // // ARPU = 总收入/活跃人数
    // compARPU() {
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   this.dataForm.arPu = this.dataForm.totalRevenue / this.dataForm.aliveCount
    //
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   this.dataForm.arPu = Number.isNaN(this.dataForm.arPu)
    //     ? 0
    //     : this.dataForm.arPu
    //
    //   return this.dataForm.arPu
    // },
    // // ecpm = 总收入/广告展示量*1000
    // compECPM() {
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   this.dataForm.ecpm =
    //     (Number(this.dataForm.totalRevenue) /
    //       Number(this.dataForm.adImpressions)) *
    //     1000
    //
    //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    //   this.dataForm.ecpm = Number.isNaN(this.dataForm.ecpm)
    //     ? 0
    //     : this.dataForm.ecpm
    //
    //   return this.dataForm.ecpm
    // },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/statbusinessdaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.statBusinessDaily.appId
              this.dataForm.day = data.statBusinessDaily.day
              this.dataForm.newCount = data.statBusinessDaily.newCount
              this.dataForm.aliveCount = data.statBusinessDaily.aliveCount
              this.dataForm.newArriveFrontCount =
                data.statBusinessDaily.newArriveFrontCount
              this.dataForm.aliveArriveFrontCount =
                data.statBusinessDaily.aliveArriveFrontCount
              this.dataForm.externalSceneCount =
                data.statBusinessDaily.externalSceneCount
              this.dataForm.csjIncome = data.statBusinessDaily.csjIncome
              this.dataForm.ylhIncome = data.statBusinessDaily.ylhIncome
              this.dataForm.ksIncome = data.statBusinessDaily.ksIncome
              this.dataForm.baiduIncome = data.statBusinessDaily.baiduIncome
              this.dataForm.externalBaiduInfoIncome =
                data.statBusinessDaily.externalBaiduInfoIncome
              this.dataForm.cost = data.statBusinessDaily.cost
              this.dataForm.totalRevenue = data.statBusinessDaily.totalRevenue
              this.dataForm.adImpressions = data.statBusinessDaily.adImpressions
              this.dataForm.aiPu = data.statBusinessDaily.aiPu
              this.dataForm.arPu = data.statBusinessDaily.arPu
              this.dataForm.ecpm = data.statBusinessDaily.ecpm
              this.dataForm.lockScreenAiPu =
                data.statBusinessDaily.lockScreenAiPu
              this.dataForm.lockScreenImpressions =
                data.statBusinessDaily.lockScreenImpressions
              this.dataForm.roi = data.statBusinessDaily.roi
              this.dataForm.newArriveFrontRate =
                data.statBusinessDaily.newArriveFrontRate
              this.dataForm.aliveArriveFrontRate =
                data.statBusinessDaily.aliveArriveFrontRate
              this.dataForm.externalSceneRate =
                data.statBusinessDaily.externalSceneRate
              this.dataForm.cpaByNew = data.statBusinessDaily.cpaByNew
              this.dataForm.cpaByArrive = data.statBusinessDaily.cpaByArrive
              this.dataForm.lockScreenEcpm =
                data.statBusinessDaily.lockScreenEcpm
              this.dataForm.eventCnt = data.statBusinessDaily.eventCnt
              this.dataForm.huaweiIncome = data.statBusinessDaily.huaweiIncome
              this.dataForm.mintIncome = data.statBusinessDaily.mintIncome
              this.dataForm.yandexIncome = data.statBusinessDaily.yandexIncome

              // this.dataForm.coinGrantRatio =
              //   data.statBusinessDaily.coinGrantRatio
              // this.dataForm.withdrawRatio = data.statBusinessDaily.withdrawRatio
              // this.dataForm.withdrawAmount =
              //   data.statBusinessDaily.withdrawAmount
            } else {
              this.$message.error(data.msg || '服务器错')
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/statbusinessdaily/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              day: this.dataForm.day,
              newCount: this.dataForm.newCount,
              aliveCount: this.dataForm.aliveCount,
              newArriveFrontCount: this.dataForm.newArriveFrontCount,
              aliveArriveFrontCount: this.dataForm.aliveArriveFrontCount,
              externalSceneCount: this.dataForm.externalSceneCount,
              csjIncome: this.dataForm.csjIncome,
              ylhIncome: this.dataForm.ylhIncome,
              ksIncome: this.dataForm.ksIncome,
              baiduIncome: this.dataForm.baiduIncome,
              externalBaiduInfoIncome: this.dataForm.externalBaiduInfoIncome,
              cost: this.dataForm.cost,
              totalRevenue: this.dataForm.totalRevenue,
              adImpressions: this.dataForm.adImpressions,
              aiPu: this.dataForm.aiPu,
              arPu: this.dataForm.arPu,
              ecpm: this.dataForm.ecpm,
              lockScreenAiPu: this.dataForm.lockScreenAiPu,
              lockScreenImpressions: this.dataForm.lockScreenImpressions,
              roi: this.dataForm.roi,
              newArriveFrontRate: this.dataForm.newArriveFrontRate,
              aliveArriveFrontRate: this.dataForm.aliveArriveFrontRate,
              externalSceneRate: this.dataForm.externalSceneRate,
              cpaByNew: this.dataForm.cpaByNew,
              cpaByArrive: this.dataForm.cpaByArrive,
              lockScreenEcpm: this.dataForm.lockScreenEcpm,
              eventCnt: this.dataForm.eventCnt,
              huaweiIncome: this.dataForm.huaweiIncome,
              mintIncome: this.dataForm.mintIncome,
              yandexIncome: this.dataForm.yandexIncome,
              // coinGrantRatio: this.dataForm.coinGrantRatio,
              // withdrawRatio: this.dataForm.withdrawRatio,
              // withdrawAmount: this.dataForm.withdrawAmount,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
