<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="!dataForm.id ? '新增' : '修改'"
    :visible.sync="visible">
    <el-form ref="dataForm" :model="dataForm" :rules="dataRule" label-width="80px"
             @keyup.enter.native="dataFormSubmit()">
      <el-form-item label="投放账户id" prop="accountId">
        <el-input v-model="dataForm.accountId" placeholder="投放账户id"></el-input>
      </el-form-item>
      <el-form-item label="代理商">
        <el-select
          v-model="dataForm.agent"
          collapse-tags
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in agentList"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="oppo API_ID" prop="apiId">
        <el-input v-model="dataForm.apiId" placeholder="oppo API_ID"></el-input>
      </el-form-item>
      <el-form-item label="oppo API_Key" prop="apiKey">
        <el-input v-model="dataForm.apiKey" placeholder="oppo API_Key"></el-input>
      </el-form-item>
      <el-form-item label="投放渠道" prop="channel">
        <el-select v-model="dataForm.channel" filterable placeholder="请选择投放渠道">
          <el-option label="头条" value="1"></el-option>
          <el-option label="快手" value="2"></el-option>
          <el-option label="oppo" value="3"></el-option>
          <el-option label="vivo" value="4"></el-option>
          <el-option label="honor" value="5"></el-option>
          <el-option label="uc" value="6"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批量添加" prop="extra">
        <el-input
          v-model="dataForm.extra"
          :rows="5"
          placeholder="批量添加"
          resize="none"
          type="textarea">
        </el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="'1'">启用</el-radio>
          <el-radio :label="'0'">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { agentList } from '@/map/agent'

export default {
  data() {
    return {
      visible: false,
      agentList: agentList,
      dataForm: {
        id: 0,
        accountId: '',
        agent: '',
        apiId: '',
        apiKey: '',
        channel: '',
        extra: '',
        status: '1',
      },
      dataRule: {
        agent: [
          { required: true, message: '代理名称不能为空', trigger: 'blur' },
        ],
        channel: [
          { required: true, message: '投放渠道', trigger: 'blur' },
        ],
        status: [
          { required: true, message: '状态', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/stat/ctraccountmanage/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.accountId = data.ctrAccountManage.accountId
              this.dataForm.agent = data.ctrAccountManage.agent
              this.dataForm.apiId = data.ctrAccountManage.apiId
              this.dataForm.apiKey = data.ctrAccountManage.apiKey
              this.dataForm.channel = data.ctrAccountManage.channel
              this.dataForm.extra = data.ctrAccountManage.extra
              this.dataForm.status = data.ctrAccountManage.status
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/stat/ctraccountmanage/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'accountId': this.dataForm.accountId,
              'agent': this.dataForm.agent,
              'apiId': this.dataForm.apiId,
              'apiKey': this.dataForm.apiKey,
              'channel': this.dataForm.channel,
              'extra': this.dataForm.extra,
              'status': this.dataForm.status,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
