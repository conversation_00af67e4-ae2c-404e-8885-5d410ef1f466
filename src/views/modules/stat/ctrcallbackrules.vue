<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
      'select',
      // 'copy',
    ]"
    operate-width="260"
    :before-select="beforeSelect"
    @model-init-data="handleModelInitData"
  >
    <template #table_item_appId="{row}">
      <color-tag :id="row.appId">{{ row.appId | getAppName }}</color-tag>
    </template>
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appId"
        :is-show-all="false"
        clearable
      />
    </template>
    <template #form_channel>
      <arr-select
        :list="channelList"
        v-model="selectFormData.channel"
        multiple
        clearable
      />
    </template>
    <template #form_brand>
      <arr-select
        :list="brandList"
        v-model="selectFormData.brand"
        multiple
        clearable
      />
    </template>
    <template #form_accountId>
      <el-input
        v-model="selectFormData.accountId"
        clearable
        placeholder="账号ID"
      />
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>

    <template #model>
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model="modelConfig.formData.appId"
          :is-show-all="false"
          clearable
        />
      </el-form-item>
      <el-form-item label="策略名称" prop="ruleName">
        <el-input
          v-model="modelConfig.formData.ruleName"
          placeholder="策略名称"
        />
      </el-form-item>
      <el-form-item label="账户列表" prop="accountIds">
        <el-input
          type="textarea"
          v-model="modelConfig.formData.accountIds"
          placeholder="账户列表"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channel">
        <arr-select
          :list="channelList"
          v-model="modelConfig.formData.channel"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <arr-select
          :list="brandList"
          v-model="modelConfig.formData.brand"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="回传比例" prop="callbackPercent">
        <el-input
          type="number"
          v-model="modelConfig.formData.callbackPercent"
          placeholder="回传比例 (0-1，1代表100%)"
          :max="1"
          :min="0"
          step="0.01"
        />
      </el-form-item>
      <el-form-item label="代理商" prop="agent">
        <arr-select
          :list="agentList"
          v-model="modelConfig.formData.agent"
          label-key="label"
          value-key="value"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="投放媒体" prop="unionType">
        <arr-select
          :list="unionTypeList"
          v-model="modelConfig.formData.unionType"
          label-key="label"
          value-key="value"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="最新日期" prop="day">
        <el-date-picker
          v-model="modelConfig.formData.day"
          type="date"
          placeholder="选择日期"
          value-format="yyyyMMdd"
          format="yyyy-MM-dd"
          style="width: 100%;"
        />
        <el-alert
          title="重要配置，谨慎修改"
          type="warning"
          style="margin-top: 10px"
        />
      </el-form-item>
      <el-form-item label="点击数" prop="clickNum">
        <el-input
          type="number"
          v-model="modelConfig.formData.clickNum"
          placeholder="点击数"
        />
        <el-alert
          title="重要配置，谨慎修改"
          type="warning"
          style="margin-top: 10px"
        />
      </el-form-item>
      <el-form-item label="曝光数" prop="exposureNum">
        <el-input
          type="number"
          v-model="modelConfig.formData.exposureNum"
          placeholder="曝光数"
        />
        <el-alert
          title="重要配置，谨慎修改"
          type="warning"
          style="margin-top: 10px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark" style="margin-bottom: 40px">
        <el-input
          type="textarea"
          v-model="modelConfig.formData.remark"
          placeholder="备注"
        />
      </el-form-item>

      <!-- 回传条件 -->
      <el-divider content-position="left">回传条件</el-divider>
      <el-form-item label="IPU下限" prop="actIpu">
        <el-input
          type="number"
          v-model="modelConfig.formData.actIpu"
          placeholder="激活IPU下限 (>=)"
        />
      </el-form-item>
      <el-form-item label="平均ECPM下限" prop="actAvgEcpm">
        <el-input
          type="number"
          v-model="modelConfig.formData.actAvgEcpm"
          placeholder="激活条件平均ECPM下限"
          step="0.01"
        />
        <span style="color: #0bb2d4; margin-left: 10px">
          {{ `(${(modelConfig.formData.actAvgEcpm / 100).toFixed(2)}元)` }}
        </span>
      </el-form-item>
      <el-form-item label="ARPU下限" prop="actMinArpu">
        <el-input
          type="number"
          v-model="modelConfig.formData.actMinArpu"
          placeholder="激活条件ARPU下限"
          step="0.01"
        />
        <span style="color: #0bb2d4; margin-left: 10px">
          {{ `(${(modelConfig.formData.actMinArpu / 100000).toFixed(2)}元)` }}
        </span>
      </el-form-item>
      <el-form-item label="ARPU上限" prop="actMaxArpu">
        <el-input
          type="number"
          v-model="modelConfig.formData.actMaxArpu"
          placeholder="激活条件ARPU上限"
          step="0.01"
        />
        <span style="color: #0bb2d4; margin-left: 10px">
          {{ `(${(modelConfig.formData.actMaxArpu / 100000).toFixed(2)}元)` }}
        </span>
      </el-form-item>

      <el-form-item label="状态" prop="status" style="margin-top: 40px">
        <radio-status v-model="modelConfig.formData.status" />
      </el-form-item>
    </template>
  </page-table>
</template>

<script>
import { drawConfigMap } from '@/map/fishing'
import { ctrCallbackRules as request } from '@/api/stat'
import { agentList } from '@/map/agent'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 50 },
          { field: 'ruleName', title: '策略名称', minWidth: 80 },
          {
            field: 'appId',
            title: '应用',
            minWidth: 100,
            slots: {
              default: 'table_item_appId',
            },
          },
          {
            field: 'status',
            title: '状态',
            minWidth: 50,
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'callbackPercent', title: '回传比例', minWidth: 60 },
          { field: 'remark', title: '备注', minWidth: 80 },
          { field: 'accountIds', title: '账户列表', minWidth: 100 },
          { field: 'channel', title: '渠道', minWidth: 100 },
          { field: 'brand', title: '品牌', minWidth: 100 },
          {
            field: 'agent',
            title: '代理商',
            minWidth: 100,
            formatter: ({ row }) =>
              this.agentList.find(item => item.value === row.agent)?.label,
          },
          {
            field: 'unionType',
            title: '投放媒体',
            minWidth: 100,
            formatter: ({ row }) =>
              this.unionTypeList.find(item => item.value === row.unionType)?.label
          },
          {
            field: 'day',
            title: '最新日期',
            minWidth: 80,
            formatter: ({ row }) =>
              this.$dayjs(row.day.toString()).format('YYYY-MM-DD'),
          },
          { field: 'clickNum', title: '点击数', minWidth: 50 },
          { field: 'exposureNum', title: '曝光数', minWidth: 50 },
          { field: 'actIpu', title: '激活IPU下限', minWidth: 80 },
          { field: 'actAvgEcpm', title: '激活平均ECPM下限', minWidth: 100 },
          { field: 'actMinArpu', title: '激活ARPU下限', minWidth: 80 },
          { field: 'actMaxArpu', title: '激活ARPU上限', minWidth: 80 },
          { field: 'createdAt', title: '创建时间', minWidth: 80 },
          { field: 'updatedAt', title: '更新时间', minWidth: 80 },
          { field: 'createdBy', title: '创建者', minWidth: 50 },
          { field: 'updatedBy', title: '更新者', minWidth: 50 },
        ],
        formConfig: {
          items: [
            {
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              title: '渠道',
              slots: {
                default: 'form_channel',
              },
            },
            {
              title: '品牌',
              slots: {
                default: 'form_brand',
              },
            },
            {
              title: '账户列表',
              slots: {
                default: 'form_accountId',
              },
            },
          ],
        },
      },
      modelConfig: {
        adapterResData(data) {
          return {
            ...data,
            day: data.day.toString(),
            // 回传条件字段确保为字符串格式用于表单显示
            actIpu: data.actIpu !== null && data.actIpu !== undefined ? data.actIpu.toString() : '',
            actAvgEcpm: data.actAvgEcpm !== null && data.actAvgEcpm !== undefined ? data.actAvgEcpm.toString() : '',
            actMinArpu: data.actMinArpu !== null && data.actMinArpu !== undefined ? data.actMinArpu.toString() : '',
            actMaxArpu: data.actMaxArpu !== null && data.actMaxArpu !== undefined ? data.actMaxArpu.toString() : '',
          }
        },
        modelConfig: {
          width: '520px',
        },
        formConfig: {
          labelWidth: '110px',
          // labelPosition: 'top',
        },
        adapterFormData: formData => {
          return {
            ...formData,
            day: Number(formData.day),
            // 回传条件字段类型转换
            actIpu: formData.actIpu ? Number(formData.actIpu) : null,
            actAvgEcpm: formData.actAvgEcpm ? Number(formData.actAvgEcpm) : null,
            actMinArpu: formData.actMinArpu ? Number(formData.actMinArpu) : null,
            actMaxArpu: formData.actMaxArpu ? Number(formData.actMaxArpu) : null,
          }
        },
        formData: {
          id: null,
          appId: '',
          ruleName: '',
          status: 0,
          accountIds: '',
          createdAt: '',
          updatedAt: '',
          createdBy: '',
          updatedBy: '',
          callbackPercent: '',
          remark: '',
          day: '',
          clickNum: '',
          exposureNum: '',
          channel: '',
          brand: '',
          agent: '',
          unionType: '',
          // 回传条件字段
          actIpu: 0,
          actAvgEcpm: 0,
          actMinArpu: 0,
          actMaxArpu: 0,
        },
        formRule: {
          appId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          ruleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
          // day: [{ required: true, message: '不能为空', trigger: 'blur' }],
          // clickNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
          channel: [{ required: true, message: '不能为空', trigger: 'blur' }],
          brand: [{ required: true, message: '不能为空', trigger: 'blur' }],
          agent: [{ required: true, message: '不能为空', trigger: 'blur' }],
          unionType: [{ required: true, message: '不能为空', trigger: 'blur' }],
          // exposureNum: [
          //   { required: true, message: '不能为空', trigger: 'blur' },
          // ],
          accountIds: [{ required: true, message: '不能为空', trigger: 'blur' }],
          callbackPercent: [
            { required: true, message: '不能为空', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value === '' || value === null || value === undefined) {
                  callback()
                  return
                }
                const num = Number(value)
                if (isNaN(num)) {
                  callback(new Error('请输入有效的数字'))
                } else if (num < 0 || num > 1) {
                  callback(new Error('回传比例必须在0-1之间'))
                } else {
                  callback()
                }
              },
              trigger: 'blur'
            },
          ],
        },
      },
      selectFormData: {
        appId: 10081,
        channel: '',
        brand: '',
        accountId: '',
      },
      channelList: [
        '快手',
        '头条',
        '广点通',
        '百度',
        'oppo信息流',
        'vivo信息流',
        'honor信息流',
        '小米信息流',
        '华为信息流',
        'uc',
      ],
      brandList: ['荣耀', '华为', '小米', 'oppo', 'vivo'],
      agentList: agentList,
      unionTypeList: [
        { label: 'oppo联盟', value: 1 },
        { label: 'oppo站内', value: 2 },
        { label: 'vivo联盟', value: 3 },
        { label: 'vivo站内', value: 4 },
        { label: '快手站内', value: 5 },
        { label: '快手联盟', value: 6 },
        { label: '穿山甲', value: 7 },
        { label: '抖音', value: 8 },
        { label: '优量汇', value: 9 },
        { label: 'uc浏览器', value: 10 },
        { label: '荣耀站内', value: 11 },
        { label: '荣耀联盟', value: 12 },
        { label: '华为站内', value: 13 },
        { label: '华为联盟', value: 14 },
        { label: '小米站内', value: 15 },
        { label: '小米联盟', value: 16 },
        { label: '百度站内', value: 17 },
        { label: '百度联盟', value: 18 },
      ],
    }
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
    beforeSelect() {
      const channel = this.selectFormData.channel
        ? this.selectFormData.channel.join(',')
        : ''
      const brand = this.selectFormData.brand
        ? this.selectFormData.brand.join(',')
        : ''

      return {
        ...this.selectFormData,
        channel,
        brand,
      }
    },
    handleModelInitData() {
      console.log('数据初始化完成....')
    },
  },
}
</script>
