<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="日期" prop="dt">
        <el-input v-model="dataForm.dt" placeholder="日期"></el-input>
      </el-form-item>
      <!-- <el-form-item label="订阅人数" prop="subNum">
        <el-input v-model="dataForm.subNum" placeholder="订阅人数"></el-input>
      </el-form-item>
      <el-form-item label="week2订阅人数" prop="week2">
        <el-input
          v-model="dataForm.week2"
          placeholder="week2订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="week3订阅人数" prop="week3">
        <el-input
          v-model="dataForm.week3"
          placeholder="week3订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="week4订阅人数" prop="week4">
        <el-input
          v-model="dataForm.week4"
          placeholder="week4订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="week5订阅人数" prop="week5">
        <el-input
          v-model="dataForm.week5"
          placeholder="week5订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="week6订阅人数" prop="week6">
        <el-input
          v-model="dataForm.week6"
          placeholder="week6订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="week7订阅人数" prop="week7">
        <el-input
          v-model="dataForm.week7"
          placeholder="week7订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="week8订阅人数" prop="week8">
        <el-input
          v-model="dataForm.week8"
          placeholder="week8订阅人数"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-input
          v-model="dataForm.updatedAt"
          placeholder="更新时间"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="成本" prop="cost">
        <el-input v-model="dataForm.cost" placeholder="成本"></el-input>
      </el-form-item>
      <el-form-item label="累计金额" prop="amount">
        <el-input v-model="dataForm.amount" placeholder="累计金额"></el-input>
      </el-form-item>
      <el-form-item label="roi" prop="roi">
        <el-input v-model="dataForm.roi" placeholder="roi"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        dt: '',
        subNum: '',
        week2: '',
        week3: '',
        week4: '',
        week5: '',
        week6: '',
        week7: '',
        week8: '',
        createdAt: '',
        updatedAt: '',
        cost: '',
        amount: '',
        roi: '',
      },
      dataRule: {
        dt: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        // subNum: [
        //   { required: true, message: '订阅人数不能为空', trigger: 'blur' },
        // ],
        // week2: [
        //   { required: true, message: 'week2订阅人数不能为空', trigger: 'blur' },
        // ],
        // week3: [
        //   { required: true, message: 'week3订阅人数不能为空', trigger: 'blur' },
        // ],
        // week4: [
        //   { required: true, message: 'week4订阅人数不能为空', trigger: 'blur' },
        // ],
        // week5: [
        //   { required: true, message: 'week5订阅人数不能为空', trigger: 'blur' },
        // ],
        // week6: [
        //   { required: true, message: 'week6订阅人数不能为空', trigger: 'blur' },
        // ],
        // week7: [
        //   { required: true, message: 'week7订阅人数不能为空', trigger: 'blur' },
        // ],
        // week8: [
        //   { required: true, message: 'week8订阅人数不能为空', trigger: 'blur' },
        // ],
        // createdAt: [
        //   { required: true, message: '创建时间不能为空', trigger: 'blur' },
        // ],
        // updatedAt: [
        //   { required: true, message: '更新时间不能为空', trigger: 'blur' },
        // ],
        cost: [{ required: true, message: '成本不能为空', trigger: 'blur' }],
        amount: [
          { required: true, message: '累计金额不能为空', trigger: 'blur' },
        ],
        // roi: [
        //   { required: true, message: 'roi不能为空', trigger: 'blur' }
        // ]
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/subscriptionkeep/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.dt = data.subscriptionKeep.dt
              // this.dataForm.subNum = data.subscriptionKeep.subNum
              // this.dataForm.week2 = data.subscriptionKeep.week2
              // this.dataForm.week3 = data.subscriptionKeep.week3
              // this.dataForm.week4 = data.subscriptionKeep.week4
              // this.dataForm.week5 = data.subscriptionKeep.week5
              // this.dataForm.week6 = data.subscriptionKeep.week6
              // this.dataForm.week7 = data.subscriptionKeep.week7
              // this.dataForm.week8 = data.subscriptionKeep.week8
              // this.dataForm.createdAt = data.subscriptionKeep.createdAt
              // this.dataForm.updatedAt = data.subscriptionKeep.updatedAt
              this.dataForm.cost = data.subscriptionKeep.cost
              this.dataForm.amount = data.subscriptionKeep.amount
              this.dataForm.roi = data.subscriptionKeep.roi
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/subscriptionkeep/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              dt: this.dataForm.dt,
              // subNum: this.dataForm.subNum,
              // week2: this.dataForm.week2,
              // week3: this.dataForm.week3,
              // week4: this.dataForm.week4,
              // week5: this.dataForm.week5,
              // week6: this.dataForm.week6,
              // week7: this.dataForm.week7,
              // week8: this.dataForm.week8,
              // createdAt: this.dataForm.createdAt,
              // updatedAt: this.dataForm.updatedAt,
              cost: this.dataForm.cost,
              amount: this.dataForm.amount,
              roi: this.dataForm.roi,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
