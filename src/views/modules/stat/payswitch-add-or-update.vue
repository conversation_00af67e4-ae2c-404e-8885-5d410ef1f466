<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible" width="800px">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="80px">
      <el-form-item label="支付方式" prop="type">
        <el-input v-model="dataForm.type" placeholder="支付方式：1：stripe,2：空中云汇"></el-input>
      </el-form-item>
      <!-- <el-form-item label="支付渠道" prop="payList">
        <el-input v-model="dataForm.payList" placeholder="支付渠道"></el-input>
      </el-form-item> -->
      <el-form-item label="应用id" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <el-input v-model="dataForm.status" placeholder="1:启用，2：不启用"></el-input>
      </el-form-item>
      <el-form-item label="序号" prop="serialNo">
        <el-input v-model="dataForm.serialNo" placeholder="序号"></el-input>
      </el-form-item>
      <!-- <el-form-item label="类型" prop="status">
        <el-select v-model="dataForm.status" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="关联渠道">
        <el-transfer class="my-transfer" v-model="selectPayList" :data="payList" filterable target-order="push"
          :titles="['所有渠道', '分类渠道']">
          <div slot-scope="{ option }">
            {{ option.label }}
          </div>
        </el-transfer>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Sortable from 'sortablejs'
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        type: '',
        payList: '',
        appId: '',
        status: '',
        serialNo: ''
      },
      dataRule: {
        type: [
          { required: true, message: '支付方式：1：stripe,2：空中云汇不能为空', trigger: 'blur' }
        ],
        // payList: [
        //   { required: true, message: '支付渠道不能为空', trigger: 'blur' }
        // ],
        appId: [
          { required: true, message: '应用id不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '1:启用，2：不启用不能为空', trigger: 'blur' }
        ],
        serialNo: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      payList: [
        { id: 1, name: 'apple', key: 1, label: 'apple' },
        { id: 2, name: 'google', key: 2, label: 'google' },
        { id: 3, name: 'creditCard', key: 3, label: 'creditCard' },
        { id: 4, name: 'link', key: 4, label: 'link' },
        { id: 5, name: 'paypal', key: 5, label: 'paypal' },
      ],
      selectPayList: []
    }
  },
  mounted() {
    setTimeout(()=>{
      this.getPaySwitchList()

    },500)
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/stat/payswitch/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams()
          }).then(({ data }) => {
            if (data && data.code === 0) {
              console.log('data.paySwitch.payList',data.paySwitch.payList);
              let list = []
              data.paySwitch.payList.forEach(v=>{
                let item = this.payList.find(p=>p.name===v)
                if(item){
                  list.push(item.id)
                }
              })
             
              console.log(list);
              this.selectPayList = list
              console.log(this.selectPayList);
              // console.log(list);
              // data.paySwitch.payList.forEach((item, index) => {
              //   if(this.payList.some(p=>p.name===item)){
              //     list.push()
              //   }
              //   this.$set(this.selectPayList, 'index')
              // })
              this.$forceUpdate()
              console.log(this.selectPayList)

              this.dataForm.type = data.paySwitch.type
              this.dataForm.payList = data.paySwitch.payList
              this.dataForm.appId = data.paySwitch.appId
              this.dataForm.status = data.paySwitch.status
              this.dataForm.serialNo = data.paySwitch.serialNo

            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.dataForm.payList = this.selectPayList.map((id, index) => {
            const item = this.payList.find(payList => payList.id === id)
            return item.name
            // return {
            // id: id,
            // name: item.name,
            // order: index,
            // }
          })
          this.$http({
            url: this.$http.adornUrl(`/stat/payswitch/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'type': this.dataForm.type,
              'payList': this.dataForm.payList,
              'appId': this.dataForm.appId,
              'status': this.dataForm.status,
              'serialNo': this.dataForm.serialNo
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getPaySwitchList() {
      console.log('getPaySwitchList');
      this.$nextTick(() => {
        const ele = document
          .querySelectorAll('.my-transfer .el-transfer-panel')[1]
          .querySelector(' .el-transfer-panel__list')
        //  .el-transfer-panel__list
        console.log('ele object');
        console.log(ele)
        new Sortable(ele, {
          animation: 150,
          ghostClass: 'blue-background-class',
          // 结束拖拽
          onEnd: (/**Event*/ evt) => {
            console.log(this.selectPayList);
            this.selectPayList = this.arrJumpQueue(
              this.selectPayList,
              evt.oldIndex,
              evt.newIndex
            )
            console.log(this.selectPayList)
            console.log(evt.oldIndex, evt.newIndex)
            // var itemEl = evt.item;  // dragged HTMLElement
            // evt.to;    // target list
            // evt.from;  // previous list
            // evt.oldIndex;  // element's old index within old parent
            // evt.newIndex;  // element's new index within new parent
            // evt.clone // the clone element
            // evt.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
          },
        })
      })
    },
    // 数组插队
    arrJumpQueue(arr, oldIndex, newIndex) {
      if (oldIndex === newIndex) {
        return arr
      }

      const jumpItem = [arr[oldIndex]]

      if (newIndex === 0) {
        const a1 = arr.slice(0, oldIndex)
        const a2 = arr.slice(oldIndex + 1)
        return [jumpItem, a1, a2].flat()
      }

      if (newIndex === arr.length - 1) {
        const a1 = arr.slice(0, oldIndex)
        const a2 = arr.slice(oldIndex + 1)
        return [a1, a2, jumpItem].flat()
      }

      if (oldIndex === 0) {
        const a1 = arr.slice(1, newIndex)
        const a2 = arr.slice(newIndex)
        return [a1, jumpItem, a2].flat()
      }

      if (oldIndex === arr.length - 1) {
        const a1 = arr.slice(0, newIndex)
        const a2 = arr.slice(newIndex, oldIndex)
        return [a1, jumpItem, a2].flat()
      }

      if (newIndex > oldIndex) {
        const a1 = arr.slice(0, oldIndex)
        const a2 = arr.slice(oldIndex + 1, newIndex + 1)
        const a3 = arr.slice(newIndex + 1)
        return [a1, a2, jumpItem, a3].flat()
      } else {
        const a1 = arr.slice(0, newIndex)
        const a2 = arr.slice(newIndex, oldIndex)
        const a3 = arr.slice(oldIndex + 1)

        return [a1, jumpItem, a2, a3].flat()
      }
    },
    handleClose() {
      this.$emit('close')
      this.selectPayList = []
    },
  }
}
</script>
<style scoped>
.my-transfer::v-deep {
  .el-transfer-panel {
    width: 250px;
  }
}
</style>
