<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="日期选择">
        <el-date-picker
          clearable
          v-model="dataForm.date"
          :type="dateType"
          :format="dateFormat"
          :value-format="dateValueFormat"
          placeholder="选择日期，右侧切换日期格式"
        />
        <el-select
          v-model="dateType"
          @change="changeDateFormat"
          style="width: 70px;"
        >
          <el-option
            v-for="item in dateTypeList"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主体">
        <el-select v-model="dataForm.groupId" @change="changeGroupId">
          <el-option
            v-for="[key, label] in appGroupWithAll"
            :value="key"
            :label="label"
            :key="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('stat:statbusinesssummarydaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('stat:statbusinesssummarydaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel(false)"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      class="adapter-height"
      :max-height="tableHeight"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<af-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="id"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="id"-->
      <!--  width="50"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="appId"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="应用id"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="month"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="月份"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="year"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="年"-->
      <!--/>-->
      <af-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      >
        <template slot-scope="{ row }">
          <span>
            {{ $dayjs(String(row.day)).format(columnDateFormat) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="newCount"
        header-align="center"
        align="center"
        label="新增"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.newCount
                ? parseInt(row.newCount.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="aliveCount"
        header-align="center"
        align="center"
        label="活跃"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.aliveCount
                ? parseInt(row.aliveCount.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <!--<af-table-column-->
      <!--  prop="newArriveFrontCount"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="新用户首页到达统计"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="newArriveFrontRate"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="新用户首页到达率"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="aliveArriveFrontCount"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="活跃用户首页到达统计"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="aliveArriveFrontRate"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="活跃用户首页达到率"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="externalSceneCount"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="外部场景触发人数"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="externalSceneRate"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="外部场景触发率"-->
      <!--/>-->
      <af-table-column
        prop="csjIncome"
        header-align="center"
        align="center"
        label="OPPO"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.csjIncome
                ? parseInt(row.csjIncome.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ylhIncome"
        header-align="center"
        align="center"
        label="VIVO"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.ylhIncome
                ? parseInt(row.ylhIncome.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ksIncome"
        header-align="center"
        align="center"
        label="Honor"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.ksIncome
                ? parseInt(row.ksIncome.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="apploveIncome"
        header-align="center"
        align="center"
        label="applovin"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.apploveIncome
                ? parseFloat(row.apploveIncome.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <!--<af-table-column-->
      <!--  prop="externalBaiduInfoIncome"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="锁屏"-->
      <!--  width="90"-->
      <!--&gt;-->
      <!--  <template slot-scope="{ row }">-->
      <!--    <span>-->
      <!--      {{-->
      <!--        row.externalBaiduInfoIncome-->
      <!--          ? parseInt(-->
      <!--              row.externalBaiduInfoIncome.toFixed(2)-->
      <!--            ).toLocaleString()-->
      <!--          : '-'-->
      <!--      }}-->
      <!--    </span>-->
      <!--  </template>-->
      <!--</af-table-column>-->

      <af-table-column
        prop="externalBaiduInfoIncome"
        header-align="center"
        align="center"
        label="bigo"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.externalBaiduInfoIncome &&
                parseFloat(row.externalBaiduInfoIncome).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="yandexIncome"
        header-align="center"
        align="center"
        label="yandex"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.yandexIncome && parseFloat(row.yandexIncome).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="chartboostIncome"
        header-align="center"
        align="center"
        label="chartboost"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.chartboostIncome &&
                parseFloat(row.chartboostIncome).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="heliumIncome"
        header-align="center"
        align="center"
        label="helium"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.heliumIncome && parseFloat(row.heliumIncome).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="baiduIncome"
        header-align="center"
        align="center"
        label="海外华为"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.baiduIncome && parseFloat(row.baiduIncome).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="mintIncome"
        header-align="center"
        align="center"
        label="Mintegral"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.mintIncome && parseFloat(row.mintIncome).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="huaweiIncome"
        header-align="center"
        align="center"
        label="华为"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.huaweiIncome && parseFloat(row.huaweiIncome).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>

      <!--<af-table-column-->
      <!--  prop="adImpressions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="广告展示量"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="baiduImpressions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="百度广告展示量"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="ksImpressions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="topon广告展示量"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="ylhImpressions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="优量汇广告展示量"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="csjImpressions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="穿山甲广告展示量"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="lockScreenImpressions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="锁屏广告展示量(百度资讯广告展示量)"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="aiPu"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="人均广告展示次数广告AIPU"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="lockScreenAiPu"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="锁屏AIPU"-->
      <!--/>-->
      <af-table-column
        prop="arPu"
        header-align="center"
        align="center"
        label="arpu"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.arPu && row.arPu.toFixed(4) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ecpm"
        header-align="center"
        align="center"
        label="广告ecpm"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.ecpm && row.ecpm.toFixed(2) }}
          </span>
        </template>
      </af-table-column>
      <!--<af-table-column-->
      <!--  prop="lockScreenEcpm"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="锁屏ecpm"-->
      <!--/>-->
      <af-table-column
        prop="roi"
        header-align="center"
        align="center"
        label="ROI"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.roi && row.roi.toFixed(2) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="cpaByNew"
        header-align="center"
        align="center"
        label="新增CPA"
        width="90"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.cpaByNew && parseFloat(row.cpaByNew).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <!--<af-table-column-->
      <!--  prop="cpaByArrive"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="激活CPA"-->
      <!--/>-->
      <af-table-column
        prop="kwaiCpa"
        header-align="center"
        align="center"
        label="快手Cpa"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.kwaiCpa && row.kwaiCpa.toFixed(4) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ttCpa"
        header-align="center"
        align="center"
        label="ttCpa"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.ttCpa && row.ttCpa.toFixed(4) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="mintCpa"
        header-align="center"
        align="center"
        label="mintCpa"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.mintCpa && row.mintCpa.toFixed(4) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ggCpa"
        header-align="center"
        align="center"
        label="ggCpa"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.ggCpa && row.ggCpa.toFixed(4) }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="fbCpa"
        header-align="center"
        align="center"
        label="fbCpa"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.fbCpa && row.fbCpa.toFixed(4) }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="bigoCpa"
        header-align="center"
        align="center"
        label="bigoCpa"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.bigoCpa && row.bigoCpa.toFixed(4) }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="kwaiCost"
        header-align="center"
        align="center"
        label="快手成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.kwaiCost && parseFloat(row.kwaiCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ttCost"
        header-align="center"
        align="center"
        label="tt成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.ttCost && parseFloat(row.ttCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="mintCost"
        header-align="center"
        align="center"
        label="mint成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.mintCost && parseFloat(row.mintCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="ggCost"
        header-align="center"
        align="center"
        label="gg成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.ggCost && parseFloat(row.ggCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="fbCost"
        header-align="center"
        align="center"
        label="fb成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.fbCost && parseFloat(row.fbCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="bigoCost"
        header-align="center"
        align="center"
        label="bigo成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.bigoCost && parseFloat(row.bigoCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="huaweiCost"
        header-align="center"
        align="center"
        label="华为成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.huaweiCost && parseFloat(row.huaweiCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="vivoCost"
        header-align="center"
        align="center"
        label="vivo成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.vivoCost && parseFloat(row.vivoCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="oppoCost"
        header-align="center"
        align="center"
        label="oppo成本"
        v-if="false"
      >
        <template slot-scope="{ row }">
          <span>
            {{ row.oppoCost && parseFloat(row.oppoCost).toLocaleString() }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="cost"
        header-align="center"
        align="center"
        label="成本"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.cost ? parseInt(row.cost.toFixed(2)).toLocaleString() : '-'
            }}
          </span>
        </template>
      </af-table-column>
      <af-table-column
        prop="totalRevenue"
        header-align="center"
        align="center"
        label="总收入"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              row.totalRevenue
                ? parseInt(row.totalRevenue.toFixed(2)).toLocaleString()
                : '-'
            }}
          </span>
        </template>
      </af-table-column>

      <af-table-column
        prop="profit"
        header-align="center"
        align="center"
        label="毛利"
      >
        <template slot-scope="{ row }">
          <span>
            {{
              parseInt(
                ((row.totalRevenue || 0) - (row.cost || 0)).toFixed(2)
              ).toLocaleString()
            }}
          </span>
        </template>
      </af-table-column>
      <!--<af-table-column-->
      <!--  prop="createdAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="创建时间"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="updatedAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="更新时间"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  prop="updatedBy"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="更新者"-->
      <!--/>-->
      <!--<af-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.id)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.id)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</af-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <!--<add-or-update-->
    <!--  v-if="addOrUpdateVisible"-->
    <!--  ref="addOrUpdate"-->
    <!--  @refreshDataList="getDataList"-->
    <!--/>-->
  </div>
</template>

<script>
import { mixinElTableAdapterHeight } from '@/mixins'
import { appGroupWithAll } from '@/map/common'

// import AddOrUpdate from './statbusinesssummarydaily-add-or-update'
export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        date: '',
        groupId: 0,
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      dateTypeList: [
        {
          label: '天',
          value: 'date',
          valueFormat: 'yyyyMMdd',
          format: 'yyyy-MM-dd',
          apiUrl: '/stat/statbusinesssummarydaily/list?day=',
        },
        {
          label: '月',
          value: 'month',
          valueFormat: 'yyyyMM',
          format: 'yyyy-MM',
          apiUrl: '/stat/statbusinesssummarydaily/month_list?month=',
        },
        {
          label: '年',
          value: 'year',
          valueFormat: 'yyyy',
          format: 'yyyy',
          apiUrl: '/stat/statbusinesssummarydaily/year_list?year=',
        },
      ],
      dateType: 'date',
      dateValueFormat: 'yyyyMMdd',
      dateFormat: 'yyyy-MM-dd',
      columnDateFormat: 'YYYY-MM-DD',
      apiUrl: '/stat/statbusinesssummarydaily/list?day=',
      allDataList: [],
      appGroupWithAll,
    }
  },
  watch: {
    // '$store.state.user.groupIdList': {
    //   handler(groupIdList) {
    //     console.log('00-----', groupIdList)
    //     if (groupIdList) {
    //       const list = Array.from(this.appGroupWithAll).filter(([key]) =>
    //         groupIdList.includes(key)
    //       )
    //       if (list && list.length) {
    //         this.appGroupWithAll = new Map(list)
    //         this.dataForm.groupId = list[0][0]
    //       }
    //     }
    //   },
    //   immediate: true,
    // },
  },
  components: {
    // AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        groupId: this.dataForm.groupId,
      }

      this.$http({
        url: this.$http.adornUrl(this.apiUrl + (this.dataForm.date || 0)),
        method: 'get',
        params: this.$http.adornParams(params),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            if (this.dateType === 'date') {
              this.dataList = data.page.list
              this.totalPage = data.page.totalCount
            } else {
              this.allDataList = data.data
              // this.allDataList = new Array(36).fill(data.data).flat() // 模拟数据
              this.getCurrentDataList()
              this.totalPage = this.allDataList.length
            }
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
          this.columnDateFormat = this.dateFormat.toUpperCase()
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    getCurrentDataList() {
      this.dataList = this.allDataList.slice(
        (this.pageIndex - 1) * this.pageSize,
        (this.pageIndex - 1) * this.pageSize + this.pageSize
      )
      console.log(this.allDataList, this.pageIndex, this.dataList)
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getData()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getData()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/statbusinesssummarydaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getData() {
      if (this.dateType === 'date') {
        this.getDataList()
      } else {
        if (!this.allDataList.length) {
          this.getDataList()
        } else {
          this.getCurrentDataList()
        }
      }
    },
    changeDateFormat(v) {
      this.pageIndex = 1
      const res = this.dateTypeList.find(it => it.value === v)
      this.dateValueFormat = res.valueFormat
      this.dateFormat = res.format
      this.apiUrl = res.apiUrl
      this.dataForm.date = ''
    },
    changeGroupId() {
      this.pageIndex = 1
      this.getDataList()
    },
  },
}
</script>
