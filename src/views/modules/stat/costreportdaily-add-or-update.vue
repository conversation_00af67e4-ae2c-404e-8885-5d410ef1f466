<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="应用id" prop="appCode">
        <el-input
          v-model="dataForm.appCode"
          :disabled="!!dataForm.id"
          placeholder="应用id"
        />
      </el-form-item>
      <el-form-item label="日期" prop="day">
        <el-input
          v-model="dataForm.day"
          :disabled="!!dataForm.id"
          placeholder="日期"
        />
      </el-form-item>
      <el-form-item label="消耗金额" prop="statCost">
        <el-input v-model="dataForm.statCost" placeholder="消耗金额" />
      </el-form-item>
      <el-form-item label="赠款消耗金额" prop="statGrantCost">
        <el-input v-model="dataForm.statGrantCost" placeholder="赠款消耗金额" />
      </el-form-item>
      <el-form-item label="现金消耗金额" prop="statCashCost">
        <el-input v-model="dataForm.statCashCost" placeholder="现金消耗金额" />
      </el-form-item>
      <el-form-item label="展示数" prop="showCnt">
        <el-input v-model="dataForm.showCnt" placeholder="展示数" />
      </el-form-item>
      <el-form-item label="点击数" prop="clickCnt">
        <el-input v-model="dataForm.clickCnt" placeholder="点击数" />
      </el-form-item>
      <el-form-item label="转化数" prop="convertCnt">
        <el-input v-model="dataForm.convertCnt" placeholder="转化数" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appCode: '',
        day: '',
        statCost: '',
        statGrantCost: '',
        statCashCost: '',
        showCnt: '',
        clickCnt: '',
        convertCnt: '',
      },
      dataRule: {
        appCode: [
          { required: true, message: '应用id不能为空', trigger: 'blur' },
        ],
        day: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        statCost: [
          { required: true, message: '消耗金额不能为空', trigger: 'blur' },
        ],
        statGrantCost: [
          { required: true, message: '赠款消耗金额不能为空', trigger: 'blur' },
        ],
        statCashCost: [
          { required: true, message: '现金消耗金额不能为空', trigger: 'blur' },
        ],
        showCnt: [
          { required: true, message: '展示数不能为空', trigger: 'blur' },
        ],
        clickCnt: [
          { required: true, message: '点击数不能为空', trigger: 'blur' },
        ],
        convertCnt: [
          { required: true, message: '转化数不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/costreportdaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appCode = data.costReportDaily.appCode
              this.dataForm.day = data.costReportDaily.day
              this.dataForm.statCost = data.costReportDaily.statCost
              this.dataForm.showCnt = data.costReportDaily.showCnt
              this.dataForm.clickCnt = data.costReportDaily.clickCnt
              this.dataForm.convertCnt = data.costReportDaily.convertCnt
              this.dataForm.statGrantCost = data.costReportDaily.statGrantCost
              this.dataForm.statCashCost = data.costReportDaily.statCashCost
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/costreportdaily/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appCode: this.dataForm.appCode,
              day: this.dataForm.day,
              statCost: this.dataForm.statCost,
              showCnt: this.dataForm.showCnt,
              clickCnt: this.dataForm.clickCnt,
              convertCnt: this.dataForm.convertCnt,
              statGrantCost: this.dataForm.statGrantCost,
              statCashCost: this.dataForm.statCashCost,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
