<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      disabled
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="100px"
    >
      <el-form-item label="日期" prop="time">
        <el-input v-model="dataForm.time" placeholder="日期，格式：yyyyMMd" />
      </el-form-item>
      <el-form-item label="系统应用" prop="appCode">
        <el-input v-model="dataForm.appCode" placeholder="系统应用app_code" />
      </el-form-item>
      <el-form-item label="appsid" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="appsid" />
      </el-form-item>
      <el-form-item label="应用名称" prop="appName">
        <el-input v-model="dataForm.appName" placeholder="应用名称" />
      </el-form-item>
      <el-form-item label="到访页pv" prop="cpuVisitPv">
        <el-input v-model="dataForm.cpuVisitPv" placeholder="到访页pv" />
      </el-form-item>
      <el-form-item label="详情页pv" prop="cpuDetailPv">
        <el-input v-model="dataForm.cpuDetailPv" placeholder="详情页pv" />
      </el-form-item>
      <el-form-item label="广告展现量" prop="view">
        <el-input v-model="dataForm.view" placeholder="广告展现量" />
      </el-form-item>
      <el-form-item label="预计收入" prop="income">
        <el-input v-model="dataForm.income" placeholder="预计收入" />
      </el-form-item>
      <el-form-item label="点击量" prop="click">
        <el-input v-model="dataForm.click" placeholder="点击量" />
      </el-form-item>
      <el-form-item label="ecpm" prop="ecpm">
        <el-input v-model="dataForm.ecpm" placeholder="ecpm" />
      </el-form-item>
      <el-form-item label="点击率" prop="ctr">
        <el-input v-model="dataForm.ctr" placeholder="点击率" />
      </el-form-item>
      <el-form-item label="日期数值" prop="day">
        <el-input v-model="dataForm.day" placeholder="日期数值" />
      </el-form-item>
      <el-form-item label="创建日期" prop="createdAt">
        <el-input v-model="dataForm.createdAt" placeholder="" />
      </el-form-item>
    </el-form>
    <!--<span slot="footer" class="dialog-footer">-->
    <!--  <el-button @click="visible = false">取消</el-button>-->
    <!--  <el-button type="primary" @click="dataFormSubmit()">确定</el-button>-->
    <!--</span>-->
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        time: '',
        appCode: '',
        appId: '',
        appName: '',
        cpuVisitPv: '',
        cpuDetailPv: '',
        view: '',
        income: '',
        click: '',
        ecpm: '',
        ctr: '',
        day: '',
        createdAt: '',
      },
      dataRule: {
        time: [
          {
            required: true,
            message: '日期，格式：yyyyMMd不能为空',
            trigger: 'blur',
          },
        ],
        appCode: [
          {
            required: true,
            message: '系统应用app_code不能为空',
            trigger: 'blur',
          },
        ],
        appId: [{ required: true, message: 'appsid不能为空', trigger: 'blur' }],
        appName: [
          { required: true, message: '应用名称不能为空', trigger: 'blur' },
        ],
        cpuVisitPv: [
          { required: true, message: '到访页pv不能为空', trigger: 'blur' },
        ],
        cpuDetailPv: [
          { required: true, message: '详情页pv不能为空', trigger: 'blur' },
        ],
        view: [
          { required: true, message: '广告展现量不能为空', trigger: 'blur' },
        ],
        income: [
          { required: true, message: '预计收入不能为空', trigger: 'blur' },
        ],
        click: [{ required: true, message: '点击量不能为空', trigger: 'blur' }],
        ecpm: [{ required: true, message: 'ecpm不能为空', trigger: 'blur' }],
        ctr: [{ required: true, message: '点击率不能为空', trigger: 'blur' }],
        day: [{ required: true, message: '日期数值不能为空', trigger: 'blur' }],
        createdAt: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/baiducontentreportdaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.time = data.baiduContentReportDaily.time
              this.dataForm.appCode = data.baiduContentReportDaily.appCode
              this.dataForm.appId = data.baiduContentReportDaily.appId
              this.dataForm.appName = data.baiduContentReportDaily.appName
              this.dataForm.cpuVisitPv = data.baiduContentReportDaily.cpuVisitPv
              this.dataForm.cpuDetailPv =
                data.baiduContentReportDaily.cpuDetailPv
              this.dataForm.view = data.baiduContentReportDaily.view
              this.dataForm.income = data.baiduContentReportDaily.income
              this.dataForm.click = data.baiduContentReportDaily.click
              this.dataForm.ecpm = data.baiduContentReportDaily.ecpm
              this.dataForm.ctr = data.baiduContentReportDaily.ctr
              this.dataForm.day = data.baiduContentReportDaily.day
              this.dataForm.createdAt = data.baiduContentReportDaily.createdAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/baiducontentreportdaily/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              time: this.dataForm.time,
              appCode: this.dataForm.appCode,
              appId: this.dataForm.appId,
              appName: this.dataForm.appName,
              cpuVisitPv: this.dataForm.cpuVisitPv,
              cpuDetailPv: this.dataForm.cpuDetailPv,
              view: this.dataForm.view,
              income: this.dataForm.income,
              click: this.dataForm.click,
              ecpm: this.dataForm.ecpm,
              ctr: this.dataForm.ctr,
              day: this.dataForm.day,
              createdAt: this.dataForm.createdAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
