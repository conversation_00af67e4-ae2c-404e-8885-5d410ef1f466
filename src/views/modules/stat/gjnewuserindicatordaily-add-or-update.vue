<template>
  <el-dialog
    :title="!dataForm.dt ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="${column.comments}" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="channelName">
      <el-input v-model="dataForm.channelName" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="marketCode">
      <el-input v-model="dataForm.marketCode" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="advertiseId">
      <el-input v-model="dataForm.advertiseId" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="brand">
      <el-input v-model="dataForm.brand" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="model">
      <el-input v-model="dataForm.model" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="androidVersion">
      <el-input v-model="dataForm.androidVersion" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="appVersion">
      <el-input v-model="dataForm.appVersion" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="dnu">
      <el-input v-model="dataForm.dnu" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="impUv">
      <el-input v-model="dataForm.impUv" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="impCnt">
      <el-input v-model="dataForm.impCnt" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="income">
      <el-input v-model="dataForm.income" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="appDay2">
      <el-input v-model="dataForm.appDay2" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="appDay3">
      <el-input v-model="dataForm.appDay3" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="appDay7">
      <el-input v-model="dataForm.appDay7" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="appDay14">
      <el-input v-model="dataForm.appDay14" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="appDay30">
      <el-input v-model="dataForm.appDay30" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="uiDay2">
      <el-input v-model="dataForm.uiDay2" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="uiDay3">
      <el-input v-model="dataForm.uiDay3" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="uiDay7">
      <el-input v-model="dataForm.uiDay7" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="uiDay14">
      <el-input v-model="dataForm.uiDay14" placeholder="${column.comments}"></el-input>
    </el-form-item>
    <el-form-item label="${column.comments}" prop="uiDay30">
      <el-input v-model="dataForm.uiDay30" placeholder="${column.comments}"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          dt: 0,
          appId: '',
          channelName: '',
          marketCode: '',
          advertiseId: '',
          brand: '',
          model: '',
          androidVersion: '',
          appVersion: '',
          dnu: '',
          impUv: '',
          impCnt: '',
          income: '',
          appDay2: '',
          appDay3: '',
          appDay7: '',
          appDay14: '',
          appDay30: '',
          uiDay2: '',
          uiDay3: '',
          uiDay7: '',
          uiDay14: '',
          uiDay30: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          channelName: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          marketCode: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          advertiseId: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          brand: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          model: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          androidVersion: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          appVersion: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          dnu: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          impUv: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          impCnt: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          income: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          appDay2: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          appDay3: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          appDay7: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          appDay14: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          appDay30: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          uiDay2: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          uiDay3: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          uiDay7: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          uiDay14: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ],
          uiDay30: [
            { required: true, message: '${column.comments}不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.dt = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.dt) {
            this.$http({
              url: this.$http.adornUrl(`/stat/gjnewuserindicatordaily/info/${this.dataForm.dt}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.gjNewUserIndicatorDaily.appId
                this.dataForm.channelName = data.gjNewUserIndicatorDaily.channelName
                this.dataForm.marketCode = data.gjNewUserIndicatorDaily.marketCode
                this.dataForm.advertiseId = data.gjNewUserIndicatorDaily.advertiseId
                this.dataForm.brand = data.gjNewUserIndicatorDaily.brand
                this.dataForm.model = data.gjNewUserIndicatorDaily.model
                this.dataForm.androidVersion = data.gjNewUserIndicatorDaily.androidVersion
                this.dataForm.appVersion = data.gjNewUserIndicatorDaily.appVersion
                this.dataForm.dnu = data.gjNewUserIndicatorDaily.dnu
                this.dataForm.impUv = data.gjNewUserIndicatorDaily.impUv
                this.dataForm.impCnt = data.gjNewUserIndicatorDaily.impCnt
                this.dataForm.income = data.gjNewUserIndicatorDaily.income
                this.dataForm.appDay2 = data.gjNewUserIndicatorDaily.appDay2
                this.dataForm.appDay3 = data.gjNewUserIndicatorDaily.appDay3
                this.dataForm.appDay7 = data.gjNewUserIndicatorDaily.appDay7
                this.dataForm.appDay14 = data.gjNewUserIndicatorDaily.appDay14
                this.dataForm.appDay30 = data.gjNewUserIndicatorDaily.appDay30
                this.dataForm.uiDay2 = data.gjNewUserIndicatorDaily.uiDay2
                this.dataForm.uiDay3 = data.gjNewUserIndicatorDaily.uiDay3
                this.dataForm.uiDay7 = data.gjNewUserIndicatorDaily.uiDay7
                this.dataForm.uiDay14 = data.gjNewUserIndicatorDaily.uiDay14
                this.dataForm.uiDay30 = data.gjNewUserIndicatorDaily.uiDay30
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/gjnewuserindicatordaily/${!this.dataForm.dt ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'dt': this.dataForm.dt || undefined,
                'appId': this.dataForm.appId,
                'channelName': this.dataForm.channelName,
                'marketCode': this.dataForm.marketCode,
                'advertiseId': this.dataForm.advertiseId,
                'brand': this.dataForm.brand,
                'model': this.dataForm.model,
                'androidVersion': this.dataForm.androidVersion,
                'appVersion': this.dataForm.appVersion,
                'dnu': this.dataForm.dnu,
                'impUv': this.dataForm.impUv,
                'impCnt': this.dataForm.impCnt,
                'income': this.dataForm.income,
                'appDay2': this.dataForm.appDay2,
                'appDay3': this.dataForm.appDay3,
                'appDay7': this.dataForm.appDay7,
                'appDay14': this.dataForm.appDay14,
                'appDay30': this.dataForm.appDay30,
                'uiDay2': this.dataForm.uiDay2,
                'uiDay3': this.dataForm.uiDay3,
                'uiDay7': this.dataForm.uiDay7,
                'uiDay14': this.dataForm.uiDay14,
                'uiDay30': this.dataForm.uiDay30
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
