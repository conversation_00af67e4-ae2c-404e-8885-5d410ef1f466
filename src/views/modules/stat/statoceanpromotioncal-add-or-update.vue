<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用ID" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用ID"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="广告主ID" prop="advertiseId">
      <el-input v-model="dataForm.advertiseId" placeholder="广告主ID"></el-input>
    </el-form-item>
    <el-form-item label="广告计划ID" prop="aid">
      <el-input v-model="dataForm.aid" placeholder="广告计划ID"></el-input>
    </el-form-item>
    <el-form-item label="激活回传率" prop="activatePercent">
      <el-input v-model="dataForm.activatePercent" placeholder="激活回传率"></el-input>
    </el-form-item>
    <el-form-item label="关键行为率" prop="addictionPercent">
      <el-input v-model="dataForm.addictionPercent" placeholder="关键行为率"></el-input>
    </el-form-item>
    <el-form-item label="激活数" prop="dnu">
      <el-input v-model="dataForm.dnu" placeholder="激活数"></el-input>
    </el-form-item>
    <el-form-item label="回传激活数" prop="activateCount">
      <el-input v-model="dataForm.activateCount" placeholder="回传激活数"></el-input>
    </el-form-item>
    <el-form-item label="回传关键行为数" prop="addictionCount">
      <el-input v-model="dataForm.addictionCount" placeholder="回传关键行为数"></el-input>
    </el-form-item>
    <el-form-item label="arpu" prop="arpu">
      <el-input v-model="dataForm.arpu" placeholder="arpu"></el-input>
    </el-form-item>
    <el-form-item label="roi" prop="roi">
      <el-input v-model="dataForm.roi" placeholder="roi"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="实际关键行为率" prop="actualAdditonPercent">
      <el-input v-model="dataForm.actualAdditonPercent" placeholder="实际关键行为率"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          day: '',
          advertiseId: '',
          aid: '',
          activatePercent: '',
          addictionPercent: '',
          dnu: '',
          activateCount: '',
          addictionCount: '',
          arpu: '',
          roi: '',
          createdAt: '',
          updatedAt: '',
          actualAdditonPercent: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用ID不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          advertiseId: [
            { required: true, message: '广告主ID不能为空', trigger: 'blur' }
          ],
          aid: [
            { required: true, message: '广告计划ID不能为空', trigger: 'blur' }
          ],
          activatePercent: [
            { required: true, message: '激活回传率不能为空', trigger: 'blur' }
          ],
          addictionPercent: [
            { required: true, message: '关键行为率不能为空', trigger: 'blur' }
          ],
          dnu: [
            { required: true, message: '激活数不能为空', trigger: 'blur' }
          ],
          activateCount: [
            { required: true, message: '回传激活数不能为空', trigger: 'blur' }
          ],
          addictionCount: [
            { required: true, message: '回传关键行为数不能为空', trigger: 'blur' }
          ],
          arpu: [
            { required: true, message: 'arpu不能为空', trigger: 'blur' }
          ],
          roi: [
            { required: true, message: 'roi不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          actualAdditonPercent: [
            { required: true, message: '实际关键行为率不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/statoceanpromotioncal/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.statOceanPromotionCal.appId
                this.dataForm.day = data.statOceanPromotionCal.day
                this.dataForm.advertiseId = data.statOceanPromotionCal.advertiseId
                this.dataForm.aid = data.statOceanPromotionCal.aid
                this.dataForm.activatePercent = data.statOceanPromotionCal.activatePercent
                this.dataForm.addictionPercent = data.statOceanPromotionCal.addictionPercent
                this.dataForm.dnu = data.statOceanPromotionCal.dnu
                this.dataForm.activateCount = data.statOceanPromotionCal.activateCount
                this.dataForm.addictionCount = data.statOceanPromotionCal.addictionCount
                this.dataForm.arpu = data.statOceanPromotionCal.arpu
                this.dataForm.roi = data.statOceanPromotionCal.roi
                this.dataForm.createdAt = data.statOceanPromotionCal.createdAt
                this.dataForm.updatedAt = data.statOceanPromotionCal.updatedAt
                this.dataForm.actualAdditonPercent = data.statOceanPromotionCal.actualAdditonPercent
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/statoceanpromotioncal/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'day': this.dataForm.day,
                'advertiseId': this.dataForm.advertiseId,
                'aid': this.dataForm.aid,
                'activatePercent': this.dataForm.activatePercent,
                'addictionPercent': this.dataForm.addictionPercent,
                'dnu': this.dataForm.dnu,
                'activateCount': this.dataForm.activateCount,
                'addictionCount': this.dataForm.addictionCount,
                'arpu': this.dataForm.arpu,
                'roi': this.dataForm.roi,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt,
                'actualAdditonPercent': this.dataForm.actualAdditonPercent
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
