<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="选择应用">
        <app-select
          ref="appSelect"
          :has-all="false"
          :auto-init="false"
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item label="日期">
        <el-date-picker
          v-model="dataForm.day"
          type="date"
          value-format="yyyyMMdd"
          placeholder="选择日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary">查询</el-button>
        <!--<el-button v-if="isAuth('stat:statbusinessdaily:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('stat:statbusinessdaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <div class="table-height">
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        style="width: 100%;"
        height="100%"
      >
        <!--<af-table-column-->
        <!--  type="selection"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  width="50"-->
        <!--/>-->
        <!--<af-table-column-->
        <!--  prop="id"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="主键"-->
        <!--/>-->
        <!--<af-table-column-->
        <!--  prop="appId"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="应用"-->
        <!--  width="120"-->
        <!--&gt;-->
        <!--  <template slot-scope="{ row }">-->
        <!--    <el-tag-->
        <!--      v-if="$store.state.ad.appList && $store.state.ad.appList.length"-->
        <!--    >-->
        <!--      {{-->
        <!--        row.appId | getListLabel($store.state.ad.appList, 'code', 'name')-->
        <!--      }}-->
        <!--    </el-tag>-->
        <!--  </template>-->
        <!--</af-table-column>-->
        <!--fixed="left"-->
        <af-table-column
          prop="day"
          header-align="center"
          align="center"
          label="日期"
        >
          <template slot-scope="{ row }">
            <span>{{ $dayjs(String(row.day)).format('MM-DD') }}</span>
          </template>
        </af-table-column>
        <af-table-column
          prop="newCount"
          header-align="center"
          align="center"
          label="新增"
        />
        <af-table-column
          prop="aliveCount"
          header-align="center"
          align="center"
          label="活跃"
        />
        <af-table-column
          prop="newArriveFrontCount"
          header-align="center"
          align="center"
          label="新增首页"
        />
        <af-table-column
          prop="aliveArriveFrontCount"
          header-align="center"
          align="center"
          label="活跃首页"
        />
        <af-table-column
          prop="newArriveFrontRate"
          header-align="center"
          align="center"
          label="新增首页率"
        >
          <template slot-scope="{ row }">
            <span>
              {{ parseInt(row.newArriveFrontRate || 0) + '%' }}
            </span>
          </template>
        </af-table-column>
        <af-table-column
          prop="aliveArriveFrontRate"
          header-align="center"
          align="center"
          label="活跃首页率"
        >
          <template slot-scope="{ row }">
            <span>
              {{ parseInt(row.aliveArriveFrontRate || 0) + '%' }}
            </span>
          </template>
        </af-table-column>
        <af-table-column
          prop="attributionNum"
          header-align="center"
          align="center"
          label="归因数"
          v-if="false"
        />
        <af-table-column
          prop="attributionRate"
          header-align="center"
          align="center"
          label="归因率"
          v-if="false"
        >
          <template slot-scope="{ row }">
            <span>
              {{
                row.attributionRate
                  ? (row.attributionRate * 100).toFixed(2) + '%'
                  : '-'
              }}
            </span>
          </template>
        </af-table-column>
        <!--<af-table-column-->
        <!--  prop="externalSceneCount"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="外部触发"-->
        <!--  width="80"-->
        <!--/>-->
        <!--<af-table-column-->
        <!--  prop="externalSceneRate"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="外部触发率"-->
        <!--  width="90"-->
        <!--&gt;-->
        <!--  <template slot-scope="{ row }">-->
        <!--    <span>-->
        <!--      {{ parseInt(row.externalSceneRate || 0) + '%' }}-->
        <!--    </span>-->
        <!--  </template>-->
        <!--</af-table-column>-->
        <af-table-column
          prop="csjIncome"
          header-align="center"
          align="center"
          :label="csjIncomeLabel"
        />
        <af-table-column
          prop="ylhIncome"
          header-align="center"
          align="center"
          :label="ylhIncomeLabel"
        />
        <af-table-column
          prop="ksIncome"
          header-align="center"
          align="center"
          :label="ksIncomeLabel"
        />
        <af-table-column
          :prop="baiduIncomeProp"
          header-align="center"
          align="center"
          v-if="false"
          :label="baiduIncomeLabel"
        />

        <!--<af-table-column-->
        <!--  prop="externalBaiduInfoIncome"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="外部资讯"-->
        <!--  width="80"-->
        <!--/>-->
        <af-table-column
          prop="externalBaiduInfoIncome"
          header-align="center"
          align="center"
          label="bigo"
          v-if="false"
        />
        <af-table-column
          prop="baiduIncome"
          header-align="center"
          align="center"
          label="海外华为"
          v-if="false"
        />
        <af-table-column
          prop="yandexIncome"
          header-align="center"
          align="center"
          label="yandex"
          v-if="false"
        />
        <af-table-column
          prop="heliumIncome"
          header-align="center"
          align="center"
          label="helium"
          v-if="false"
        />
        <af-table-column
          prop="chartboostIncome"
          header-align="center"
          align="center"
          label="chartboost"
          v-if="false"
        />
        <af-table-column
          prop="mintIncome"
          header-align="center"
          align="center"
          label="Mintegral"
          v-if="false"
        />
        <af-table-column
          prop="huaweiIncome"
          header-align="center"
          align="center"
          label="华为"
        />
        <af-table-column
          prop="adImpressions"
          header-align="center"
          align="center"
          label="广告展示"
        />
        <!--<af-table-column-->
        <!--  prop="lockScreenImpressions"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="锁屏展示"-->
        <!--/>-->
        <af-table-column
          prop="aiPu"
          header-align="center"
          align="center"
          label="广告AIPU"
        />
        <!--<af-table-column-->
        <!--  prop="lockScreenAiPu"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="锁屏AIPU"-->
        <!--  width="90"-->
        <!--/>-->
        <af-table-column
          prop="arPu"
          header-align="center"
          align="center"
          label="ARPU"
        />
        <af-table-column
          prop="ecpm"
          header-align="center"
          align="center"
          label="广告ECPM"
        />
        <!--<af-table-column-->
        <!--  prop="lockScreenEcpm"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="锁屏ECPM"-->
        <!--  width="90"-->
        <!--/>-->
        <af-table-column
          prop="cpaByNew"
          header-align="center"
          align="center"
          label="新增CPA"
        />

        <af-table-column
          prop="kwaiCpa"
          header-align="center"
          align="center"
          label="快手Cpa"
          v-if="false"
        />
        <af-table-column
          prop="ttCpa"
          header-align="center"
          align="center"
          label="ttCpa"
          v-if="false"
        />
        <af-table-column
          prop="mintCpa"
          header-align="center"
          align="center"
          label="mintCpa"
          v-if="false"
        />
        <af-table-column
          prop="ggCpa"
          header-align="center"
          align="center"
          label="ggCpa"
          v-if="false"
        />
        <af-table-column
          prop="fbCpa"
          header-align="center"
          align="center"
          label="fbCpa"
          v-if="false"
        />
        <af-table-column
          prop="bigoCpa"
          header-align="center"
          align="center"
          label="bigoCpa"
          v-if="false"
        />

        <af-table-column
          prop="cpaByArrive"
          header-align="center"
          align="center"
          label="激活CPA"
        />
        <af-table-column
          prop="roi"
          header-align="center"
          align="center"
          label="累计ROI"
        />

        <!--<af-table-column-->
        <!--  prop="coinGrantRatio"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="金币发放比例"-->
        <!--  width="60"-->
        <!--/>-->
        <!--<af-table-column-->
        <!--  prop="withdrawRatio"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="提现比例"-->
        <!--  width="60"-->
        <!--/>-->
        <!--<af-table-column-->
        <!--  prop="withdrawAmount"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="每日提现金额"-->
        <!--  width="60"-->
        <!--/>-->

        <af-table-column
          prop="withdrawAmount"
          header-align="center"
          align="center"
          label="每日提现金额"
          v-if="false"
        />

        <af-table-column
          prop="kwaiCost"
          header-align="center"
          align="center"
          label="快手成本"
          v-if="false"
        />
        <af-table-column
          prop="ttCost"
          header-align="center"
          align="center"
          label="tt成本"
          v-if="false"
        />
        <af-table-column
          prop="mintCost"
          header-align="center"
          align="center"
          label="mint成本"
          v-if="false"
        />
        <af-table-column
          prop="ggCost"
          header-align="center"
          align="center"
          label="gg成本"
          v-if="false"
        />
        <af-table-column
          prop="fbCost"
          header-align="center"
          align="center"
          label="fb成本"
          v-if="false"
        />
        <af-table-column
          prop="bigoCost"
          header-align="center"
          align="center"
          label="bigo成本"
          v-if="false"
        />
        <af-table-column
          prop="huaweiCost"
          header-align="center"
          align="center"
          label="华为成本"
          v-if="false"
        />
        <af-table-column
          prop="vivoCost"
          header-align="center"
          align="center"
          label="vivo成本"
          v-if="false"
        />
        <af-table-column
          prop="oppoCost"
          header-align="center"
          align="center"
          label="oppo成本"
          v-if="false"
        />

        <af-table-column
          prop="cost"
          header-align="center"
          align="center"
          label="成本"
        />
        <af-table-column
          prop="totalRevenue"
          header-align="center"
          align="center"
          label="总收入"
        />
        <af-table-column
          prop="profit"
          header-align="center"
          align="center"
          label="毛利"
        />
        <!--<af-table-column-->
        <!--  prop="createdAt"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="创建时间"-->
        <!--  width="90"-->
        <!--&gt;-->
        <!--  <template slot-scope="{ row }">-->
        <!--    <span>{{ $dayjs(String(row.createdAt)).format('YYYY-MM-DD') }}</span>-->
        <!--  </template>-->
        <!--</af-table-column>-->
        <!--<af-table-column-->
        <!--  prop="updatedAt"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="更新时间"-->
        <!--  width="135"-->
        <!--/>-->
        <af-table-column
          header-align="center"
          align="center"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="addOrUpdateHandle(scope.row.id)"
            >
              修改
            </el-button>
            <!--<el-button-->
            <!--  type="text"-->
            <!--  size="small"-->
            <!--  @click="deleteHandle(scope.row.id)"-->
            <!--&gt;-->
            <!--  删除-->
            <!--</el-button>-->
          </template>
        </af-table-column>
      </el-table>
    </div>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './statbusinessdaily-add-or-update'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        day: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      appList: [],
    }
  },
  computed: {
    currentApp() {
      return this.$store.state.ad.appList.find(
        it => it.id === this.$store.state.ad.appId
      )
    },
    csjIncomeLabel() {
      return 'OPPO'
      /*return this.currentApp && this.currentApp.groupId === 12
        ? 'pangle'
        : '穿山甲'*/
    },
    ylhIncomeLabel() {
      return 'VIVO'
      /*return this.currentApp && this.currentApp.groupId === 12
        ? 'unity'
        : '优量汇'*/
    },
    ksIncomeLabel() {
      return 'Honor'
      /*return this.currentApp && this.currentApp.groupId === 12
        ? 'topon'
        : '快手'*/
    },
    baiduIncomeLabel() {
      return this.currentApp && this.currentApp.groupId === 12
        ? 'Applovin'
        : '百度'
    },
    baiduIncomeProp() {
      return this.currentApp && this.currentApp.groupId === 12
        ? 'apploveIncome'
        : 'baiduIncome'
    },
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    this.$refs.appSelect.init()
    // this.getAppList()
  },
  filters: {
    floatFormatNum(value, len = 2) {
      return parseFloat(value.toFixed(len)).toLocaleString()
    },
    intFormatNum(value, len = 2) {
      return parseInt(value.toFixed(len)).toLocaleString()
    },
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const res = this.$store.state.ad.appList.find(
        it => it.id === this.$store.state.ad.appId
      )

      this.$http({
        url: this.$http.adornUrl('/stat/statbusinessdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appId: res && res.code ? res.code : '', // 这个字段表面上看是交appId，但实际上却是code
          day: this.dataForm.day || '',
          key: this.dataForm.key || '',
        }),
      }).then(({ data }) => {
        if (data && data.code === 0 && data.page && data.page.list) {
          this.dataList = data.page.list.map(it => {
            let item = { ...it }

            item.profit = parseInt(
              (
                (it.totalRevenue || 0) -
                (it.cost || 0) -
                (it.withdrawAmount || 0)
              ).toFixed(2)
            ).toLocaleString()

            return item
          })
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      console.log('currentApp', this.currentApp)
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/statbusinessdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.table-height {
  height: Calc(100vh - 135px - 97px);
}
</style>
