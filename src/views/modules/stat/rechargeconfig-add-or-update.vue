<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="115px"
    >
      <el-form-item label="购买档次名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="购买档次名称" />
      </el-form-item>
      <el-form-item label="购买档次描述" prop="refer">
        <el-input v-model="dataForm.refer" placeholder="购买档次名称" />
      </el-form-item>
      <el-form-item label="商品price" prop="product">
        <el-input v-model="dataForm.product" placeholder="商品price" />
      </el-form-item>
      <el-form-item label="空中云汇价格ID" prop="airPrice">
        <el-input v-model="dataForm.airPrice" placeholder="空中云汇价格ID" />
      </el-form-item>
      <el-form-item label="paypal ID" prop="paypalPlan">
        <el-input v-model="dataForm.paypalPlan" placeholder="paypal ID" />
      </el-form-item>
      <el-form-item label="购买价格" prop="price">
        <el-input v-model="dataForm.price" placeholder="购买价格,单位美元">
          <template slot="prepend">
            <span>$</span>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="购买所得金币" prop="coins">
        <el-input v-model="dataForm.coins" placeholder="购买所得金币" />
      </el-form-item>
      <el-form-item label="序号" prop="serialNo">
        <el-input v-model="dataForm.serialNo" placeholder="序号" />
      </el-form-item>
      <el-form-item label="赠送的金币" prop="extraCoins">
        <el-input v-model="dataForm.extraCoins" placeholder="赠送的金币" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <arr-select
          :list="statusList"
          v-model="dataForm.status"
          label-key="label"
          value-key="value"
          placeholder="状态"
        />
      </el-form-item>
      <el-form-item label="是否终身" prop="isPermanent">
        <arr-select
          :list="permanentList"
          v-model="dataForm.isPermanent"
          label-key="label"
          value-key="value"
          placeholder="是否终身"
        />
      </el-form-item>

      <el-form-item label="是否订阅" prop="isSubscription">
        <arr-select
          :list="subscriptionList"
          v-model="dataForm.isSubscription"
          label-key="label"
          value-key="value"
          placeholder="是否订阅"
        />
      </el-form-item>

      <el-form-item label="原始价格" prop="originPrice">
        <el-input v-model="dataForm.originPrice" placeholder="原始价格" />
      </el-form-item>
      <el-form-item label="支付类型" prop="type">
        <arr-select
          :list="payList"
          v-model="dataForm.type"
          label-key="label"
          value-key="value"
          placeholder="支付类型"
        />
      </el-form-item>
      <el-form-item label="方案ID" prop="appId">
        <el-input v-model.number="dataForm.appId" placeholder="方案ID" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      payList: [
        { label: '金币', value: 0 },
        { label: '周卡', value: 1 },
        { label: '月卡', value: 2 },
        { label: '年卡', value: 3 },
        { label: '永久', value: 4 },
      ],
      permanentList: [
        { label: '非终身', value: 0 },
        { label: '终身', value: 1 },
      ],
      subscriptionList: [
        { label: '非订阅', value: 0 },
        { label: '订阅', value: 1 },
      ],
      statusList: [
        { label: '无效', value: 0 },
        { label: '生效', value: 1 },
      ],
      dataForm: {
        id: 0,
        name: '',
        refer: '',
        product: '',
        airPrice: '',
        paypalPlan: '',
        price: '',
        coins: '',
        serialNo: '',
        extraCoins: '',
        status: '',
        isPermanent: '',
        isSubscription: '',
        originPrice: '',
        type: '',
        appId: '',
      },
      dataRule: {
        name: [
          { required: true, message: '购买档次名称不能为空', trigger: 'blur' },
        ],
        // refer: [{ required: true, message: '不能为空', trigger: 'blur' }],
        product: [
          { required: true, message: '商品price不能为空', trigger: 'blur' },
        ],
        // paypalPlan: [{ required: true, message: '不能为空', trigger: 'blur' }],
        price: [
          {
            required: true,
            message: '购买价格 单位美元不能为空',
            trigger: 'blur',
          },
        ],
        coins: [
          { required: true, message: '购买所得金币不能为空', trigger: 'blur' },
        ],
        serialNo: [
          { required: true, message: '序号不能为空', trigger: 'blur' },
        ],
        // extraCoins: [
        //   { required: true, message: '赠送的金币不能为空', trigger: 'blur' },
        // ],
        status: [
          {
            required: true,
            message: '1：生效，0：无效不能为空',
            trigger: 'blur',
          },
        ],
        isPermanent: [
          {
            required: true,
            message: '是否是终身会员：1：是，0：不是不能为空',
            trigger: 'blur',
          },
        ],
        isSubscription: [
          {
            required: true,
            message: '是否订阅：1：是，0：不是不能为空',
            trigger: 'blur',
          },
        ],
        originPrice: [
          { required: true, message: '原始价格不能为空', trigger: 'blur' },
        ],
        type: [{ required: true, message: '不能为空', trigger: 'blur' }],
        appId: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/rechargeconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.name = data.rechargeConfig.name
              this.dataForm.product = data.rechargeConfig.product
              this.dataForm.price = data.rechargeConfig.price
              this.dataForm.coins = data.rechargeConfig.coins
              this.dataForm.serialNo = data.rechargeConfig.serialNo
              this.dataForm.extraCoins = data.rechargeConfig.extraCoins
              this.dataForm.status = data.rechargeConfig.status
              this.dataForm.isPermanent = data.rechargeConfig.isPermanent
              this.dataForm.isSubscription = data.rechargeConfig.isSubscription
              this.dataForm.originPrice = data.rechargeConfig.originPrice
              this.dataForm.type = data.rechargeConfig.type
              this.dataForm.appId = data.rechargeConfig.appId
              this.dataForm.refer = data.rechargeConfig.refer
              this.dataForm.paypalPlan = data.rechargeConfig.paypalPlan
              this.dataForm.airPrice = data.rechargeConfig.airPrice
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/rechargeconfig/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              product: this.dataForm.product,
              price: this.dataForm.price,
              coins: this.dataForm.coins,
              serialNo: this.dataForm.serialNo,
              extraCoins: this.dataForm.extraCoins,
              status: this.dataForm.status,
              isPermanent: this.dataForm.isPermanent,
              isSubscription: this.dataForm.isSubscription,
              originPrice: this.dataForm.originPrice,
              type: this.dataForm.type,
              appId: this.dataForm.appId,
              refer: this.dataForm.refer,
              paypalPlan: this.dataForm.paypalPlan,
              airPrice: this.dataForm.airPrice,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
