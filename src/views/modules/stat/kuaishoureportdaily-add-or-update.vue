<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="媒体id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="媒体id"></el-input>
    </el-form-item>
    <el-form-item label="系统应用id" prop="appCode">
      <el-input v-model="dataForm.appCode" placeholder="系统应用id"></el-input>
    </el-form-item>
    <el-form-item label="应用名称" prop="appName">
      <el-input v-model="dataForm.appName" placeholder="应用名称"></el-input>
    </el-form-item>
    <el-form-item label="报表日期" prop="date">
      <el-input v-model="dataForm.date" placeholder="报表日期"></el-input>
    </el-form-item>
    <el-form-item label="广告位id" prop="positionId">
      <el-input v-model="dataForm.positionId" placeholder="广告位id"></el-input>
    </el-form-item>
    <el-form-item label="广告展示" prop="impression">
      <el-input v-model="dataForm.impression" placeholder="广告展示"></el-input>
    </el-form-item>
    <el-form-item label="点击" prop="click">
      <el-input v-model="dataForm.click" placeholder="点击"></el-input>
    </el-form-item>
    <el-form-item label="分成" prop="share">
      <el-input v-model="dataForm.share" placeholder="分成"></el-input>
    </el-form-item>
    <el-form-item label="点击率" prop="ctr">
      <el-input v-model="dataForm.ctr" placeholder="点击率"></el-input>
    </el-form-item>
    <el-form-item label="千次展现收益" prop="ecpm">
      <el-input v-model="dataForm.ecpm" placeholder="千次展现收益"></el-input>
    </el-form-item>
    <el-form-item label="广告位类型" prop="adStyle">
      <el-input v-model="dataForm.adStyle" placeholder="广告位类型"></el-input>
    </el-form-item>
    <el-form-item label="context_provider" prop="contextProvider">
      <el-input v-model="dataForm.contextProvider" placeholder="context_provider"></el-input>
    </el-form-item>
    <el-form-item label="报表日期数字格式" prop="day">
      <el-input v-model="dataForm.day" placeholder="报表日期数字格式"></el-input>
    </el-form-item>
    <el-form-item label="创建日期" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建日期"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          appCode: '',
          appName: '',
          date: '',
          positionId: '',
          impression: '',
          click: '',
          share: '',
          ctr: '',
          ecpm: '',
          adStyle: '',
          contextProvider: '',
          day: '',
          createdAt: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '媒体id不能为空', trigger: 'blur' }
          ],
          appCode: [
            { required: true, message: '系统应用id不能为空', trigger: 'blur' }
          ],
          appName: [
            { required: true, message: '应用名称不能为空', trigger: 'blur' }
          ],
          date: [
            { required: true, message: '报表日期不能为空', trigger: 'blur' }
          ],
          positionId: [
            { required: true, message: '广告位id不能为空', trigger: 'blur' }
          ],
          impression: [
            { required: true, message: '广告展示不能为空', trigger: 'blur' }
          ],
          click: [
            { required: true, message: '点击不能为空', trigger: 'blur' }
          ],
          share: [
            { required: true, message: '分成不能为空', trigger: 'blur' }
          ],
          ctr: [
            { required: true, message: '点击率不能为空', trigger: 'blur' }
          ],
          ecpm: [
            { required: true, message: '千次展现收益不能为空', trigger: 'blur' }
          ],
          adStyle: [
            { required: true, message: '广告位类型不能为空', trigger: 'blur' }
          ],
          contextProvider: [
            { required: true, message: 'context_provider不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '报表日期数字格式不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建日期不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/kuaishoureportdaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.kuaishouReportDaily.appId
                this.dataForm.appCode = data.kuaishouReportDaily.appCode
                this.dataForm.appName = data.kuaishouReportDaily.appName
                this.dataForm.date = data.kuaishouReportDaily.date
                this.dataForm.positionId = data.kuaishouReportDaily.positionId
                this.dataForm.impression = data.kuaishouReportDaily.impression
                this.dataForm.click = data.kuaishouReportDaily.click
                this.dataForm.share = data.kuaishouReportDaily.share
                this.dataForm.ctr = data.kuaishouReportDaily.ctr
                this.dataForm.ecpm = data.kuaishouReportDaily.ecpm
                this.dataForm.adStyle = data.kuaishouReportDaily.adStyle
                this.dataForm.contextProvider = data.kuaishouReportDaily.contextProvider
                this.dataForm.day = data.kuaishouReportDaily.day
                this.dataForm.createdAt = data.kuaishouReportDaily.createdAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/kuaishoureportdaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'appCode': this.dataForm.appCode,
                'appName': this.dataForm.appName,
                'date': this.dataForm.date,
                'positionId': this.dataForm.positionId,
                'impression': this.dataForm.impression,
                'click': this.dataForm.click,
                'share': this.dataForm.share,
                'ctr': this.dataForm.ctr,
                'ecpm': this.dataForm.ecpm,
                'adStyle': this.dataForm.adStyle,
                'contextProvider': this.dataForm.contextProvider,
                'day': this.dataForm.day,
                'createdAt': this.dataForm.createdAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
