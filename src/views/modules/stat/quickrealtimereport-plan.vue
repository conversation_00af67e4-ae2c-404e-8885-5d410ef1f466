<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="70%"
    :modal-append-to-body="false"
    :append-to-body="false"
  >
    <div>
      <div ref="echarts" style="width: 100%;height: 300px"></div>
      <el-table
        ref="myTable"
        class="adapter-height"
        :data="dataList"
        border
        v-loading="dataListLoading"
        :header-cell-style="{ background: '#f8f9fa' }"
        style="width: 100%"
      >
        <el-table-column
          v-for="data in dataForm"
          :key="data.prop"
          :prop="data.prop"
          header-align="center"
          align="center"
          :label="data.label"
          min-width="90"
          :fixed="data.fixed"
          :show-overflow-tooltip="data.showOverflowTooltip"
        >
          <template slot-scope="scope">
            <div :style="data.style && data.style(scope.row[data.prop])">
              {{ data.formatter ? data.formatter(scope.row[data.prop]) : scope.row[data.prop] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="100"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="addCallback(scope.row)"
            >
              回传
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="editOCPC(scope.row)"
            >
              出价
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </el-dialog>
</template>

<script>
import { mixinElTableAdapterHeight } from '@/mixins'
import Decimal from 'decimal.js'
import * as echarts from 'echarts'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      visible: false,
      title: '',
      dataList: [],
      dataListLoading: false,
      initChartOption: {
        title: {
          text: 'ROI曲线'
        },
        tooltip: {
          show: true,
          trigger: 'axis',
        },
        xAxis: {
          type: 'time',
          name: '时间',
          boundaryGap: true
        },
        yAxis: {
          type: 'value',
          name: 'ROI',
          min: function (value) {
            return Math.max(value.min - 0.1, 0).toFixed(1)
          },
          max: function (value) {
            return Math.max(value.max + 0.1, 1).toFixed(1)
          },
        },
        series: [
          {
            name: 'ROI',
            type: 'line',
            smooth: true
          }
        ]
      },
      groupStatus: {
        0: '启动中',
        1: '暂停中',
        2: '应用状态异常',
        3: '快应用状态异常',
        4: '已结束',
        5: '未开始',
        6: '不在推广时段',
        7: '规格投放暂停',
        8: '计划暂停'
      },
      dataForm: [
        { prop: 'dt', label: '日期', fixed: 'left' },
        // { prop: 'accountName', label: '账户名称' },
        // { prop: 'brand', label: '品牌' },
        // { prop: 'accountId', label: '账户ID', fixed: 'left' },
        { prop: 'planId', label: '计划ID', fixed: 'left' },
        { prop: 'groupId', label: '广告组ID', fixed: 'left', showOverflowTooltip: true },
        // { prop: 'channel', label: '投放平台' },
        // { prop: 'appId', label: '应用ID' },
        // { prop: 'unionType', label: '投放媒体' },
        // { prop: 'agent', label: '代理商' },
        // { prop: 'totalBalance', label: '余额' },
        // { prop: 'budget', label: '预算' },
        { prop: 'cost', label: '消耗' },
        { prop: 'ocpcPrice', label: '转化出价' },
        { prop: 'costExposureNum', label: '曝光' },
        { prop: 'adClickNum', label: '点击' },
        { prop: 'costEcpm', label: 'ECPM' },
        { prop: 'cpc', label: 'CPC' },
        { prop: 'ctr', label: 'CTR', formatter: this.toPercentage },
        { prop: 'formCnt', label: '表单提交量' },
        { prop: 'formPrice', label: 'CPA' },
        { prop: 'conversionRate', label: 'CVR', formatter: this.toPercentage2 },
        // { prop: 'returnRate', label: '回传率' },
        { prop: 'income', label: '预估收益' },
        { prop: 'xincome', label: '开屏收入' },
        // { prop: 'exposureNum', label: '曝光总数' },
        // { prop: 'ipu', label: 'IPV' },
        // { prop: 'inCtr', label: 'CTR' },
        // { prop: 'attrRate', label: '归因率' },
        { prop: 'ecpm', label: 'ECPM' },
        { prop: 'zong', label: '总收入' },
        { prop: 'roi', label: 'ROI', fixed: 'right', style: this.roiStyle },
        { prop: 'groupStatus', label: '状态', fixed: 'right', formatter: (value) => this.groupStatus[value] },
      ],
    }
  },
  methods: {
    init(row) {
      this.visible = true
      this.title = row.accountId
      this.$nextTick(() => {
        this.getROIData(row.accountId, row.dt)
        this.getDataList(row.accountId, row.dt)
      })
    },
    getROIData(accountId, dt) {
      var chart = echarts.init(this.$refs.echarts, null, this.initChartOption);
      window.addEventListener('resize', chart.resize)
      chart.clear()
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimereport/line_list'),
        method: 'get',
        params: this.$http.adornParams({
          accountId: accountId,
          dt: dt
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          const option = {
            ...this.initChartOption,
            series: [
              {
                data: data.list.filter(it => it.roi > 0 && it.roi <= 5).map(it => [it.timePoint, it.roi || 0]),
                type: 'line',
                smooth: true
              }
            ]
          }
          chart.setOption(option)
        }
      })
    },
    getDataList(id, dt) {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimereport/group_list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 1000,
          accountId: id,
          dt: dt
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.list
            .map(it => {
              if (it.adClickNum > 0) {
                it.cpa = (it.cost / (it.adClickNum * it.conversionRate)).toFixed(2)
              }
              it.zong = this.addEarnings(it.income, it.xincome)
              return it
            })
            .sort((a, b) => {
              let dt = new Date(b.dt) - new Date(a.dt)
              if (dt !== 0) {
                return dt
              }
              return b.cost - a.cost
            })
        } else {
          this.dataList = []
        }
        this.dataListLoading = false
      })
    },
    closePlan(row) {
      this.$confirm(`确定进行[关闭计划]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/close_plan'),
          method: 'get',
          params: this.$http.adornParams({
            accountId: row.accountId,
            planId: row.planId,
          }),
        }).then(({ data }) => {
          if (data && data.ret === 0) {
            this.$message({
              type: 'success',
              message: '操作成功',
            })
          } else {
            this.$message({
              type: 'error',
              message: data.msg || '操作失败',
            })
          }
          this.dataListLoading = false
        })
      }).catch(() => {})
    },
    addCallback(row) {
      this.$prompt('请输入需要手动回传的数量', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputErrorMessage: '数量格式不正确',
        inputValue: '',
        inputType: 'number',
        inputPlaceholder: '手动回传的数量'
      }).then(({ value }) => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/add_callback'),
          method: 'get',
          params: this.$http.adornParams({
            accountId: row.accountId,
            groupId: row.groupId,
            num: value,
          }),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              type: 'success',
              message: '操作成功',
            })
          } else {
            this.$message({
              type: 'error',
              message: data.msg || '操作失败',
            })
          }
          this.dataListLoading = false
        })
      }).catch(() => {
      });
    },
    editOCPC(row) {
      this.$prompt('请输入需要修改的转化出价', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.ocpcPrice,
        inputPlaceholder: '转化出价'
      }).then(({ value }) => {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/stat/quickrealtimereport/edit_ocpc'),
          method: 'get',
          params: this.$http.adornParams({
            accountId: row.accountId,
            groupId: row.groupId,
            ocpc: value,
          }),
        }).then(({ data }) => {
          if (data.data && data.data.ret === 0) {
            this.$message({
              type: 'success',
              message: '操作成功',
            })
          } else {
            this.$message({
              type: 'error',
              message: data.data.msg || '操作失败',
            })
          }
          this.dataListLoading = false
        })
      }).catch(() => {
      });
    },
    addEarnings(a, b) {
      if (!a && !b) {
        return 0
      }
      if (!b) {
        return a.toFixed(2)
      }
      return new Decimal(a).plus(b).toNumber().toFixed(2)
    },
    toPercentage(value) {
      return `${Math.round(value * 100)}%`
    },

    toPercentage2(value) {
      return `${(value * 100).toFixed(2)}%`
    },
    roiStyle(value) {
      return value >= 1 ? 'color: #f56c6c' : 'color: #67c23a'
    },
    updateEchartsSize() {
      if (this.$refs.echarts) {
        const chartInstance = this.$refs.echarts.__chart__;
        if (chartInstance) {
          chartInstance.resize();
        }
      }
    },
  },
  mounted() {
    this.updateEchartsSize();
    window.addEventListener('resize', this.updateEchartsSize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.updateEchartsSize);
  },
}
</script>

<style scoped>
</style>