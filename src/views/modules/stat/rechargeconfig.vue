<template>
  <div class="mod-config">
    <div
      style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      "
    >
      <el-form
        style="width: 100%; height: 32px; transform: translateY(1px)"
        :inline="true"
        :model="dataForm"
        @keyup.enter.native="getDataList()"
      >
        <el-form-item label="方案ID">
          <el-input v-model="dataForm.appId" placeholder="方案ID" clearable />
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">查询</el-button>
          <el-button
            v-if="isAuth('stat:rechargeconfig:save')"
            type="primary"
            @click="addOrUpdateHandle()"
          >
            新增
          </el-button>
          <el-button
            v-if="isAuth('stat:rechargeconfig:delete')"
            type="danger"
            @click="deleteHandle()"
            :disabled="dataListSelections.length <= 0"
          >
            批量删除
          </el-button>
        </el-form-item>
      </el-form>
      <el-alert>
        组一明显卡片：排序0-10，组二左右卡片：排序11-50，组三金币卡片：排序51-100
      </el-alert>
    </div>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      />
      <el-table-column
        prop="id"
        label="表格ID"
        header-align="center"
        align="center"
        width="80"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="方案ID"
        width="90"
      />
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="购买档次名称"
      />
      <el-table-column
        prop="refer"
        header-align="center"
        align="center"
        label="购买档次提示"
      />
      <el-table-column
        prop="product"
        header-align="center"
        align="center"
        label="商品price"
      />
      <el-table-column
        prop="airPrice"
        header-align="center"
        align="center"
        label="空中云汇价格ID"
      />
      <el-table-column
        prop="paypalPlan"
        header-align="center"
        align="center"
        label="paypal ID"
      />
      <el-table-column
        prop="price"
        header-align="center"
        align="center"
        label="购买价格 单位美元"
      />
      <el-table-column
        prop="coins"
        header-align="center"
        align="center"
        label="购买所得金币"
      />
      <el-table-column
        prop="serialNo"
        header-align="center"
        align="center"
        label="序号"
      />
      <el-table-column
        prop="extraCoins"
        header-align="center"
        align="center"
        label="赠送的金币"
      />

      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          {{ getLabel(statusList, scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="isPermanent"
        header-align="center"
        align="center"
        label="是否终身会员"
      >
        <template slot-scope="scope">
          {{ getLabel(permanentList, scope.row.isPermanent) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="isSubscription"
        header-align="center"
        align="center"
        label="是否订阅"
      >
        <template slot-scope="scope">
          {{ getLabel(subscriptionList, scope.row.isSubscription) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="支付类型"
      >
        <template slot-scope="scope">
          {{ getLabel(payList, scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="originPrice"
        header-align="center"
        align="center"
        label="原始价格"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './rechargeconfig-add-or-update'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        appId: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      payList: [
        { label: '金币', value: 0 },
        { label: '周卡', value: 1 },
        { label: '月卡', value: 2 },
        { label: '年卡', value: 3 },
        { label: '永久', value: 4 },
      ],
      permanentList: [
        { label: '非终身', value: 0 },
        { label: '终身', value: 1 },
      ],
      subscriptionList: [
        { label: '非订阅', value: 0 },
        { label: '订阅', value: 1 },
      ],
      statusList: [
        { label: '无效', value: 0 },
        { label: '生效', value: 1 },
      ],
    }
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/rechargeconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
          appId: this.dataForm.appId,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/stat/rechargeconfig/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getLabel(list, value) {
      const res = list.find(item => item.value === value)
      return res ? res.label : '-'
    },
  },
}
</script>
