<template>
  <div>
    <page-table
      ref="page-table"
      :grid-config="gridOptions"
      :request-collection="request"
      :model-config="modelConfig"
      operate-width="180"
      :show-operate="false"
      :features="[]"
      :before-select="beforeSelect"
    >
      <template #form_appCode>
        <app-select-component
          v-model="selectFormData.appCode"
          :is-show-all="false"
          clearable
          @change="reloadQuery"
        />
      </template>
      <template #form_country>
        <country-select v-model="selectFormData.country" />
      </template>
      <template #form_channel>
        <arr-select
          :list="channelList"
          v-model="selectFormData.channel"
          value-key="value"
          label-key="label"
          clearable
          @change="reloadQuery"
        />
      </template>
      <template #form_advertiserId>
        <el-input
          v-model="selectFormData.advertiserId"
          placeholder="账号"
          clearable
        />
      </template>
      <template #form_showDetail>
        <el-checkbox
          v-model="selectFormData.showDetail"
          true-label="1"
          false-label="0"
          @change="reloadQuery"
        >
          显示账户纬度数据
        </el-checkbox>
      </template>
      <template #table_item_appCode="{row}">
        <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
      </template>
      <template #top>
        <div style="margin-bottom: 10px;">
          <el-alert>均为拉api的数据</el-alert>
        </div>
      </template>
    </page-table>
  </div>
</template>

<script>
import { hwRealtimeDataRequest as request } from '@/api/stat'
import CountrySelect from '@/components/country-select'
import { countryList } from '@/map/sat'

export default {
  components: {
    CountrySelect,
  },
  data() {
    return {
      channelList: [
        { label: 'tiktok', value: 1 },
        { label: 'google', value: 2 },
        { label: 'mint', value: 3 },
        { label: 'facebook', value: 4 },
        { label: '快手', value: 5 },
        { label: 'bigo', value: 6 },
      ],
      request,
      countryList,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 200,
            slots: { default: 'table_item_appCode' },
          },
          { field: 'advertiserId', title: '广告主ID', minWidth: 60 },
          {
            field: 'country',
            title: '国家',
            minWidth: 60,
            formatter: ({ row }) => {
              const res = countryList.find(it => it.country === row.country)
              return res ? res.country_name : ''
            },
          },
          { field: 'impressions', title: '展示数', minWidth: 60 },
          { field: 'conversion', title: '转化数', minWidth: 60 },
          { field: 'click', title: '点击数', minWidth: 60 },
          { field: 'cost', title: '消耗', minWidth: 60 },
          { field: 'cpi', title: '转化成本', minWidth: 60 },
          { field: 'ctr', title: 'ctr', minWidth: 60 },
          { field: 'conversionRate', title: '转化率', minWidth: 150 },
          { field: 'day', title: '日期', minWidth: 150 },
          { field: 'hour', title: '小时', minWidth: 150 },
          { field: 'createdAt', title: '时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            { title: '应用', slots: { default: 'form_appCode' } },
            // { title: '国家', slots: { default: 'form_country' } },
            // { title: '账户', slots: { default: 'form_advertiserId' } },

            {
              title: '媒体',
              field: 'channel',
              slots: { default: 'form_channel' },
            },

            {
              slots: { default: 'form_showDetail' },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: { width: '500px' },
        formConfig: { labelWidth: '140px' },
      },
      selectFormData: {
        appCode: null,
        country: null,
        advertiserId: null,
        showDetail: '0',
        channel: '',
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
          country:
            this.selectFormData.country !== 'all'
              ? this.selectFormData.country
              : '',
        }
      },
    }
  },
  methods: {
    reloadQuery() {
      this.$refs['page-table'].reloadQuery()
    },
  },
}
</script>
