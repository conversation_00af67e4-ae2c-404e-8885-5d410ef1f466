<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="70%"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
      :inline="true"
    >
      <el-form-item label="日期" prop="dt">
        <el-input
          v-model="dataForm.dt"
          placeholder="日期"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="厂商(投放)" prop="brand">
        <el-input
          v-model="dataForm.brand"
          placeholder="厂商(投放)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="投放平台(投放)" prop="channel">
        <el-input
          v-model="dataForm.channel"
          placeholder="投放平台(投放)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="账户名称(投放)"
        prop="accountName"
        :disabled="disabled"
      >
        <el-input
          v-model="dataForm.accountName"
          placeholder="账户名称(投放)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="账户id(投放)" prop="accountId">
        <el-input
          v-model="dataForm.accountId"
          placeholder="账户id(投放)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="出价" prop="chujia">
        <el-input
          v-model="dataForm.chujia"
          placeholder="出价"
          :disabled="disabled"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="账户预算" prop="budget">
        <el-input
          v-model="dataForm.budget"
          placeholder="账户预算"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="当前账户余额" prop="totalBalance">
        <el-input
          v-model="dataForm.totalBalance"
          placeholder="当前账户余额"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="点击广告用户数" prop="adClickNum">
        <el-input
          v-model="dataForm.adClickNum"
          placeholder="点击广告用户数"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="cpc价格" prop="cpc">
        <el-input
          v-model="dataForm.cpc"
          placeholder="cpc价格"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="素材点击率ctr(投放)" prop="ctr">
        <el-input
          v-model="dataForm.ctr"
          placeholder="素材点击率ctr(投放)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="投放金额" prop="cost">
        <el-input
          v-model="dataForm.cost"
          placeholder="投放金额"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="新增用户(去重)" prop="newNum">
        <el-input
          v-model="dataForm.newNum"
          placeholder="新增用户(去重)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="落地页拉起率" prop="landpagePercent">
        <el-input
          v-model="dataForm.landpagePercent"
          placeholder="落地页拉起率"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="投放点击拉起率" prop="landpageNoPercent">
        <el-input
          v-model="dataForm.landpageNoPercent"
          placeholder="投放点击拉起率"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="投放点击拉起率（全天去重）" prop="dayUpPercent">
        <el-input
          v-model="dataForm.dayUpPercent"
          placeholder="投放点击拉起率（全天去重）"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="吊起成本(去重)" prop="upCpi">
        <el-input
          v-model="dataForm.upCpi"
          placeholder="吊起成本(去重)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="吊起成本(不去重)" prop="upNoCpi">
        <el-input
          v-model="dataForm.upNoCpi"
          placeholder="吊起成本(不去重)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="人均拉起次数" prop="upAvg">
        <el-input
          v-model="dataForm.upAvg"
          placeholder="人均拉起次数"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="回传比例" prop="回传比例">
        <el-input
          v-model="dataForm.returnRate"
          placeholder="回传比例"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="预估收益" prop="income">
        <el-input
          v-model="dataForm.income"
          placeholder="预估收益"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="开屏收入" prop="xincome">
        <el-input v-model="dataForm.xincome" placeholder="开屏收入"></el-input>
      </el-form-item>
      <el-form-item label="广告曝光" prop="costExposureNum">
        <el-input
          v-model="dataForm.costExposureNum"
          placeholder="广告曝光"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="广告曝光(变现)" prop="exposureNum">
        <el-input
          v-model="dataForm.exposureNum"
          placeholder="广告曝光(变现)"
          :disabled="disabled"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="ecpm(投放)" prop="costEcpm">
        <el-input
          v-model="dataForm.costEcpm"
          placeholder="ecpm(投放)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="IPU(变现)" prop="ipu">
        <el-input
          v-model="dataForm.ipu"
          placeholder="ipu(变现)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="ecpm(变现)" prop="ecpm">
        <el-input
          v-model="dataForm.ecpm"
          placeholder="ecpm(变现)"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="roi" prop="roi">
        <el-input
          v-model="dataForm.roi"
          placeholder="roi"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="转化率（账户维度）" prop="conversionRate">
        <el-input
          v-model="dataForm.conversionRate"
          placeholder="转化率（账户维度）"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="归因率" prop="attrRate">
        <el-input
          v-model="dataForm.attrRate"
          placeholder="归因率"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-input
          v-model="dataForm.updatedAt"
          placeholder="更新时间"
          :disabled="disabled"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      disabled: true,
      dataForm: {
        id: 0,
        dt: '',
        brand: '',
        channel: '',
        accountName: '',
        accountId: '',
        adClickNum: '',
        cpc: '',
        ctr: '',
        cost: '',
        newNum: '',
        landpagePercent: '',
        upCpi: '',
        upNoCpi: '',
        upAvg: '',
        income: '',
        exposureNum: '',
        ipu: '',
        ecpm: '',
        roi: '',
        createdAt: '',
        updatedAt: '',
        budget: '',
        totalBalance: '',
        dayUpPercent: '',
        xincome: '',
        landpageNoPercent: '',
        costExposureNum: '',
        costEcpm: '',
        conversionRate: '',
        attrRate: '',
      },
      dataRule: {
        dt: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        // brand: [
        //   { required: true, message: '厂商(投放)不能为空', trigger: 'blur' },
        // ],
        channel: [
          {
            required: true,
            message: '投放平台(投放)不能为空',
            trigger: 'blur',
          },
        ],
        accountName: [
          {
            required: true,
            message: '账户名称(投放)不能为空',
            trigger: 'blur',
          },
        ],
        accountId: [
          { required: true, message: '账户id(投放)不能为空', trigger: 'blur' },
        ],
        // chujia: [{ required: true, message: '出价不能为空', trigger: 'blur' }],
        adClickNum: [
          {
            required: true,
            message: '点击广告用户数不能为空',
            trigger: 'blur',
          },
        ],
        cpc: [{ required: true, message: 'cpc价格不能为空', trigger: 'blur' }],
        ctr: [
          {
            required: true,
            message: '素材点击率ctr(投放)不能为空',
            trigger: 'blur',
          },
        ],
        cost: [
          { required: true, message: '投放金额不能为空', trigger: 'blur' },
        ],
        newNum: [
          {
            required: true,
            message: '新增用户(去重)不能为空',
            trigger: 'blur',
          },
        ],
        landpagePercent: [
          { required: true, message: '落地页拉起率不能为空', trigger: 'blur' },
        ],
        upCpi: [
          {
            required: true,
            message: '吊起成本(去重)不能为空',
            trigger: 'blur',
          },
        ],
        upNoCpi: [
          {
            required: true,
            message: '吊起成本(不去重)不能为空',
            trigger: 'blur',
          },
        ],
        upAvg: [
          { required: true, message: '人均拉起次数不能为空', trigger: 'blur' },
        ],
        income: [
          { required: true, message: '预估收益不能为空', trigger: 'blur' },
        ],
        exposureNum: [
          {
            required: true,
            message: '广告曝光(变现)不能为空',
            trigger: 'blur',
          },
        ],
        ipu: [
          { required: true, message: 'ipu(变现)不能为空', trigger: 'blur' },
        ],
        ecpm: [
          { required: true, message: 'ecpm(变现)不能为空', trigger: 'blur' },
        ],
        roi: [{ required: true, message: 'roi不能为空', trigger: 'blur' }],
        // createdAt: [
        //   { required: true, message: '创建时间不能为空', trigger: 'blur' },
        // ],
        // updatedAt: [
        //   { required: true, message: '更新时间不能为空', trigger: 'blur' },
        // ],
        budget: [{ required: true, message: '账户预算', trigger: 'blur' }],
        totalBalance: [
          { required: true, message: '当前账户余额', trigger: 'blur' },
        ],
        dayUpPercent: [
          {
            required: true,
            message: '投放点击拉起率（全天去重）',
            trigger: 'blur',
          },
        ],
        xincome: [{ required: true, message: '开屏收入', trigger: 'blur' }],
        landpageNoPercent: [
          { required: true, message: '投放点击拉起率', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/quickrealtimereport/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              if (data?.code === 0) {
                const fields = [
                  'dt',
                  'brand',
                  'channel',
                  'accountName',
                  'accountId',
                  'adClickNum',
                  'cpc',
                  'ctr',
                  'cost',
                  'newNum',
                  'landpagePercent',
                  'upCpi',
                  'upNoCpi',
                  'upAvg',
                  'income',
                  'exposureNum',
                  'ipu',
                  'ecpm',
                  'roi',
                  'createdAt',
                  'updatedAt',
                  'totalBalance',
                  'dayUpPercent',
                  'xincome',
                  'landpageNoPercent',
                  'budget',
                  'costExposureNum',
                  'costEcpm',
                  'conversionRate',
                  'attrRate',
                  'returnRate',
                ]

                fields.forEach(field => {
                  if (field in data.quickRealtimeReport) {
                    this.dataForm[field] = data.quickRealtimeReport[field]
                  }
                })
              }
              // this.dataForm.dt = data.quickRealtimeReport.dt
              // this.dataForm.brand = data.quickRealtimeReport.brand
              // this.dataForm.channel = data.quickRealtimeReport.channel
              // this.dataForm.accountName = data.quickRealtimeReport.accountName
              // this.dataForm.accountId = data.quickRealtimeReport.accountId
              // // this.dataForm.chujia = data.quickRealtimeReport.chujia
              // this.dataForm.adClickNum = data.quickRealtimeReport.adClickNum
              // this.dataForm.cpc = data.quickRealtimeReport.cpc
              // this.dataForm.ctr = data.quickRealtimeReport.ctr
              // this.dataForm.cost = data.quickRealtimeReport.cost
              // this.dataForm.newNum = data.quickRealtimeReport.newNum
              // this.dataForm.landpagePercent =
              //   data.quickRealtimeReport.landpagePercent
              // this.dataForm.upCpi = data.quickRealtimeReport.upCpi
              // this.dataForm.upNoCpi = data.quickRealtimeReport.upNoCpi
              // this.dataForm.upAvg = data.quickRealtimeReport.upAvg
              // this.dataForm.income = data.quickRealtimeReport.income
              // this.dataForm.exposureNum = data.quickRealtimeReport.exposureNum
              // this.dataForm.ipu = data.quickRealtimeReport.ipu
              // this.dataForm.ecpm = data.quickRealtimeReport.ecpm
              // this.dataForm.roi = data.quickRealtimeReport.roi
              // this.dataForm.createdAt = data.quickRealtimeReport.createdAt
              // this.dataForm.updatedAt = data.quickRealtimeReport.updatedAt
              // this.dataForm.totalBalance = data.quickRealtimeReport.totalBalance
              // this.dataForm.dayUpPercent = data.quickRealtimeReport.dayUpPercent
              // this.dataForm.xincome = data.quickRealtimeReport.xincome
              // this.dataForm.landpageNoPercent =
              //   data.quickRealtimeReport.landpageNoPercent
              // this.dataForm.budget = data.quickRealtimeReport.budget
              // this.dataForm.costExposureNum =
              //   data.quickRealtimeReport.costExposureNum
              // this.dataForm.costEcpm = data.quickRealtimeReport.costEcpm
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/stat/quickrealtimereport/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              ...this.dataForm,
              xincome: Number(this.dataForm.xincome),
              income: Number(this.dataForm.income),
            }),
            // data: this.$http.adornData({
            //   id: this.dataForm.id || undefined,
            //   dt: this.dataForm.dt,
            //   brand: this.dataForm.brand,
            //   channel: this.dataForm.channel,
            //   accountName: this.dataForm.accountName,
            //   accountId: this.dataForm.accountId,
            //   // chujia: this.dataForm.chujia,
            //   adClickNum: this.dataForm.adClickNum,
            //   cpc: this.dataForm.cpc,
            //   ctr: this.dataForm.ctr,
            //   cost: this.dataForm.cost,
            //   newNum: this.dataForm.newNum,
            //   landpagePercent: this.dataForm.landpagePercent,
            //   upCpi: this.dataForm.upCpi,
            //   upNoCpi: this.dataForm.upNoCpi,
            //   upAvg: this.dataForm.upAvg,
            //   income: this.dataForm.income,
            //   exposureNum: this.dataForm.exposureNum,
            //   ipu: this.dataForm.ipu,
            //   ecpm: this.dataForm.ecpm,
            //   roi: this.dataForm.roi,
            //   createdAt: this.dataForm.createdAt,
            //   updatedAt: this.dataForm.updatedAt,
            //   totalBalance: this.dataForm.totalBalance,
            //   dayUpPercent: this.dataForm.dayUpPercent,
            //   xincome: Number(this.dataForm.xincome),
            //   landpageNoPercent: this.dataForm.landpageNoPercent,
            //   budget: this.dataForm.budget,
            //   costExposureNum: this.dataForm.costExposureNum,
            //   costEcpm: this.dataForm.costEcpm,
            // }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
