<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    operate-width="180"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appList"
        :is-show-all="false"
        clearable
        multiple
        collapse-tags
        :app-filter="appFilter"
      />
    </template>
    <template #form_day>
      <el-date-picker
        v-model="selectFormData.day"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        placeholder="选择日期"
        value-format="yyyyMMdd"
      />
    </template>
    <template #form_brand>
      <map-select
        v-model="selectFormData.brandList"
        :list="brandList"
        clearable
        multiple
        collapse-tags
      />
    </template>
    <template #table_item_appCode="{row}">
      <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
    </template>
    <template #table_item_day="{row}">
      <span>{{ row.day | formatDate }}</span>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>
  </page-table>
</template>

<script>
import { quickAppBrandDataDailyRequest as request } from '@/api/stat'

export default {
  data() {
    return {
      fishList: [],
      brandList: new Map([
        ['oppo', 'OPPO'],
        ['vivo', 'VIVO'],
        ['huawei', '华为'],
        ['xiaomi', '小米'],
      ]),
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'day',
            title: '日期',
            minWidth: 100,
            slots: {
              default: 'table_item_day',
            },
          },
          {
            field: 'appCode',
            title: '应用',
            minWidth: 120,
            slots: {
              default: 'table_item_appCode',
            },
          },
          {
            field: 'brand',
            title: '厂商',
            minWidth: '80',
            formatter: ({ row }) => this.brandList.get(row.brand),
          },
          { field: 'dnu', title: 'dnu', minWidth: '80' },
          { field: 'dau', title: 'dau', minWidth: '80' },

          {
            field: 'ecpm',
            title: 'ecpm',
            minWidth: '80',
          },

          {
            field: 'arpu',
            title: 'arpu',
            minWidth: '80',
          },
          { field: 'aliveCost', title: '活跃成本', minWidth: '80' },
          {
            field: 'ipu',
            title: '人均广告ipu',
            minWidth: '120',
          },
          {
            field: 'ctr',
            title: 'ctr',
            minWidth: '80',
          },

          { field: 'mainPu', title: '人均吊起次数', minWidth: '120' },
          { field: 'mainRate', title: '吊起率', minWidth: '80' },
          { field: 'mainNum', title: '总吊起数', minWidth: '80' },
          {
            field: 'attributeRate',
            title: '归因率',
            minWidth: '80',
            formatter: ({ row }) => {
              return (row.attributeRate * 100).toFixed(0) + '%'
            },
          },
          {
            field: 'reportRate',
            title: '投诉率',
            minWidth: '80',
            formatter: ({ row }) => {
              return (row.reportRate * 10000).toFixed(2) + '‱'
            },
          },

          {
            field: 'costAdImpression',
            title: '投放广告展现',
            minWidth: '120',
          },

          {
            field: 'incomeAdImpression',
            title: '商业化广告展现',
            minWidth: '120',
          },

          { field: 'cost', title: '消耗', minWidth: '80' },

          {
            field: 'income',
            title: '收入',
            minWidth: '80',
          },
          {
            field: 'grossProfit',
            title: '毛利',
            minWidth: '80',
          },
          {
            field: 't0Roi',
            title: '当日roi',
            minWidth: '80',
          },
          {
            field: 'roi',
            title: '累计roi',
            minWidth: '80',
          },
          { field: 'createdAt', title: '创建时间', minWidth: 150 },
          { field: 'updatedAt', title: '更新时间', minWidth: 150 },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              title: '日器',
              slots: {
                default: 'form_day',
              },
            },
            {
              title: '品牌',
              slots: {
                default: 'form_brand',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
      },
      selectFormData: {
        appList: null,
        day: null,
        brandList: null,
      },
      beforeSelect: () => {
        const appList = this.selectFormData.appList
          ? this.selectFormData.appList.join()
          : ''
        const brandList = this.selectFormData.brandList
          ? this.selectFormData.brandList.join()
          : ''
        const startDay = this.selectFormData.day
          ? this.selectFormData.day[0]
          : ''
        const endDay = this.selectFormData.day ? this.selectFormData.day[1] : ''
        return {
          appList,
          brandList,
          startDay,
          endDay,
        }
      },
      appFilter: it => it.groupId === 11,
    }
  },
}
</script>
