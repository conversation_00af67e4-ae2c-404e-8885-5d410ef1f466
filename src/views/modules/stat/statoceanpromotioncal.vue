<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="['select']"
    operate-width="330"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #table_item_appId="{row}">
      <color-tag :id="row.appId">{{ row.appId | getAppName }}</color-tag>
    </template>

    <template #form_range_time>
      <el-date-picker
        v-model="rangeTime"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
      />
    </template>

    <template #form_advertise_id>
      <el-input v-model="advertiseId" clearable />
    </template>

    <template #form_advertise_id_list>
      <arr-select
        v-model="advertiseId"
        :list="advertiseIdList"
        label-key="advertiser_id"
        value-key="advertiser_id"
        clearable
        filterable
      />
    </template>

    <template #form_appCode>
      <app-select-component v-model="appCode" :is-show-all="false" clearable />
    </template>

    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
  </page-table>
</template>

<script>
import { baseConfigMap, drawConfigMap } from '@/map/fishing'
import dayjs from '@/dayjs'
import { statOceanPromotionCalRequest as request } from '@/api/stat'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      baseConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'seq', title: '序号', minWidth: 60 },
          {
            field: 'appId',
            title: '应用',
            minWidth: 100,
            slots: {
              default: 'table_item_appId',
            },
          },
          { field: 'day', title: '日期', width: 120 },
          { field: 'advertiseId', title: '广告主ID', minWidth: 140 },
          { field: 'aid', title: '广告计划ID', minWidth: 140 },
          { field: 'dnu', title: '激活', minWidth: 140 },
          { field: 'activateCount', title: '回传激活', minWidth: 60 },
          { field: 'addictionCount', title: '关键行为', minWidth: 60 },
          { field: 'activatePercent', title: '激活回传率', minWidth: 80 },
          { field: 'addictionPercent', title: '关键行为率', minWidth: 80 },
          { field: 'arpu', title: 'arpu', minWidth: 160 },
          { field: '实际关键行为率', title: '实际关键行为率', minWidth: 160 },
          { field: 'createdAt', title: '创建时间', minWidth: 160 },
          { field: 'updatedAt', title: '更新时间', minWidth: 160 },
        ],
        formConfig: {
          items: [
            // {
            //   field: 'userId',
            //   title: '用户ID',
            //   itemRender: {
            //     name: '$input',
            //     props: { placeholder: '请选择', clearable: true },
            //     defaultValue: '',
            //   },
            // },
            {
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              field: 'advertiseId',
              title: '投放账户',
              // itemRender: {
              //   name: '$input',
              //   props: { placeholder: '请选择', clearable: true },
              // },
              slots: {
                default: 'form_advertise_id',
              },
            },
            {
              title: '日期范围',
              slots: {
                default: 'form_range_time',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '900px',
        },
        formConfig: {
          labelWidth: '140px',
          labelPosition: 'top',
        },
      },
      rangeTime: [],
      appCode: '',
      advertiseId: '',
      advertiseIdList: [],
    }
  },
  methods: {
    beforeSelect(form) {
      console.log(form)
      if (this.rangeTime) {
        const format = 'YYYYMMDD'
        const startTime = this.rangeTime[0]
          ? dayjs(this.rangeTime[0]).format(format)
          : ''
        const endTime = this.rangeTime[1]
          ? dayjs(this.rangeTime[1]).format(format)
          : ''

        return {
          appCode: this.appCode,
          advertiseId: this.advertiseId,
          startTime: startTime,
          endTime: endTime,
        }
      }

      return {
        advertiseId: this.advertiseId,
        appCode: this.appCode,
      }
    },
    getAdvertiserIdList() {
      this.requestCollection.getAdvertiserIdList(this.appCode).then(res => {
        if (res.code === 0) {
          this.advertiseIdList = res.data
        }
      })
    },
  },
}
</script>
