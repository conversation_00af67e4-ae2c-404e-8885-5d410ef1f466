<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id" prop="appCode">
      <el-input v-model="dataForm.appCode" placeholder="应用id"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="新增数字" prop="newAddNums">
      <el-input v-model="dataForm.newAddNums" placeholder="新增数字"></el-input>
    </el-form-item>
    <el-form-item label="活跃数字" prop="activeNums">
      <el-input v-model="dataForm.activeNums" placeholder="活跃数字"></el-input>
    </el-form-item>
    <el-form-item label="次留" prop="keep2">
      <el-input v-model="dataForm.keep2" placeholder="次留"></el-input>
    </el-form-item>
    <el-form-item label="3留" prop="keep3">
      <el-input v-model="dataForm.keep3" placeholder="3留"></el-input>
    </el-form-item>
    <el-form-item label="4留" prop="keep4">
      <el-input v-model="dataForm.keep4" placeholder="4留"></el-input>
    </el-form-item>
    <el-form-item label="5留" prop="keep5">
      <el-input v-model="dataForm.keep5" placeholder="5留"></el-input>
    </el-form-item>
    <el-form-item label="6留" prop="keep6">
      <el-input v-model="dataForm.keep6" placeholder="6留"></el-input>
    </el-form-item>
    <el-form-item label="7留" prop="keep7">
      <el-input v-model="dataForm.keep7" placeholder="7留"></el-input>
    </el-form-item>
    <el-form-item label="8留" prop="keep8">
      <el-input v-model="dataForm.keep8" placeholder="8留"></el-input>
    </el-form-item>
    <el-form-item label="9留" prop="keep9">
      <el-input v-model="dataForm.keep9" placeholder="9留"></el-input>
    </el-form-item>
    <el-form-item label="10留" prop="keep10">
      <el-input v-model="dataForm.keep10" placeholder="10留"></el-input>
    </el-form-item>
    <el-form-item label="11留" prop="keep11">
      <el-input v-model="dataForm.keep11" placeholder="11留"></el-input>
    </el-form-item>
    <el-form-item label="12留" prop="keep12">
      <el-input v-model="dataForm.keep12" placeholder="12留"></el-input>
    </el-form-item>
    <el-form-item label="13留" prop="keep13">
      <el-input v-model="dataForm.keep13" placeholder="13留"></el-input>
    </el-form-item>
    <el-form-item label="14留" prop="keep14">
      <el-input v-model="dataForm.keep14" placeholder="14留"></el-input>
    </el-form-item>
    <el-form-item label="15留" prop="keep15">
      <el-input v-model="dataForm.keep15" placeholder="15留"></el-input>
    </el-form-item>
    <el-form-item label="16留" prop="keep16">
      <el-input v-model="dataForm.keep16" placeholder="16留"></el-input>
    </el-form-item>
    <el-form-item label="17留" prop="keep17">
      <el-input v-model="dataForm.keep17" placeholder="17留"></el-input>
    </el-form-item>
    <el-form-item label="18留" prop="keep18">
      <el-input v-model="dataForm.keep18" placeholder="18留"></el-input>
    </el-form-item>
    <el-form-item label="19留" prop="keep19">
      <el-input v-model="dataForm.keep19" placeholder="19留"></el-input>
    </el-form-item>
    <el-form-item label="20留" prop="keep20">
      <el-input v-model="dataForm.keep20" placeholder="20留"></el-input>
    </el-form-item>
    <el-form-item label="21留" prop="keep21">
      <el-input v-model="dataForm.keep21" placeholder="21留"></el-input>
    </el-form-item>
    <el-form-item label="22留" prop="keep22">
      <el-input v-model="dataForm.keep22" placeholder="22留"></el-input>
    </el-form-item>
    <el-form-item label="23留" prop="keep23">
      <el-input v-model="dataForm.keep23" placeholder="23留"></el-input>
    </el-form-item>
    <el-form-item label="24留" prop="keep24">
      <el-input v-model="dataForm.keep24" placeholder="24留"></el-input>
    </el-form-item>
    <el-form-item label="25留" prop="keep25">
      <el-input v-model="dataForm.keep25" placeholder="25留"></el-input>
    </el-form-item>
    <el-form-item label="26留" prop="keep26">
      <el-input v-model="dataForm.keep26" placeholder="26留"></el-input>
    </el-form-item>
    <el-form-item label="27留" prop="keep27">
      <el-input v-model="dataForm.keep27" placeholder="27留"></el-input>
    </el-form-item>
    <el-form-item label="28留" prop="keep28">
      <el-input v-model="dataForm.keep28" placeholder="28留"></el-input>
    </el-form-item>
    <el-form-item label="29留" prop="keep29">
      <el-input v-model="dataForm.keep29" placeholder="29留"></el-input>
    </el-form-item>
    <el-form-item label="30留" prop="keep30">
      <el-input v-model="dataForm.keep30" placeholder="30留"></el-input>
    </el-form-item>
    <el-form-item label="60留" prop="keep60">
      <el-input v-model="dataForm.keep60" placeholder="60留"></el-input>
    </el-form-item>
    <el-form-item label="90留" prop="keep90">
      <el-input v-model="dataForm.keep90" placeholder="90留"></el-input>
    </el-form-item>
    <el-form-item label="120留" prop="keep120">
      <el-input v-model="dataForm.keep120" placeholder="120留"></el-input>
    </el-form-item>
    <el-form-item label="150留" prop="keep150">
      <el-input v-model="dataForm.keep150" placeholder="150留"></el-input>
    </el-form-item>
    <el-form-item label="180留" prop="keep180">
      <el-input v-model="dataForm.keep180" placeholder="180留"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updateAt">
      <el-input v-model="dataForm.updateAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appCode: '',
          day: '',
          newAddNums: '',
          activeNums: '',
          keep2: '',
          keep3: '',
          keep4: '',
          keep5: '',
          keep6: '',
          keep7: '',
          keep8: '',
          keep9: '',
          keep10: '',
          keep11: '',
          keep12: '',
          keep13: '',
          keep14: '',
          keep15: '',
          keep16: '',
          keep17: '',
          keep18: '',
          keep19: '',
          keep20: '',
          keep21: '',
          keep22: '',
          keep23: '',
          keep24: '',
          keep25: '',
          keep26: '',
          keep27: '',
          keep28: '',
          keep29: '',
          keep30: '',
          keep60: '',
          keep90: '',
          keep120: '',
          keep150: '',
          keep180: '',
          createdAt: '',
          updateAt: ''
        },
        dataRule: {
          appCode: [
            { required: true, message: '应用id不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          newAddNums: [
            { required: true, message: '新增数字不能为空', trigger: 'blur' }
          ],
          activeNums: [
            { required: true, message: '活跃数字不能为空', trigger: 'blur' }
          ],
          keep2: [
            { required: true, message: '次留不能为空', trigger: 'blur' }
          ],
          keep3: [
            { required: true, message: '3留不能为空', trigger: 'blur' }
          ],
          keep4: [
            { required: true, message: '4留不能为空', trigger: 'blur' }
          ],
          keep5: [
            { required: true, message: '5留不能为空', trigger: 'blur' }
          ],
          keep6: [
            { required: true, message: '6留不能为空', trigger: 'blur' }
          ],
          keep7: [
            { required: true, message: '7留不能为空', trigger: 'blur' }
          ],
          keep8: [
            { required: true, message: '8留不能为空', trigger: 'blur' }
          ],
          keep9: [
            { required: true, message: '9留不能为空', trigger: 'blur' }
          ],
          keep10: [
            { required: true, message: '10留不能为空', trigger: 'blur' }
          ],
          keep11: [
            { required: true, message: '11留不能为空', trigger: 'blur' }
          ],
          keep12: [
            { required: true, message: '12留不能为空', trigger: 'blur' }
          ],
          keep13: [
            { required: true, message: '13留不能为空', trigger: 'blur' }
          ],
          keep14: [
            { required: true, message: '14留不能为空', trigger: 'blur' }
          ],
          keep15: [
            { required: true, message: '15留不能为空', trigger: 'blur' }
          ],
          keep16: [
            { required: true, message: '16留不能为空', trigger: 'blur' }
          ],
          keep17: [
            { required: true, message: '17留不能为空', trigger: 'blur' }
          ],
          keep18: [
            { required: true, message: '18留不能为空', trigger: 'blur' }
          ],
          keep19: [
            { required: true, message: '19留不能为空', trigger: 'blur' }
          ],
          keep20: [
            { required: true, message: '20留不能为空', trigger: 'blur' }
          ],
          keep21: [
            { required: true, message: '21留不能为空', trigger: 'blur' }
          ],
          keep22: [
            { required: true, message: '22留不能为空', trigger: 'blur' }
          ],
          keep23: [
            { required: true, message: '23留不能为空', trigger: 'blur' }
          ],
          keep24: [
            { required: true, message: '24留不能为空', trigger: 'blur' }
          ],
          keep25: [
            { required: true, message: '25留不能为空', trigger: 'blur' }
          ],
          keep26: [
            { required: true, message: '26留不能为空', trigger: 'blur' }
          ],
          keep27: [
            { required: true, message: '27留不能为空', trigger: 'blur' }
          ],
          keep28: [
            { required: true, message: '28留不能为空', trigger: 'blur' }
          ],
          keep29: [
            { required: true, message: '29留不能为空', trigger: 'blur' }
          ],
          keep30: [
            { required: true, message: '30留不能为空', trigger: 'blur' }
          ],
          keep60: [
            { required: true, message: '60留不能为空', trigger: 'blur' }
          ],
          keep90: [
            { required: true, message: '90留不能为空', trigger: 'blur' }
          ],
          keep120: [
            { required: true, message: '120留不能为空', trigger: 'blur' }
          ],
          keep150: [
            { required: true, message: '150留不能为空', trigger: 'blur' }
          ],
          keep180: [
            { required: true, message: '180留不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updateAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/stat/activekeepdaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appCode = data.activeKeepDaily.appCode
                this.dataForm.day = data.activeKeepDaily.day
                this.dataForm.newAddNums = data.activeKeepDaily.newAddNums
                this.dataForm.activeNums = data.activeKeepDaily.activeNums
                this.dataForm.keep2 = data.activeKeepDaily.keep2
                this.dataForm.keep3 = data.activeKeepDaily.keep3
                this.dataForm.keep4 = data.activeKeepDaily.keep4
                this.dataForm.keep5 = data.activeKeepDaily.keep5
                this.dataForm.keep6 = data.activeKeepDaily.keep6
                this.dataForm.keep7 = data.activeKeepDaily.keep7
                this.dataForm.keep8 = data.activeKeepDaily.keep8
                this.dataForm.keep9 = data.activeKeepDaily.keep9
                this.dataForm.keep10 = data.activeKeepDaily.keep10
                this.dataForm.keep11 = data.activeKeepDaily.keep11
                this.dataForm.keep12 = data.activeKeepDaily.keep12
                this.dataForm.keep13 = data.activeKeepDaily.keep13
                this.dataForm.keep14 = data.activeKeepDaily.keep14
                this.dataForm.keep15 = data.activeKeepDaily.keep15
                this.dataForm.keep16 = data.activeKeepDaily.keep16
                this.dataForm.keep17 = data.activeKeepDaily.keep17
                this.dataForm.keep18 = data.activeKeepDaily.keep18
                this.dataForm.keep19 = data.activeKeepDaily.keep19
                this.dataForm.keep20 = data.activeKeepDaily.keep20
                this.dataForm.keep21 = data.activeKeepDaily.keep21
                this.dataForm.keep22 = data.activeKeepDaily.keep22
                this.dataForm.keep23 = data.activeKeepDaily.keep23
                this.dataForm.keep24 = data.activeKeepDaily.keep24
                this.dataForm.keep25 = data.activeKeepDaily.keep25
                this.dataForm.keep26 = data.activeKeepDaily.keep26
                this.dataForm.keep27 = data.activeKeepDaily.keep27
                this.dataForm.keep28 = data.activeKeepDaily.keep28
                this.dataForm.keep29 = data.activeKeepDaily.keep29
                this.dataForm.keep30 = data.activeKeepDaily.keep30
                this.dataForm.keep60 = data.activeKeepDaily.keep60
                this.dataForm.keep90 = data.activeKeepDaily.keep90
                this.dataForm.keep120 = data.activeKeepDaily.keep120
                this.dataForm.keep150 = data.activeKeepDaily.keep150
                this.dataForm.keep180 = data.activeKeepDaily.keep180
                this.dataForm.createdAt = data.activeKeepDaily.createdAt
                this.dataForm.updateAt = data.activeKeepDaily.updateAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/stat/activekeepdaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appCode': this.dataForm.appCode,
                'day': this.dataForm.day,
                'newAddNums': this.dataForm.newAddNums,
                'activeNums': this.dataForm.activeNums,
                'keep2': this.dataForm.keep2,
                'keep3': this.dataForm.keep3,
                'keep4': this.dataForm.keep4,
                'keep5': this.dataForm.keep5,
                'keep6': this.dataForm.keep6,
                'keep7': this.dataForm.keep7,
                'keep8': this.dataForm.keep8,
                'keep9': this.dataForm.keep9,
                'keep10': this.dataForm.keep10,
                'keep11': this.dataForm.keep11,
                'keep12': this.dataForm.keep12,
                'keep13': this.dataForm.keep13,
                'keep14': this.dataForm.keep14,
                'keep15': this.dataForm.keep15,
                'keep16': this.dataForm.keep16,
                'keep17': this.dataForm.keep17,
                'keep18': this.dataForm.keep18,
                'keep19': this.dataForm.keep19,
                'keep20': this.dataForm.keep20,
                'keep21': this.dataForm.keep21,
                'keep22': this.dataForm.keep22,
                'keep23': this.dataForm.keep23,
                'keep24': this.dataForm.keep24,
                'keep25': this.dataForm.keep25,
                'keep26': this.dataForm.keep26,
                'keep27': this.dataForm.keep27,
                'keep28': this.dataForm.keep28,
                'keep29': this.dataForm.keep29,
                'keep30': this.dataForm.keep30,
                'keep60': this.dataForm.keep60,
                'keep90': this.dataForm.keep90,
                'keep120': this.dataForm.keep120,
                'keep150': this.dataForm.keep150,
                'keep180': this.dataForm.keep180,
                'createdAt': this.dataForm.createdAt,
                'updateAt': this.dataForm.updateAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
