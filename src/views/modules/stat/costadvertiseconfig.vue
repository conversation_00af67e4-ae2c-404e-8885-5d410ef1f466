<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="request"
      :model-config="modelConfig"
      :features="['insert', 'delete', 'batch_delete', 'update']"
      operate-width="180"
      :before-select="beforeSelect"
    >
      <template #table_item_state="{row}">
        <color-tag :id="row.state">
          {{ stateMap.get(row.state) }}
        </color-tag>
      </template>

      <template #model>
        <el-form-item label="广告主ID" prop="advertiseId">
          <el-input
            v-model="modelConfig.formData.advertiseId"
            placeholder="过滤词"
            clearable
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <arr-select
            :list="typeList"
            label-key="label"
            value-key="value"
            v-model="modelConfig.formData.type"
            clearable
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <map-radio v-model="modelConfig.formData.state" :list="stateMap" />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import { costAdvertiseConfigRequest as request } from '@/api/stat'

export default {
  data() {
    return {
      typeList: [
        { label: 'tt', value: 1 },
        { label: 'google', value: 2 },
        { label: 'facebook', value: 3 },
      ],
      stateMap: new Map([
        [1, '启用'],
        [2, '不启用'],
      ]),
      request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', title: 'ID', width: 50 },
          { field: 'advertiseId', title: '广告主ID', minWidth: 60 },
          {
            field: 'type',
            title: '类型',
            minWidth: 60,
            formatter: ({ row }) => {
              const res = this.typeList.find(it => it.value === row.type)
              return res && res.label ? res.label : ''
            },
          },
          {
            field: 'state',
            title: '状态',
            minWidth: 60,
            slots: {
              default: 'table_item_state',
            },
          },
        ],
        formConfig: {
          items: [
            // { title: '投放平台', slots: { default: 'form_adPlatform' } },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '440px',
        },
        formData: {
          id: null,
          advertiseId: '',
          type: '',
          state: 1,
        },
        formConfig: { labelWidth: '110px' },
        formRule: {
          advertiseId: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          type: [{ required: true, message: '不能为空', trigger: 'blur' }],
          state: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
      selectFormData: {
        appCode: null,
        day: null,
        country: null,
        adPlatform: null,
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
      rules: {
        type: [{ required: true, message: '必传' }],
        file: [{ required: true, message: '必传' }],
      },
    }
  },
}
</script>
