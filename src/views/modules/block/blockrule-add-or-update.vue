<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    class="adaptive_dialog"
    @closed="_closed"
    width="740px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <h3>新增屏蔽规则</h3>
      <el-form-item label="规则名" prop="name">
        <el-input v-model.trim="dataForm.name" placeholder="规则名" clearable />
      </el-form-item>
      <!--<el-form-item label="应用名称" prop="appId">-->
      <!--  <el-select v-model="dataForm.appId" clearable>-->
      <!--    <el-option-->
      <!--      v-for="item in appList"-->
      <!--      :key="item.id"-->
      <!--      :label="item.name"-->
      <!--      :value="item.id"-->
      <!--    />-->
      <!--  </el-select>-->
      <!--</el-form-item>-->
      <div style="margin-bottom: 30px;">
        <!--@app-id-error="_handleAppIdError"-->
        <rule-table
          ref="ruleTable"
          :rule-list="dataForm.rules.list"
          :app-id="dataForm.appId"
          :app-version-list="appVersionList"
          :channel-list="channelList"
        />
      </div>
      <!--<el-form-item label="规则代码" prop="code">-->
      <!--  <el-input v-model="dataForm.code" placeholder="规则代码"></el-input>-->
      <!--</el-form-item>-->

      <h3>其他信息</h3>
      <el-collapse v-model="activeNames">
        <el-collapse-item title="规则详情" name="1">
          <code-editor
            :value="JSON.stringify(dataForm.rules.list)"
            line-numbers
            readonly
          />
        </el-collapse-item>
        <el-collapse-item title="附加信息" name="2">
          <code-editor v-model="dataForm.extra" line-numbers readonly />
        </el-collapse-item>
      </el-collapse>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="_dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import RuleTable from '@/components/rule-table'
import CodeEditor from '@/components/code-editor'

export default {
  components: {
    RuleTable,
    CodeEditor,
  },
  data() {
    return {
      visible: false,
      activeNames: [],
      dataForm: {
        id: 0,
        name: '',
        code: '',
        rules: {
          list: [],
        },
        extra: '',
        createdAt: '',
        updatedAt: '',
        appId: '',
      },
      dataRule: {
        name: [
          {
            required: true,
            message: '规则名不能为空',
            trigger: 'blur',
          },
        ],
        code: [
          {
            required: false,
            message: '规则代码不能为空',
            trigger: 'blur',
          },
        ],
        rules: [
          {
            required: true,
            message: '规则详情不能为空',
            trigger: 'blur',
          },
        ],
        extra: [
          {
            required: false,
            message: '附加信息不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          {
            required: false,
            message: '创建时间不能为空',
            trigger: 'blur',
          },
        ],
        updatedAt: [
          {
            required: false,
            message: '更新时间不能为空',
            trigger: 'blur',
          },
        ],
        appId: [
          {
            required: true,
            message: '应用名称不能为空',
            trigger: 'blur',
          },
        ],
      },
      // appId: null,
      appList: [],
      appVersionList: new Map([]),
      channelList: new Map([]),
    }
  },
  watch: {
    'dataForm.rules.list': {
      deep: true,
      handler(list) {
        this._genFn(list)
      },
    },
    // 'dataForm.appId'(app_id) {
    //   this._getAppVersionList(app_id)
    //   this._getChannelList(app_id)
    // },
  },
  methods: {
    /**
     * 把JSON生成对应的js函数
     * @private
     */
    _genFn(list) {
      let ifCondition = 'false'

      if (list && list.length) {
        ifCondition = ''
      }

      list.forEach(({ rule, condition, ruleDetail }) => {
        switch (rule) {
          case 'city':
          case 'appVersion':
          case 'brand':
          case 'channel':
            if (ifCondition !== '') {
              ifCondition += '&&'
            }
            ifCondition = containCon(ifCondition, rule, condition, ruleDetail)
            break
          case 'activateTime':
            if (ifCondition !== '') {
              ifCondition += '&&'
            }
            if (condition === '<') {
              ifCondition += `Date.now() < new Date(info.app.activation_time).getTime() + (${ruleDetail}*60*1000)`
            }
            if (condition === '>') {
              ifCondition += `Date.now() > new Date(info.app.activation_time).getTime() + (${ruleDetail}*60*1000)`
            }
            break
        }
      })

      function containCon(ifCondition, rule, condition, ruleDetail) {
        const strD = JSON.stringify(ruleDetail)
        let infoAttr = ''

        switch (rule) {
          case 'city':
            infoAttr = 'location.city'
            break
          case 'appVersion':
            // infoAttr = 'app.version_name'
            infoAttr = 'app.version_code'
            break
          case 'brand':
            infoAttr = 'device.brand'
            break
          case 'channel':
            infoAttr = 'market.channel_code'
            break
        }

        if (condition === 'in') {
          if (rule === 'city') {
            ifCondition += `(!info.${infoAttr} || ${strD}.indexOf(info.${infoAttr}) !== -1)`
          } else if (rule === 'appVersion') {
            ifCondition += `${strD}.indexOf(info.${infoAttr}) !== -1`
          } else {
            ifCondition += `${strD}.indexOf(info.${infoAttr}.toLocaleLowerCase()) !== -1`
          }
        } else {
          if (rule === 'city') {
            ifCondition += `( !info.${infoAttr} || !((${strD}.indexOf(info.${infoAttr})) !== -1) )`
          } else if (rule === 'appVersion') {
            ifCondition += `!((${strD}.indexOf(info.${infoAttr})) !== -1)`
          } else {
            ifCondition += `!((${strD}.indexOf(info.${infoAttr}.toLocaleLowerCase())) !== -1)`
          }
        }

        return ifCondition
      }

      this.dataForm.extra = `function block_rule(deviceInfo) {
          try {
            var info = JSON.parse(deviceInfo);
            if (${ifCondition || 'false'}) {
              return true;
            }
            return false;
          } catch (e) {
            return false;
          }
        }`
    },
    /**
     * 获取app列表
     * @private
     */
    _getAppList() {
      this.$store
        .dispatch('api/app/getAppList', {
          page: 1,
          limit: 1000,
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.page.list
          }
        })
    },
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true

      this._getAppList()

      this._getAppVersionList()
      this._getChannelList()

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/block/blockrule/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.name = data.blockRule.name
              this.dataForm.code = data.blockRule.code
              this.dataForm.extra = data.blockRule.extra
              try {
                this.dataForm.rules = JSON.parse(data.blockRule.rules)
                this.dataForm.appId = this.dataForm.rules.appId
              } catch (e) {
                console.log(e)
              }
            }
          })
        }
      })
    },
    _closed() {
      this.dataForm.rules.list = []
      this.$refs.ruleTable.resetFields()
      this.$emit('closed')
    },
    // 表单提交
    _dataFormSubmit: function() {
      this.$refs['dataForm'].validate(async valid => {
        if (!valid) return

        try {
          await this.$refs.ruleTable.validate()
        } catch (e) {
          return e
        }

        // 提交表单之前，先执行一下JS是否有语法错误
        try {
          eval(`(${this.dataForm.extra})({})`)
        } catch (e) {
          this.activeNames = '2'
          return this.$message.error('请检查"附加信息"中是否有JS语法错误')
        }

        try {
          const { data } = await this.$http({
            url: this.$http.adornUrl(
              `/block/blockrule/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              code: this.dataForm.code,
              rules: JSON.stringify({
                ...this.dataForm.rules,
                appId: this.dataForm.appId,
              }),
              extra: this.dataForm.extra,
            }),
          })

          if (data && data.code === 0) {
            this.$message.success('操作成功')
            this.visible = false
            this.$emit('refreshDataList')
          } else {
            this.$message.error(data.msg)
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    _handleAppIdError() {
      this.$refs.dataForm.validateField('appId')
    },
    _getAppVersionList() {
      const params = {
        page: 1,
        limit: 1000,
        app_id: '',
      }

      this.$store
        .dispatch('api/app/getVersionList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appVersionList = new Map(
              data.page.list
                .sort((a, b) => a.versionCode - b.versionCode)
                .map(it => [it.versionCode, it.versionName])
            )
          } else {
            this.appVersionList = new Map([])
          }
        })
    },
    _getChannelList() {
      const params = {
        page: 1,
        limit: 1000,
        app_id: '',
      }

      this.$store
        .dispatch('api/app/getChannelList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.channelList = new Map(
              // data.page.list.map(it => [it.id, it.channelName])
              data.page.list.map(it => [it.channelCode, it.channelName])
            )
            console.log('this.channelList', this.channelList)
          } else {
            this.channelList = new Map([])
          }
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.t-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-table {
  width: 100%;
  border-collapse: collapse;

  &::v-deep {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  thead {
    height: 50px;
    font-size: 16px;
    background-color: #eee;
  }

  tr {
    border-bottom: 1px #eee solid;
  }

  td {
    padding: 25px 5px;
    text-align: center;
    vertical-align: middle;

    &:nth-of-type(4n) {
      width: 80px;
    }
  }
}
</style>
