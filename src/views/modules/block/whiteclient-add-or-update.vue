<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-form-item label="配置名" prop="name">
        <el-input v-model="dataForm.name" placeholder="配置名"></el-input>
      </el-form-item>
      <el-form-item label="配置代码" prop="code">
        <el-input v-model="dataForm.code" placeholder="配置代码"></el-input>
      </el-form-item>
      <el-form-item label="设备ID" prop="udid">
        <vue-json-editor
          v-model="dataForm.udid"
          :expandedOnStart="true"
          mode="code"
        />
      </el-form-item>
      <el-form-item label="IMEI" prop="imei">
        <vue-json-editor
          v-model="dataForm.imei"
          :expandedOnStart="true"
          mode="code"
        />
      </el-form-item>
      <el-form-item label="MAC" prop="mac">
        <vue-json-editor
          v-model="dataForm.mac"
          :expandedOnStart="true"
          mode="code"
        />
      </el-form-item>
      <el-form-item label="OAID" prop="oaid">
        <vue-json-editor
          v-model="dataForm.oaid"
          :expandedOnStart="true"
          mode="code"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="dataForm.status">
          <el-option
            v-for="[key, label] in whiteClientStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <!--<el-form-item label="创建时间" prop="createdAt">-->
      <!--  <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="更新时间" prop="updatedAt">-->
      <!--  <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>-->
      <!--</el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import vueJsonEditor from 'vue-json-editor'
import { whiteClientStatus } from '@/map/common'

export default {
  components: {
    vueJsonEditor,
  },
  data() {
    return {
      visible: false,
      activeNames: ['1'],
      dataForm: {
        id: 0,
        name: '',
        code: '',
        udid: '',
        imei: '',
        mac: '',
        oaid: '',
        status: '',
        // createdAt: '',
        // updatedAt: ''
      },
      dataRule: {
        name: [{ required: true, message: '配置名不能为空', trigger: 'blur' }],
        code: [
          { required: true, message: '配置代码不能为空', trigger: 'blur' },
        ],
        udid: [{ required: true, message: '设备ID不能为空', trigger: 'blur' }],
        imei: [{ required: true, message: 'IMEI不能为空', trigger: 'blur' }],
        mac: [{ required: true, message: 'MAC不能为空', trigger: 'blur' }],
        oaid: [{ required: true, message: 'OAID不能为空', trigger: 'blur' }],
        status: [
          { required: true, message: '状态：0关 1开不能为空', trigger: 'blur' },
        ],
        // createdAt: [
        //   { required: true, message: '创建时间不能为空', trigger: 'blur' }
        // ],
        // updatedAt: [
        //   { required: true, message: '更新时间不能为空', trigger: 'blur' }
        // ]
      },
      whiteClientStatus,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/block/whiteclient/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.name = data.whiteClient.name
              this.dataForm.code = data.whiteClient.code
              try {
                this.dataForm.udid = JSON.parse(data.whiteClient.udid)
                this.dataForm.imei = JSON.parse(data.whiteClient.imei)
                this.dataForm.mac = JSON.parse(data.whiteClient.mac)
                this.dataForm.oaid = JSON.parse(data.whiteClient.oaid)
              } catch (e) {
                console.log(e)
              }
              this.dataForm.status = data.whiteClient.status
              // this.dataForm.createdAt = data.whiteClient.createdAt
              // this.dataForm.updatedAt = data.whiteClient.updatedAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/block/whiteclient/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              code: this.dataForm.code,
              udid: JSON.stringify(this.dataForm.udid),
              imei: JSON.stringify(this.dataForm.imei),
              mac: JSON.stringify(this.dataForm.mac),
              oaid: JSON.stringify(this.dataForm.oaid),
              status: this.dataForm.status,
              // 'createdAt': this.dataForm.createdAt,
              // 'updatedAt': this.dataForm.updatedAt
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
              })
              this.visible = false
              this.$emit('refreshDataList')
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
