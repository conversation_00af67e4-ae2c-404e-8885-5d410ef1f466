<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button @click="getDataList()" type="primary" icon="el-icon-search">
          查询
        </el-button>
        <el-button
          v-if="isAuth('conf:config:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          icon="el-icon-plus"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('conf:config:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      class="adapter-height"
      :max-height="tableHeight"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="自增ID"
      ></el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="配置描述"
      ></el-table-column>
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="配置代码"
      ></el-table-column>
      <el-table-column
        prop="data"
        header-align="center"
        align="center"
        label="配置内容"
      >
        <template slot-scope="{ row }">
          <el-button type="text" @click="showJson(row.data)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用ID"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="appList && appList.length">
            {{ row.appId | getListLabel(appList, 'code', 'name') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="{ row }">
          <el-tag>{{ confStatus.get(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <!--<el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      :app-list="appList"
      @refreshDataList="getDataList"
    />
    <el-dialog :visible.sync="jsonVisible">
      <vue-json-editor v-model="json" mode="tree" />
    </el-dialog>
  </div>
</template>

<script>
import vueJsonEditor from 'vue-json-editor'
import AddOrUpdate from './config-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'

import { confStatus } from '@/map/common'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      confStatus,
      appList: [],
      json: '',
      jsonVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    vueJsonEditor,
  },
  activated() {
    this.getDataList()
    this.getAppList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        key: this.dataForm.key,
      }

      this.$store
        .dispatch('api/conf/getConfigList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$store
          .dispatch('api/conf/deleteConfig', [ids])
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
      })
    },
    getAppList() {
      this.$store.dispatch('api/app/getAppListWithRole').then(({ data }) => {
        if (data && data.code === 0) {
          this.appList = data.apps
        }
      })
    },
    showJson(data) {
      this.jsonVisible = true
      try {
        this.json = JSON.parse(data)
      } catch (e) {
        console.log(e)
      }
    },
  },
}
</script>
