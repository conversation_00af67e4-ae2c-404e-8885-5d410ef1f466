<template>
  <div>
    <el-form :model="selectFormData" inline>
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model="selectFormData.appId"
          :is-show-all="false"
          clearable
        />
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <arr-select
          :list="brandList"
          v-model="selectFormData.brand"
          label-key="label"
          value-key="value"
        />
      </el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="getData">
        搜索
      </el-button>
      <el-button @click="handleAdd" icon="el-icon-plus">新增</el-button>
    </el-form>
    <el-table :data="tableData" stripe border style="width: 100%">
      <el-table-column type="index" width="50" />
      <el-table-column prop="appId" label="应用">
        <template slot-scope="{ row }">
          <color-tag :id="Number(row.appId)">
            {{ Number(row.appId) | getAppName }}
          </color-tag>
        </template>
      </el-table-column>
      <el-table-column prop="percent" label="比例" />
      <el-table-column prop="interval" label="时间间隔" />
      <el-table-column prop="brand" label="品牌" />
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="edit(scope.row)" type="text" size="small">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="isEdit ? '编辑' : '新增'"
      :visible.sync="dialogVisible"
      width="400px"
      @closed="handleClosed"
    >
      <el-form ref="form" :model="formData" label-width="80px" :rules="rules">
        <el-form-item prop="appId" label="应用">
          <app-select-component
            v-model="formData.appId"
            :is-show-all="false"
            clearable
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <arr-select
            :list="brandList"
            v-model="formData.brand"
            label-key="label"
            value-key="value"
          />
        </el-form-item>
        <el-form-item prop="percent" label="比例">
          <el-input
            type="number"
            :min="0"
            :max="100"
            v-model="formData.percent"
            clearable
            placeholder="范围说明(0-100)"
          />
          <el-alert
            title="范围说明(0-100)"
            type="info"
            style="margin-top: 10px"
          />
        </el-form-item>
        <el-form-item prop="interval" label="时间间隔">
          <el-input
            type="number"
            :min="intervalMin"
            :max="intervalMax"
            v-model="formData.interval"
            clearable
            placeholder="时间间隔"
          />
          <el-alert
            :title="`范围说明(${intervalMin}-${intervalMax})小时，整数`"
            type="info"
            style="margin-top: 10px"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { ctrCallbackRules } from '@/api/stat'
import { isInt } from '@/utils/validate'

export default {
  data() {
    return {
      ctrCallbackRules,
      tableData: [],
      dialogVisible: false,
      intervalMin: 1,
      intervalMax: 24,
      formData: {
        appId: '',
        brand: 0,
        interval: 0, // 间隔
        percent: 0, // 比例
      },
      rules: {
        appId: { required: true, message: '必填', trigger: 'blur' },
        brand: { required: true, message: '必填', trigger: 'blur' },
        interval: [
          { required: true, message: '必填', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value < this.intervalMin || value > this.intervalMax) {
                return callback(
                  new Error(`${this.intervalMin}-${this.intervalMax}之间`)
                )
              }
              if (!isInt(value)) {
                callback(new Error('必须为整数'))
              } else {
                callback()
              }
            },
          },
        ],
        percent: { required: true, message: '必填', trigger: 'blur' },
      },
      selectFormData: {
        appId: '',
        brand: 'vivo',
      },
      isEdit: false,
      brandList: [
        { label: '荣耀', value: 'honor' },
        { label: '华为', value: 'huawei' },
        { label: '小米', value: 'xiaomi' },
        { label: 'OPPO', value: 'oppo' },
        { label: 'VIVO', value: 'vivo' },
      ],
    }
  },
  activated() {
    this.getData()
  },
  methods: {
    getData() {
      ctrCallbackRules
        .upPercentList(this.selectFormData.appId, this.selectFormData.brand)
        .then(res => {
          if (res.code === 0 && res.data) {
            this.tableData = res.data
          }
        })
    },
    edit(row) {
      this.isEdit = true
      this.formData = Object.assign({}, row)
      this.formData.appId = Number(this.formData.appId)
      this.dialogVisible = true
    },
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
    },
    handleClose() {},
    handleClosed() {
      this.$refs.form.resetFields()
      for (const key in this.formData) {
        this.formData[key] = ''
      }
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()

        await ctrCallbackRules.upPercentSave({
          appId: this.formData.appId,
          brand: this.formData.brand,
          interval: this.formData.interval,
          percent: this.formData.percent,
        })
        this.$message.success('操作成功')
        this.dialogVisible = false
        this.selectFormData.appId = ''
        this.getData()
      } catch (e) {
        console.log(e)
      }
    },
  },
}
</script>
