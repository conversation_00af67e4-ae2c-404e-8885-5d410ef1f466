<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-form-item label="配置描述" prop="name">
        <el-input v-model="dataForm.name" placeholder="配置描述" />
      </el-form-item>
      <el-form-item label="配置代码" prop="code">
        <el-input v-model="dataForm.code" placeholder="配置代码" />
      </el-form-item>
      <el-form-item label="配置内容" prop="data">
        <vue-json-editor
          v-model="dataForm.data"
          :expandedOnStart="true"
          mode="code"
        />
      </el-form-item>
      <el-form-item label="应用ID" prop="appId">
        <el-select v-model="dataForm.appId">
          <el-option
            v-for="{ name, code } in appList"
            :key="code"
            :value="code"
            :label="name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio v-for="[key, label] in confStatus" :key="key" :label="key">
            {{ label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!--<el-form-item label="创建时间" prop="createdAt">-->
      <!--  <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="更新时间" prop="updatedAt">-->
      <!--  <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>-->
      <!--</el-form-item>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { confStatus } from '@/map/common'
import vueJsonEditor from '@/components/my-vue-json-editor'

export default {
  props: {
    appList: {
      type: Array,
    },
  },
  components: {
    vueJsonEditor,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        code: '',
        data: '',
        appId: 0,
        status: '',
        createdAt: '',
        updatedAt: '',
      },
      dataRule: {
        name: [
          {
            required: true,
            message: '配置描述不能为空',
            trigger: 'blur',
          },
        ],
        code: [
          {
            required: false,
            message: '配置代码不能为空',
            trigger: 'blur',
          },
        ],
        data: [
          {
            required: true,
            message: '配置内容不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '状态：1正常 0禁用不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          {
            required: true,
            message: '创建时间不能为空',
            trigger: 'blur',
          },
        ],
        updatedAt: [
          {
            required: true,
            message: '更新时间不能为空',
            trigger: 'blur',
          },
        ],
      },
      confStatus,
    }
  },

  methods: {
    onJsonChange(value) {
      console.log('value:', value)
    },
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$store
            .dispatch('api/conf/getConfInfo', this.dataForm.id)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.name = data.config.name
                this.dataForm.code = data.config.code
                try {
                  this.dataForm.data = JSON.parse(data.config.data)
                } catch (e) {
                  console.log(e)
                }
                this.dataForm.appId = data.config.appId
                this.dataForm.status = data.config.status
                // this.dataForm.createdAt = data.config.createdAt
                // this.dataForm.updatedAt = data.config.updatedAt
              }
            })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = {
            id: this.dataForm.id || undefined,
            name: this.dataForm.name,
            code: this.dataForm.code,
            data: JSON.stringify(this.dataForm.data),
            appId: this.dataForm.appId,
            status: this.dataForm.status,
            // 'createdAt': this.dataForm.createdAt,
            // 'updatedAt': this.dataForm.updatedAt
          }

          this.$store
            .dispatch('api/conf/addOrUpdateConf', data)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message.success('操作成功')
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            })
        }
      })
    },
  },
}
</script>
