<template>
  <div class="mod-config">
    <base-form
      :inline="true"
      :model.sync="dataForm"
      @submit="currentChangeHandle(1)"
    >
      <el-form-item label="规则类型" prop="ruleType">
        <el-select
          v-model="dataForm.type"
          clearable
          @change="currentChangeHandle(1)"
        >
          <template v-for="[key, label] in strategyTypeList">
            <el-option
              v-if="key !== 3"
              :value="key"
              :label="label"
              :key="key"
            />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item>
        <!--<el-button @click="getDataList()">查询</el-button>-->
        <el-button
          v-if="isAuth('activate:activatestrategy:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          icon="el-icon-plus"
        >
          新增
        </el-button>
        <!--<el-button v-if="isAuth('activate:activatestrategy:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </el-form-item>
    </base-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
        width="50"
      />
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="策略名"
      />
      <el-table-column
        prop="description"
        header-align="center"
        align="center"
        label="策略描述"
      />
      <el-table-column
        prop="category"
        header-align="center"
        align="center"
        label="适用类型"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.category === 1 ? '' : 'success'">
            {{ beApplicableTypeList.get(row.category) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="策略类型"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.type === 1 ? '' : 'success'">
            {{ strategyTypeList.get(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <!--<el-table-column-->
      <!--  prop="conditions"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="执行条件"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="extra"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="附加信息"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="uniqueType"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="显示模板"-->
      <!--/>-->
      <el-table-column
        prop="createdUser.username"
        header-align="center"
        align="center"
        label="创建人"
      />
      <el-table-column
        prop="updatedUser.username"
        header-align="center"
        align="center"
        label="编辑人"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <!--<el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AddOrUpdate from './activatestrategy-add-or-update'
import {
  con1List,
  con2List,
  beApplicableTypeList,
  strategyTypeList,
} from '@/map/common'
import BaseForm from '@/components/base-form'

export default {
  data() {
    return {
      dataForm: {
        type: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      con1List,
      con2List,
      beApplicableTypeList,
      strategyTypeList,
    }
  },
  components: {
    BaseForm,
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        ...this.dataForm,
      }
      this.$store
        .dispatch('api/marketReturn/getActivateStrategyList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activate/activatestrategy/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
