<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="540px"
    @click.native="validateCondition"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="策略名" prop="name">
        <el-input v-model="dataForm.name" placeholder="策略名"></el-input>
      </el-form-item>
      <el-form-item label="适用类型" prop="category">
        <el-select v-model="dataForm.category" style="width: 190px;">
          <el-option
            v-for="[key, label] in beApplicableTypeList"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="策略类型" prop="type">
        <el-select v-model="dataForm.type" style="width: 190px;">
          <template v-for="[key, label] in strategyTypeList">
            <el-option
              v-if="key !== 3"
              :key="key"
              :label="label"
              :value="key"
            />
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="策略模板" prop="uniqueType">
        <el-select v-model="dataForm.uniqueType" style="width: 190px;">
          <el-option
            v-for="(label, value) in strategyTemplateSelect"
            :key="value"
            :label="label"
            :value="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="renderComponents && renderComponents.length"
        label="执行条件"
        prop="conditions"
      >
        <exec-condition
          ref="execCondition"
          :render-components="renderComponents"
          @click.native.stop
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { beApplicableTypeList, strategyTypeList } from '@/map/common'
import ExecCondition from '@/components/exec-condition'
import { upperFirst } from 'lodash'

export default {
  components: {
    ExecCondition,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        description: null,
        category: 1,
        type: '',
        conditions: null,
        extra: null,
        uniqueType: null,
        createdUid: null,
        updatedUid: null,
      },
      dataRule: {
        name: [{ required: true, message: '策略名不能为空', trigger: 'blur' }],
        description: [
          { required: true, message: '策略描述不能为空', trigger: 'blur' },
        ],
        category: [
          { required: true, message: '适用类型不能为空', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '策略类型不能为空', trigger: 'blur' },
        ],
        conditions: [
          { required: true, message: '执行条件必须填完', trigger: 'blur' },
        ],
        extra: [
          { required: true, message: '附加信息不能为空', trigger: 'blur' },
        ],
        uniqueType: [
          { required: true, message: '显示模板不能为空', trigger: 'blur' },
        ],
        createdUid: [
          { required: true, message: '创建人ID不能为空', trigger: 'blur' },
        ],
        updatedUid: [
          { required: true, message: '编辑人ID不能为空', trigger: 'blur' },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        updatedAt: [
          { required: true, message: '更新时间不能为空', trigger: 'blur' },
        ],
      },
      beApplicableTypeList,
      strategyTypeList,
      strategyTemplate: {
        1: {
          con1: '条件1',
          con1_con2: '条件1 + 条件2',
          con1_con2_con3: '条件1 + 条件2 + 条件3',
          con1_con2_key3: '条件1 + 条件2 + 关键指标',
        },
        2: {
          con1: '条件1',
          con1_con2: '条件1 + 条件2',
          con1_con2_key3: '条件1 + 条件2 + 关键指标',
        },
      },
    }
  },
  watch: {
    'dataForm.type': {
      immediate: true,
      handler() {
        this.dataForm.uniqueType = ''
      },
    },
    'dataForm.uniqueType': {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          if (this.$refs.execCondition) {
            this.$refs.execCondition.resetValue()
          }
          this.dataForm.conditions = ''
          this.dataForm.extra = ''
        })
      },
    },
  },
  computed: {
    strategyTemplateSelect() {
      return this.strategyTemplate[this.dataForm.type]
    },
    renderComponents() {
      return this.dataForm.uniqueType
        .split('_')
        .filter(it => it)
        .map(it => upperFirst(it))
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/activate/activatestrategy/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.name = data.activateStrategy.name
              this.dataForm.description = data.activateStrategy.description
              this.dataForm.category = data.activateStrategy.category
              this.dataForm.type = data.activateStrategy.type
              setTimeout(() => {
                this.dataForm.uniqueType = data.activateStrategy.uniqueType
              })
              setTimeout(() => {
                this.dataForm.conditions = data.activateStrategy.conditions
                this.dataForm.extra = data.activateStrategy.extra
                this.$refs.execCondition.setValue(this.dataForm.extra)
              })
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.getInfo()
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/activate/activatestrategy/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              description: this.dataForm.description,
              category: this.dataForm.category,
              type: this.dataForm.type,
              conditions: this.dataForm.conditions,
              extra: this.dataForm.extra,
              uniqueType: this.dataForm.uniqueType,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getInfo() {
      if (this.$refs.execCondition) {
        const extraInfo = this.$refs.execCondition.buildExtraInfo()
        const buildCondition = this.$refs.execCondition.buildCondition()
        const description = this.$refs.execCondition.buildDescribe()
        if (
          extraInfo.some(it => it === '' || it === undefined || it === null)
        ) {
          this.dataForm.conditions = ''
          this.dataForm.extra = ''
          this.dataForm.description = ''
        } else {
          this.dataForm.conditions = buildCondition
          this.dataForm.extra = extraInfo
          this.dataForm.description = description
        }
        this.$refs['dataForm'].validateField('conditions')
      }
    },
    validateCondition() {
      this.getInfo()
    },
  },
}
</script>
