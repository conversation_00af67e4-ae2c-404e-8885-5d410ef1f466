<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="统计维度：1规则/2策略" prop="statType">
      <el-input v-model="dataForm.statType" placeholder="统计维度：1规则/2策略"></el-input>
    </el-form-item>
    <el-form-item label="规则名" prop="ruleName">
      <el-input v-model="dataForm.ruleName" placeholder="规则名"></el-input>
    </el-form-item>
    <el-form-item label="策略描述" prop="strategyDesc">
      <el-input v-model="dataForm.strategyDesc" placeholder="策略描述"></el-input>
    </el-form-item>
    <el-form-item label="统计规则ID" prop="ruleId">
      <el-input v-model="dataForm.ruleId" placeholder="统计规则ID"></el-input>
    </el-form-item>
    <el-form-item label="统计策略ID" prop="strategyId">
      <el-input v-model="dataForm.strategyId" placeholder="统计策略ID"></el-input>
    </el-form-item>
    <el-form-item label="回传方式：1双出价/2关键行为" prop="callbackCategory">
      <el-input v-model="dataForm.callbackCategory" placeholder="回传方式：1双出价/2关键行为"></el-input>
    </el-form-item>
    <el-form-item label="回传类型：1激活/2次留/3关键行为" prop="callbackType">
      <el-input v-model="dataForm.callbackType" placeholder="回传类型：1激活/2次留/3关键行为"></el-input>
    </el-form-item>
    <el-form-item label="回传数量" prop="callbackCount">
      <el-input v-model="dataForm.callbackCount" placeholder="回传数量"></el-input>
    </el-form-item>
    <el-form-item label="回传比例" prop="callbackPercent">
      <el-input v-model="dataForm.callbackPercent" placeholder="回传比例"></el-input>
    </el-form-item>
    <el-form-item label="回传ARPU" prop="callbackArpu">
      <el-input v-model="dataForm.callbackArpu" placeholder="回传ARPU"></el-input>
    </el-form-item>
    <el-form-item label="当天总ARPU" prop="dayArpu">
      <el-input v-model="dataForm.dayArpu" placeholder="当天总ARPU"></el-input>
    </el-form-item>
    <el-form-item label="附加数据" prop="extra">
      <el-input v-model="dataForm.extra" placeholder="附加数据"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="应用ID" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用ID"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          statType: '',
          ruleName: '',
          strategyDesc: '',
          ruleId: '',
          strategyId: '',
          callbackCategory: '',
          callbackType: '',
          callbackCount: '',
          callbackPercent: '',
          callbackArpu: '',
          dayArpu: '',
          extra: '',
          day: '',
          appId: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          statType: [
            { required: true, message: '统计维度：1规则/2策略不能为空', trigger: 'blur' }
          ],
          ruleName: [
            { required: true, message: '规则名不能为空', trigger: 'blur' }
          ],
          strategyDesc: [
            { required: true, message: '策略描述不能为空', trigger: 'blur' }
          ],
          ruleId: [
            { required: true, message: '统计规则ID不能为空', trigger: 'blur' }
          ],
          strategyId: [
            { required: true, message: '统计策略ID不能为空', trigger: 'blur' }
          ],
          callbackCategory: [
            { required: true, message: '回传方式：1双出价/2关键行为不能为空', trigger: 'blur' }
          ],
          callbackType: [
            { required: true, message: '回传类型：1激活/2次留/3关键行为不能为空', trigger: 'blur' }
          ],
          callbackCount: [
            { required: true, message: '回传数量不能为空', trigger: 'blur' }
          ],
          callbackPercent: [
            { required: true, message: '回传比例不能为空', trigger: 'blur' }
          ],
          callbackArpu: [
            { required: true, message: '回传ARPU不能为空', trigger: 'blur' }
          ],
          dayArpu: [
            { required: true, message: '当天总ARPU不能为空', trigger: 'blur' }
          ],
          extra: [
            { required: true, message: '附加数据不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          appId: [
            { required: true, message: '应用ID不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activate/advertisecallbackstat/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.statType = data.advertiseCallbackStat.statType
                this.dataForm.ruleName = data.advertiseCallbackStat.ruleName
                this.dataForm.strategyDesc = data.advertiseCallbackStat.strategyDesc
                this.dataForm.ruleId = data.advertiseCallbackStat.ruleId
                this.dataForm.strategyId = data.advertiseCallbackStat.strategyId
                this.dataForm.callbackCategory = data.advertiseCallbackStat.callbackCategory
                this.dataForm.callbackType = data.advertiseCallbackStat.callbackType
                this.dataForm.callbackCount = data.advertiseCallbackStat.callbackCount
                this.dataForm.callbackPercent = data.advertiseCallbackStat.callbackPercent
                this.dataForm.callbackArpu = data.advertiseCallbackStat.callbackArpu
                this.dataForm.dayArpu = data.advertiseCallbackStat.dayArpu
                this.dataForm.extra = data.advertiseCallbackStat.extra
                this.dataForm.day = data.advertiseCallbackStat.day
                this.dataForm.appId = data.advertiseCallbackStat.appId
                this.dataForm.createdAt = data.advertiseCallbackStat.createdAt
                this.dataForm.updatedAt = data.advertiseCallbackStat.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activate/advertisecallbackstat/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'statType': this.dataForm.statType,
                'ruleName': this.dataForm.ruleName,
                'strategyDesc': this.dataForm.strategyDesc,
                'ruleId': this.dataForm.ruleId,
                'strategyId': this.dataForm.strategyId,
                'callbackCategory': this.dataForm.callbackCategory,
                'callbackType': this.dataForm.callbackType,
                'callbackCount': this.dataForm.callbackCount,
                'callbackPercent': this.dataForm.callbackPercent,
                'callbackArpu': this.dataForm.callbackArpu,
                'dayArpu': this.dataForm.dayArpu,
                'extra': this.dataForm.extra,
                'day': this.dataForm.day,
                'appId': this.dataForm.appId,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
