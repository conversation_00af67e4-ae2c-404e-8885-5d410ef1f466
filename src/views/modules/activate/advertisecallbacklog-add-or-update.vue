<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="设备ID" prop="udid">
      <el-input v-model="dataForm.udid" placeholder="设备ID"></el-input>
    </el-form-item>
    <el-form-item label="应用ID" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用ID"></el-input>
    </el-form-item>
    <el-form-item label="广告监测ID" prop="callbackId">
      <el-input v-model="dataForm.callbackId" placeholder="广告监测ID"></el-input>
    </el-form-item>
    <el-form-item label="回传方式：1双出价/2关键行为" prop="callbackCategory">
      <el-input v-model="dataForm.callbackCategory" placeholder="回传方式：1双出价/2关键行为"></el-input>
    </el-form-item>
    <el-form-item label="回传类型：1激活/2次留/3关键行为" prop="callbackType">
      <el-input v-model="dataForm.callbackType" placeholder="回传类型：1激活/2次留/3关键行为"></el-input>
    </el-form-item>
    <el-form-item label="回传状态" prop="callbackStatus">
      <el-input v-model="dataForm.callbackStatus" placeholder="回传状态"></el-input>
    </el-form-item>
    <el-form-item label="触发回传规则ID" prop="callbackRuleId">
      <el-input v-model="dataForm.callbackRuleId" placeholder="触发回传规则ID"></el-input>
    </el-form-item>
    <el-form-item label="触发回传策略ID" prop="callbackStrategyId">
      <el-input v-model="dataForm.callbackStrategyId" placeholder="触发回传策略ID"></el-input>
    </el-form-item>
    <el-form-item label="回传HTTP状态" prop="callbackCode">
      <el-input v-model="dataForm.callbackCode" placeholder="回传HTTP状态"></el-input>
    </el-form-item>
    <el-form-item label="回传返回" prop="callbackResult">
      <el-input v-model="dataForm.callbackResult" placeholder="回传返回"></el-input>
    </el-form-item>
    <el-form-item label="回传时数据统计快照" prop="callbackSnapshot">
      <el-input v-model="dataForm.callbackSnapshot" placeholder="回传时数据统计快照"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          udid: '',
          appId: '',
          callbackId: '',
          callbackCategory: '',
          callbackType: '',
          callbackStatus: '',
          callbackRuleId: '',
          callbackStrategyId: '',
          callbackCode: '',
          callbackResult: '',
          callbackSnapshot: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          udid: [
            { required: true, message: '设备ID不能为空', trigger: 'blur' }
          ],
          appId: [
            { required: true, message: '应用ID不能为空', trigger: 'blur' }
          ],
          callbackId: [
            { required: true, message: '广告监测ID不能为空', trigger: 'blur' }
          ],
          callbackCategory: [
            { required: true, message: '回传方式：1双出价/2关键行为不能为空', trigger: 'blur' }
          ],
          callbackType: [
            { required: true, message: '回传类型：1激活/2次留/3关键行为不能为空', trigger: 'blur' }
          ],
          callbackStatus: [
            { required: true, message: '回传状态不能为空', trigger: 'blur' }
          ],
          callbackRuleId: [
            { required: true, message: '触发回传规则ID不能为空', trigger: 'blur' }
          ],
          callbackStrategyId: [
            { required: true, message: '触发回传策略ID不能为空', trigger: 'blur' }
          ],
          callbackCode: [
            { required: true, message: '回传HTTP状态不能为空', trigger: 'blur' }
          ],
          callbackResult: [
            { required: true, message: '回传返回不能为空', trigger: 'blur' }
          ],
          callbackSnapshot: [
            { required: true, message: '回传时数据统计快照不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/activate/advertisecallbacklog/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.udid = data.advertiseCallbackLog.udid
                this.dataForm.appId = data.advertiseCallbackLog.appId
                this.dataForm.callbackId = data.advertiseCallbackLog.callbackId
                this.dataForm.callbackCategory = data.advertiseCallbackLog.callbackCategory
                this.dataForm.callbackType = data.advertiseCallbackLog.callbackType
                this.dataForm.callbackStatus = data.advertiseCallbackLog.callbackStatus
                this.dataForm.callbackRuleId = data.advertiseCallbackLog.callbackRuleId
                this.dataForm.callbackStrategyId = data.advertiseCallbackLog.callbackStrategyId
                this.dataForm.callbackCode = data.advertiseCallbackLog.callbackCode
                this.dataForm.callbackResult = data.advertiseCallbackLog.callbackResult
                this.dataForm.callbackSnapshot = data.advertiseCallbackLog.callbackSnapshot
                this.dataForm.createdAt = data.advertiseCallbackLog.createdAt
                this.dataForm.updatedAt = data.advertiseCallbackLog.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/activate/advertisecallbacklog/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'udid': this.dataForm.udid,
                'appId': this.dataForm.appId,
                'callbackId': this.dataForm.callbackId,
                'callbackCategory': this.dataForm.callbackCategory,
                'callbackType': this.dataForm.callbackType,
                'callbackStatus': this.dataForm.callbackStatus,
                'callbackRuleId': this.dataForm.callbackRuleId,
                'callbackStrategyId': this.dataForm.callbackStrategyId,
                'callbackCode': this.dataForm.callbackCode,
                'callbackResult': this.dataForm.callbackResult,
                'callbackSnapshot': this.dataForm.callbackSnapshot,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
