<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="800px"
    @closed="$emit('closed')"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="应用ID" prop="appId">
        <app-select-component
          v-model="dataForm.appId"
          :is-show-all="false"
          :disabled="!!dataForm.id"
        />
      </el-form-item>
      <el-form-item label="规则类型" prop="ruleType">
        <template v-for="[key, label] in strategyTypeList">
          <el-radio
            v-if="key !== 3"
            v-model="dataForm.ruleType"
            :label="key"
            :key="key"
            :disabled="!!dataForm.id"
          >
            {{ label }}
          </el-radio>
        </template>
      </el-form-item>
      <el-form-item label="策略选择" prop="strategyId">
        <el-table
          :data="strategyList"
          max-height="400px"
          style="width: 100%"
          border
        >
          <el-table-column width="50" align="center">
            <template slot="header">
              <el-radio class="table-radio" v-model="videoIgnore" />
            </template>
            <template slot-scope="{ row }">
              <el-radio
                class="table-radio"
                v-model="dataForm.strategyId"
                :label="row.id"
              />
            </template>
          </el-table-column>
          <el-table-column prop="id" label="id" width="50" align="center" />
          <el-table-column prop="name" label="策略名" align="center" />
          <el-table-column prop="description" label="策略描述" align="center" />
        </el-table>
      </el-form-item>
      <el-form-item
        v-if="dataForm.ruleType === 2"
        label="次留类型"
        prop="extra.alive_type"
      >
        <el-select v-model="dataForm.extra.alive_type" clearable>
          <el-option
            v-for="[key, label] in aliveTypeList"
            :value="key"
            :label="label"
            :key="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="dataForm.status">
          <el-option
            v-for="[key, label] in activateRulesStatus"
            :value="key"
            :label="label"
            :key="key"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import AppSelectComponent from '@/components/app-select-component'
import { strategyTypeList } from '@/map/common'
import { activateRulesStatus, aliveTypeList } from '@/map/activate'

export default {
  components: {
    AppSelectComponent,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: undefined,
        appId: '',
        ruleType: '',
        strategyId: '',
        advertiserId: null,
        status: 0,
        extra: {
          alive_type: 1,
        },
      },
      dataRule: {
        appId: [{ required: true, message: '应用ID不能为空', trigger: 'blur' }],
        ruleType: [
          { required: true, message: '回传规则类型不能为空', trigger: 'blur' },
        ],
        strategyId: [
          { required: true, message: '策略ID不能为空', trigger: 'blur' },
        ],
        advertiserId: [
          { required: true, message: '广告主ID不能为空', trigger: 'blur' },
        ],
        'extra.alive_type': [
          { required: true, message: '次留类型不能为空', trigger: 'blur' },
        ],
      },
      strategyList: [],
      activateRulesStatus,
      strategyTypeList,
      videoIgnore: false,
      aliveTypeList,
    }
  },
  watch: {
    'dataForm.ruleType': {
      immediate: true,
      handler() {
        this.dataForm.strategyId = ''
        this.getActivateStrategyList()
      },
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || undefined
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/activate/activaterules/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.activateRules.appId
                this.dataForm.ruleType = data.activateRules.ruleType
                this.dataForm.advertiserId = data.activateRules.advertiserId
                this.dataForm.status = data.activateRules.status
                setTimeout(() => {
                  this.dataForm.strategyId = data.activateRules.strategyId
                })
                if (data.activateRules && data.activateRules.extra) {
                  this.dataForm.extra.alive_type =
                    data.activateRules.extra.alive_type
                }
              } else {
                this.$message.error(data.msg || '服务器错误')
              }
            })
            .catch(() => {
              this.$message.error('服务器错误')
            })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.handleChangeRuleType(this.dataForm.ruleType)

          this.$http({
            url: this.$http.adornUrl(
              `/activate/activaterules/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData(this.dataForm),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    /**
     * 获取策略列表
     */
    getActivateStrategyList() {
      if (!this.dataForm.ruleType) return

      const params = {
        page: 1,
        limit: 1000,
        type: this.dataForm.ruleType,
      }
      this.$store
        .dispatch('api/marketReturn/getActivateStrategyList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.strategyList = data.page.list
          } else {
            this.strategyList = []
          }
        })
    },
    handleChangeRuleType(type) {
      if (type === 1) {
        this.dataForm.extra = null
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.table-radio {
  &::v-deep {
    .el-radio__label {
      display: none;
    }
  }
}
</style>
