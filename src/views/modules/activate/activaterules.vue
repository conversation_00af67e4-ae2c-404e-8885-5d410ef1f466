<template>
  <div class="mod-config">
    <base-form :inline="true" :model.sync="dataForm" @submit="getDataList()">
      <el-form-item label="应用">
        <app-select-component
          ref="app-select-component"
          v-model="dataForm.app_id"
          @change="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          v-if="isAuth('activate:activaterules:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          icon="el-icon-plus"
        >
          新增
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('activate:activaterules:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </base-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        width="50"
        label="ID"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用ID"
        width="80"
      >
        <template slot-scope="{ row }">
          <span>{{ row.appId | getAppName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="ruleType"
        header-align="center"
        align="center"
        label="规则类型"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.ruleType === 1 ? '' : 'success'">
            {{ strategyTypeList.get(row.ruleType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.status === 0 ? 'danger' : ''">
            {{ activateRulesStatus.get(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="strategyId"
        header-align="center"
        align="center"
        label="策略ID"
        width="60"
      />
      <el-table-column
        prop="strategy.name"
        header-align="center"
        align="center"
        label="策略名"
      />
      <el-table-column
        prop="strategy.description"
        header-align="center"
        align="center"
        label="策略描述"
      />
      <el-table-column
        prop="createdUser.username"
        header-align="center"
        align="center"
        label="创建者"
      />
      <el-table-column
        prop="updatedUser.username"
        header-align="center"
        align="center"
        label="更新者"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button type="text" size="small" @click="toData(scope)">
            数据
          </el-button>
          <!-- <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
      @closed="addOrUpdateVisible = false"
    />
  </div>
</template>

<script>
import AddOrUpdate from './activaterules-add-or-update'
import { strategyTypeList } from '@/map/common'
import AppSelectComponent from '@/components/app-select-component'
import BaseForm from '@/components/base-form'
import { activateRulesStatus } from '@/map/activate'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        app_id: null,
        rule_type: '1,2',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      activateRulesStatus,
      strategyTypeList,
    }
  },
  components: {
    BaseForm,
    AppSelectComponent,
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      if (!this.dataForm.app_id) {
        const appList = this.$refs['app-select-component'].getAppList()
        this.dataForm.app_id = appList[0].code
      }

      this.$http({
        url: this.$http.adornUrl('/activate/activaterules/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activate/activaterules/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    toData(scope) {
      this.$router.push({
        path: '/activate-advertisecallbackstat',
        query: {
          rule_id: scope.row.id,
          callback_category: 1,
          app_id: scope.row.appId,
        },
      })
    },
  },
}
</script>
