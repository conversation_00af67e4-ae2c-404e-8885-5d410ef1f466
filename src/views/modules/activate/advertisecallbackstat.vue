<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="应用">
        <app-select-component
          ref="app-select"
          v-model="dataForm.app_id"
          placeholder="参数名"
          :is-show-all="false"
          clearable
          @change="changeAppOrC"
        />
      </el-form-item>
      <!--<el-form-item label="统计策略ID">-->
      <!--  <el-input-->
      <!--    v-model="dataForm.rule_id"-->
      <!--    placeholder="统计策略ID"-->
      <!--    clearable-->
      <!--  />-->
      <!--</el-form-item>-->
      <el-form-item label="回传方式">
        <el-select
          v-model="dataForm.callback_category"
          @change="changeAppOrC"
          clearable
        >
          <el-option
            v-for="[key, label] in callbackCategoryList"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="回传规则">
        <el-select v-model="dataForm.rule_id" clearable>
          <el-option
            v-for="item in activateRulesList"
            :key="item.id"
            :label="
              item.strategy && item.strategy.name
                ? item.strategy.name
                : item.name
            "
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="dataForm.day"
          type="date"
          value-format="yyyyMMdd"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChangeHandle(1)" type="primary">
          查询
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('activate:advertisecallbackstat:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('activate:advertisecallbackstat:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="id"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="自增ID"-->
      <!--  idth="50"-->
      <!--/>-->
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <span>{{ row.appId | getAppName }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column-->
      <!--  prop="statType"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="统计维度"-->
      <!--&gt;-->
      <!--  <template #default="{row}">-->
      <!--    <span>{{ statTypeList.get(row.statType) }}</span>-->
      <!--  </template>-->
      <!--</el-table-column>-->
      <el-table-column
        prop="ruleName"
        header-align="center"
        align="center"
        label="策略名"
      />
      <el-table-column
        prop="strategyDesc"
        header-align="center"
        align="center"
        label="策略描述"
      />
      <!--<el-table-column-->
      <!--  prop="ruleId"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="统计规则ID"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="strategyId"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="统计策略ID"-->
      <!--/>-->
      <el-table-column
        prop="callbackCategory"
        header-align="center"
        align="center"
        label="回传方式"
      >
        <template #default="{row}">
          <el-tag :type="row.callbackCategory === 1 ? '' : 'success'">
            {{ callbackCategoryList.get(row.callbackCategory) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="callbackType"
        header-align="center"
        align="center"
        label="回传类型"
      >
        <template #default="{row}">
          <el-tag
            :type="
              row.callbackType === 1
                ? 'warning'
                : row.callbackType === 2
                ? ''
                : 'success'
            "
          >
            {{ strategyTypeList.get(row.callbackType) }}
            <!--{{ callbackTypeList.get(row.callbackType) }}-->
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="callbackCount"
        header-align="center"
        align="center"
        label="回传数量"
      />
      <!--<el-table-column-->
      <!--  prop="callbackPercent"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="回传比例"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="callbackArpu"
        header-align="center"
        align="center"
        label="回传ARPU"
      />
      <!--<el-table-column-->
      <!--  prop="dayArpu"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="当天总ARPU"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="extra"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="附加数据"-->
      <!--/>-->

      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="140px"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
        width="140px"
      />
      <!--      <el-table-column-->
      <!--        fixed="right"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="150"-->
      <!--        label="操作">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
      <!--          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './advertisecallbackstat-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'
import {
  callbackCategoryList,
  callbackTypeList,
  statTypeList,
} from '@/map/activate'
import { strategyTypeList } from '@/map/common'
import AppSelectComponent from '@/components/app-select-component'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        app_id: null,
        day: this.$dayjs().format('YYYYMMDD'),
        callback_category: 1,
        rule_id: null,
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      callbackCategoryList,
      strategyTypeList,
      statTypeList,
      activateRulesList: [],
      callbackTypeList,
    }
  },
  // watch: {
  //   'dataForm.callback_category'(callback_category) {
  //     this.dataForm.rule_id = null
  //     this.getActivateRulesList(callback_category === 1 ? '1,2' : '3')
  //   },
  //   'dataForm.app_id'() {
  //     this.dataForm.rule_id = null
  //     this.getActivateRulesList(
  //       this.dataForm.callback_category === 1 ? '1,2' : '3'
  //     )
  //   },
  // },
  components: {
    AddOrUpdate,
    AppSelectComponent,
  },
  activated() {
    this.$nextTick(() => {
      const query = this.$route.query
      if (!query.app_id) {
        const appList = this.$refs['app-select'].getAppList()
        if (appList && appList.length) {
          this.dataForm.app_id = appList[0].code
        }
      } else {
        this.dataForm.app_id = Number(query.app_id)
      }

      query.day && (this.dataForm.day = query.day)
      query.callback_category &&
        (this.dataForm.callback_category = Number(query.callback_category))
      query.rule_id && (this.dataForm.rule_id = Number(query.rule_id))

      this.getActivateRulesListAdapter()
      this.getDataList()
    })
  },
  methods: {
    getActivateRulesListAdapter() {
      let ruleType

      switch (this.dataForm.callback_category) {
        case 1:
          ruleType = '1,2'
          break
        case 3:
          ruleType = '4'
          break
        case 2:
          ruleType = '3'
      }

      this.getActivateRulesList(ruleType)
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const dataForm = { ...this.dataForm }
      for (const key in dataForm) {
        if (!dataForm[key]) {
          delete dataForm[key]
        }
      }
      this.$http({
        url: this.$http.adornUrl('/activate/advertisecallbackstat/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/activate/advertisecallbackstat/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getActivateRulesList(rule_type) {
      this.$http({
        url: this.$http.adornUrl('/activate/activaterules/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 1000,
          rule_type,
          app_id: this.dataForm.app_id,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.activateRulesList = data.page.list
        } else {
          this.activateRulesList = []
        }
      })
    },
    changeAppOrC() {
      this.dataForm.rule_id = null
      this.getActivateRulesListAdapter()
    },
  },
}
</script>
