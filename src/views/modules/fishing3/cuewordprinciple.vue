<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="200"
  >
    <template #model>
      <el-form-item label="分类名" prop="category">
        <el-input
          v-model="modelConfig.formData.category"
          placeholder="分类名"
        ></el-input>
      </el-form-item>
      <el-form-item label="规范类型" prop="type">
        <el-select v-model="modelConfig.formData.type" style="width: 100%;">
          <el-option
            v-for="(label, key) in typeMap"
            :label="label"
            :value="Number(key)"
            :key="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类ID" prop="categoryId">
        <el-input
          v-model="modelConfig.formData.categoryId"
          placeholder="分类ID"
        />
      </el-form-item>
      <el-form-item label="提示词" prop="cueWord">
        <el-input
          type="textarea"
          v-model="modelConfig.formData.cueWord"
          placeholder="提示词"
          :rows="10"
        />
      </el-form-item>
    </template>
  </page-table>
</template>

<script>
import { cueWordPrinciple as request } from '@/api/fishing3'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    const typeMap = {
      1: '创作者',
      2: '私人助理',
    }

    return {
      typeMap,
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', width: 60 },
          { field: 'id', title: 'ID', width: 60 },
          { field: 'category', title: '分类名' },
          {
            field: 'type',
            title: '规范类型',
            formatter: ({ row }) => typeMap[row.type],
          },
          { field: 'categoryId', title: '分类ID' },
          { field: 'cueWord', title: '提示词' },
          // { field: 'createTime', title: '创建时间' },
          // { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
          // labelPosition: 'top',
        },
        formData: {
          id: null,
          category: '',
          type: '',
          categoryId: '',
          cueWord: '',
        },
        formItemMap: {
          configKey: {
            title: '参数名',
          },
          configValue: {
            title: '参数值',
            itemRender: {
              name: 'ConfigJsonEditor',
              attrs: {
                height: 500,
              },
            },
          },
          configDesc: {
            title: '描述',
            itemRender: {
              attrs: {
                type: 'textarea',
              },
            },
          },
        },
        formRule: {
          category: [{ required: true, message: '不能为空', trigger: 'blur' }],
          type: [{ required: true, message: '不能为空', trigger: 'blur' }],
          categoryId: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          cueWord: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },

  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
