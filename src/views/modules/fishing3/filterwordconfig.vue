<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="210"
  >
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #model>
      <el-form-item label="过滤词" prop="word">
        <el-input v-model="modelConfig.formData.word" placeholder="过滤词" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <radio-status v-model="modelConfig.formData.status" />
      </el-form-item>
    </template>
  </page-table>
</template>

<script>
import { filterWordConfig as request } from '@/api/fishing3'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', width: 60 },
          { field: 'id', title: 'ID', width: 60 },
          { field: 'word', title: '过滤词' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          // {
          //   field: 'type',
          //   title: '规范类型',
          //   formatter: ({ row }) => typeMap[row.type],
          // },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          word: '',
          status: '',
        },
        formRule: {
          word: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },

  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
