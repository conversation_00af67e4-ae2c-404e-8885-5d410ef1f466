<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="230"
  >
    <template #table_item_unlockTypes="{row}">
      <color-tag :id="row.unlockType">
        {{ baseConfigMap.unlockType.get(row.unlockType) }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_fishId="{row}">
      <color-tag :id="row.fishId">
        {{ getFishName(row.fishId) }}
      </color-tag>
    </template>
  </page-table>
</template>

<script>
import {
  fishingCollectConfig as request,
  baseConfigRequest,
} from '@/api/fishing3'
import { baseConfigMap, drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      baseConfigMap,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          {
            field: 'fishId',
            title: '鱼',
            slots: {
              default: 'table_item_fishId',
            },
          },
          {
            field: 'unlockType',
            title: '解锁方式',
            slots: {
              default: 'table_item_unlockTypes',
            },
          },
          { field: 'description', title: '描述' },
          { field: 'toast', title: 'toast文案' },
          { field: 'rankNum', title: '排序' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '100px',
        },
        formData: {
          id: null,
          fishId: '',
          unlockType: '',
          description: '',
          toast: '',
          rankNum: '',
          status: '',
        },
        formItemMap: {
          fishId: {
            title: '鱼',
            itemRender: {
              name: 'arr-select',
              attrs: {
                type: 'number',
                list: [],
                valueKey: 'fishId',
                labelKey: 'name',
              },
            },
          },
          unlockType: {
            title: '解锁方式',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: baseConfigMap.unlockType,
              },
            },
          },
          toast: {
            title: 'toast文案',
          },
          rankNum: {
            title: '排序',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          description: {
            title: '描述',
            itemRender: {
              attrs: {
                type: 'textarea',
              },
            },
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
        },
        formRule: {
          fishId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          toast: [{ required: true, message: '不能为空', trigger: 'blur' }],
          unlockType: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          description: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          rankNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  activated() {
    baseConfigRequest
      .selectAll({ currentPage: 1, pageSize: 10000 })
      .then(res => {
        if (res.code === 0 && res.page && res.page.list) {
          this.modelConfig.formItemMap.fishId.itemRender.attrs.list =
            res.page.list
          this.fishList = res.page.list
        }
      })
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
