<template>
  <!--operate-width="330"-->
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    :operate-width="100"
  />
</template>

<script>
import { rechargeSetConfig as request } from '@/api/fishing3'

export default {
  data() {
    return {
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', width: 60 },
          { field: 'id', title: 'ID', width: 60 },
          { field: 'setName', title: '套餐名称' },
          {
            field: 'price',
            title: '套餐价格',
            formatter: ({ row }) => `${row.price} 元`,
          },
          {
            field: 'originPrice',
            title: '原价',
            formatter: ({ row }) => `${row.originPrice} 元`,
          },
          {
            field: 'dayPrice',
            title: '每天价格',
            formatter: ({ row }) => `${row.dayPrice} 元`,
          },
          {
            field: 'duration',
            title: '套餐有效时间',
            formatter: ({ row }) => `${row.duration} 天`,
          },

          // {
          //   field: 'type',
          //   title: '规范类型',
          //   formatter: ({ row }) => typeMap[row.type],
          // },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
        formData: {
          id: null,
          setName: '',
          price: '',
          originPrice: '',
          dayPrice: '',
          duration: '',
        },
        formItemMap: {
          setName: {
            title: '套餐名称',
            itemRender: {
              attrs: {
                // type: 'number',
              },
            },
          },
          price: {
            title: '套餐价格(元)',
            itemRender: {
              attrs: {
                type: 'number',
                clearable: true,
              },
            },
          },
          originPrice: {
            title: '原价(元)',
            itemRender: {
              attrs: {
                type: 'number',
                clearable: true,
              },
            },
          },
          dayPrice: {
            title: '每天价格(元)',
            itemRender: {
              attrs: {
                type: 'number',
                clearable: true,
              },
            },
          },
          duration: {
            title: '套餐有效时间(天)',
            itemRender: {
              attrs: {
                type: 'number',
                clearable: true,
              },
            },
          },
        },
        formRule: {
          setName: [{ required: true, message: '不能为空', trigger: 'blur' }],
          price: [{ required: true, message: '不能为空', trigger: 'blur' }],
          originPrice: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          dayPrice: [{ required: true, message: '不能为空', trigger: 'blur' }],
          duration: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
}
</script>
