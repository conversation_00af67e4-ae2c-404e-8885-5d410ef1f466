<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
    ]"
    operate-width="330"
  >
    <template #table_item_type="{row}">
      <color-tag :id="row.type">
        {{ taskConfigMap.typeList.get(row.type) }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_fishId="{row}">
      <color-tag :id="row.fishId">
        {{ getFishName(row.fishId) }}
      </color-tag>
    </template>
  </page-table>
</template>

<script>
import { taskConfigRequest as request, baseConfigRequest } from '@/api/fishing3'
import { taskConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      taskConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          {
            field: 'type',
            title: '类型',
            width: 100,
            slots: {
              default: 'table_item_type',
            },
          },
          {
            field: 'fishId',
            title: '鱼id',
            slots: {
              default: 'table_item_fishId',
            },
          },
          { field: 'title', title: '标题' },
          { field: 'askTimes', title: '任务要求次数' },
          { field: 'rewardShow', title: '奖励显示' },
          { field: 'rewardMin', title: '奖励区间' },
          { field: 'rewardMax', title: '奖励区间' },
          { field: 'rankNum', title: '排序' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '110px',
        },
        formData: {
          id: null,
          type: '',
          fishId: '',
          title: '',
          askTimes: '',
          rewardShow: '',
          rewardMin: '',
          rewardMax: '',
          rankNum: '',
          status: '',
        },
        formItemMap: {
          type: {
            title: '类型',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: taskConfigMap.typeList,
              },
            },
          },
          amount: {
            title: '额度',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          fishId: {
            title: '鱼',
            itemRender: {
              name: 'arr-select',
              attrs: {
                type: 'number',
                list: [],
                valueKey: 'fishId',
                labelKey: 'name',
              },
            },
          },
          title: {
            title: '标题',
          },

          rewardShow: {
            title: '奖励显示',
          },

          askTimes: {
            title: '任务要求次数',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          rewardMin: {
            title: '奖励区间小',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          rewardMax: {
            title: '奖励区间大',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          rankNum: {
            title: '排序',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
        },
        formRule: {
          type: [{ required: true, message: '不能为空', trigger: 'blur' }],
          fishId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          title: [{ required: true, message: '不能为空', trigger: 'blur' }],
          askTimes: [{ required: true, message: '不能为空', trigger: 'blur' }],
          rewardShow: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          rewardMin: [{ required: true, message: '不能为空', trigger: 'blur' }],
          rewardMax: [{ required: true, message: '不能为空', trigger: 'blur' }],
          rankNum: [{ required: true, message: '不能为空', trigger: 'blur' }],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  activated() {
    baseConfigRequest
      .selectAll({ currentPage: 1, pageSize: 10000 })
      .then(res => {
        if (res.code === 0 && res.page && res.page.list) {
          this.modelConfig.formItemMap.fishId.itemRender.attrs.list =
            res.page.list
          this.fishList = res.page.list
        }
      })
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
