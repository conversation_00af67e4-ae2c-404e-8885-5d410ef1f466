<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="currentChangeHandle(1)"
    >
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item prop="userId" label="用户ID">
        <el-input v-model="dataForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChangeHandle(1)" type="primary">
          查询
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column
        prop="withdrawalSerialNo"
        header-align="center"
        align="center"
        label="提现流水号"
      />
      <el-table-column
        prop="userId"
        header-align="center"
        align="center"
        label="用户id"
      />
      <el-table-column
        prop="appCode"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <el-tag>
            {{ row.appCode | getListLabel(appList, 'code', 'name') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="withdrawalAmount"
        header-align="center"
        align="center"
        label="提现金额"
      />
      <el-table-column
        prop="withdrawalConfigId"
        header-align="center"
        align="center"
        label="提现设置表id"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="{ row }">
          <el-tag>
            {{ withdrawStatus.get(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="riskAdvice"
        header-align="center"
        align="center"
        label="风险建议"
      >
        <template
          v-if="row.riskAdvice && row.riskAdvice.advice"
          slot-scope="{ row }"
        >
          <el-tag
            :type="
              row.riskAdvice.advice.toUpperCase() !== 'PASS' ? 'warning' : ''
            "
          >
            {{ atSuggestion.get(row.riskAdvice.advice.toUpperCase()) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="riskAdvice"
        header-align="center"
        align="center"
        label="风险建议标签"
      >
        <template slot-scope="{ row }">
          <template v-if="row.riskAdvice && row.riskAdvice.tags">
            <el-tag
              v-for="(item, index) in getSuggestionTag(row.riskAdvice.tags)"
              :key="index"
            >
              {{ item }}
            </el-tag>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script>
import { withdrawStatus, atSuggestion, riskTags } from '@/map/common'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        userId: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      withdrawStatus,
      atSuggestion,
      riskTags,
      appList: [],
    }
  },
  activated() {
    this.init()
    this.getDataList()
    this.getAppList()
  },
  methods: {
    init() {
      this.dataForm.userId = this.$route.query.user_id || ''
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/fishing3/fishinguserwithdrawalrecord/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.withdrawalSerialNo
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl(
            '/fishing3/fishinguserwithdrawalrecord/delete'
          ),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getAppList() {
      const params = {
        page: 1,
        limit: 100,
      }

      this.$store.dispatch('api/app/getAppList', params).then(({ data }) => {
        if (data && data.code === 0) {
          this.appList = data.page.list
        }
      })
    },
    getSuggestionTag(tags) {
      if (!tags) return []

      try {
        let arr = []
        const list = JSON.parse(tags)
        for (let i = 0; i < list.length; i++) {
          const item = this.riskTags.get(list[i])
          item && arr.push(item)
        }
        return arr
      } catch (e) {
        return []
      }
    },
  },
}
</script>
