<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      // 'insert',
      'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    operate-width="180"
  >
    <template #table_item_type="{row}">
      <color-tag :id="row.drawType">
        {{ drawConfigMap.typeList.get(row.drawType) }}
      </color-tag>
    </template>
    <template #table_item_rewardFishType="{row}">
      <tag-status
        :status="row.rewardFishType"
        active-text="是"
        inactive-text="否"
      />
    </template>
    <template #table_item_rewardType="{row}">
      <color-tag :id="row.rewardType">
        {{ baseConfigMap.rewardType.get(row.rewardType) }}
      </color-tag>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>
  </page-table>
</template>

<script>
import { baseConfigRequest as request } from '@/api/fishing3'
import { baseConfigMap, drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      baseConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'fishId', title: '鱼id' },
          { field: 'name', title: '名称' },
          {
            field: 'icon',
            title: '图标',
            slots: {
              default: 'table_item_icon',
            },
          },
          {
            field: 'rewardFishType',
            title: '增加钓鱼阶段奖励',
            slots: {
              default: 'table_item_rewardFishType',
            },
          },
          { field: 'dailyLimit', title: '每日上限' },
          { field: 'duration', title: '持续时间' },
          {
            field: 'rewardType',
            title: '奖励类型',
            slots: {
              default: 'table_item_rewardType',
            },
          },
          { field: 'rewardAmount', title: '奖励金额' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
        formData: {
          id: null,
          fishId: '',
          name: '',
          icon: '',
          rewardFishType: '',
          dailyLimit: '',
          duration: '',
          rewardType: '',
          rewardAmount: '',
        },
        formItemMap: {
          drawType: {
            title: '类型',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: drawConfigMap.typeList,
              },
            },
          },
          fishId: {
            title: '鱼ID',
            itemRender: {
              attrs: {
                disabled: true,
              },
            },
          },
          name: {
            title: '名称',
          },
          icon: {
            title: '图标',
            // itemRender: {
            //   attrs: {
            //     disabled: true,
            //   },
            // },
          },
          rewardFishType: {
            title: '增加钓鱼阶段奖励',
            itemRender: {
              name: 'radio-status',
              attrs: {
                activeText: '是',
                inactiveText: '否',
              },
            },
          },
          dailyLimit: {
            title: '每日上限',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          duration: {
            title: '持续时间',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          rewardType: {
            title: '奖励类型',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: baseConfigMap.rewardType,
              },
            },
          },
          rewardAmount: {
            title: '奖励金额',
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
        },
        formRule: {
          fishId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          name: [{ required: true, message: '不能为空', trigger: 'blur' }],
          icon: [{ required: true, message: '不能为空', trigger: 'blur' }],
          rewardFishType: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          dailyLimit: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          duration: [{ required: true, message: '不能为空', trigger: 'blur' }],
          rewardType: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          rewardAmount: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
        },
      },
    }
  },
}
</script>
