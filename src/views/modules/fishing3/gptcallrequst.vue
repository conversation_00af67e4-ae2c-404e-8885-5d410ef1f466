<template>
  <!--operate-width="330"-->
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
    ]"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appCode"
        :is-show-all="false"
      />
    </template>
    <template #table_item_success="{row}">
      <tag-status
        active-text="成功"
        inactive-text="失败"
        :status="row.success"
      />
    </template>
  </page-table>
</template>

<script>
import { gptCallRequst as request } from '@/api/fishing3'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', width: 60 },
          { field: 'id', title: 'ID', width: 60 },
          { field: 'requestTime', title: '调用时间', width: 160 },
          { field: 'costTime', title: '接口花费时间', width: 120 },
          { field: 'userId', title: '用户ID', width: 60 },
          { field: 'createdAt', title: '创建时间', width: 160 },
          { field: 'question', title: '问题' },
          {
            field: 'answer',
            title: '答案',
            showOverflow: 'tooltip',
          },
          {
            field: 'success',
            title: '状态',
            width: 80,
            slots: {
              default: 'table_item_success',
            },
          },
          // {
          //   field: 'type',
          //   title: '规范类型',
          //   formatter: ({ row }) => typeMap[row.type],
          // },
        ],
        formConfig: {
          items: [
            {
              field: 'appCode',
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        // formData: {
        //   id: null,
        //   word: '',
        //   status: '',
        // },
        // formRule: {
        //   word: [{ required: true, message: '不能为空', trigger: 'blur' }],
        //   status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // },
      },
      selectFormData: {
        appCode: 10156,
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
    }
  },
}
</script>
