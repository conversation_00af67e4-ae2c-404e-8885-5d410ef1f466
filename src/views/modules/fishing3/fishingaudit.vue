<template>
  <div class="page-table-height" style="display:flex; flex-direction: column;">
    <el-form :inline="true" :model="dataForm">
      <el-form-item prop="status" label="账户状态">
        <el-select v-model="dataForm.userStatus" clearable>
          <el-option
            v-for="[key, label] in userStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="userId" label="用户id">
        <el-input
          v-model.trim="dataForm.userId"
          clearable
          placeholder="用户id"
        />
      </el-form-item>
      <el-form-item prop="withdrawalSerialNo" label="提现流水号">
        <el-input
          v-model.trim="dataForm.withdrawalSerialNo"
          clearable
          placeholder="提现流水号"
        />
      </el-form-item>
      <el-form-item label="提现状态">
        <el-select v-model="dataForm.status" clearable>
          <el-option
            v-for="[key, label] in withdrawStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="提现金额">
        <el-select
          v-model.number="dataForm.withdrawalAmount"
          placeholder="提现金额"
          clearable
        >
          <el-option
            v-for="item in appWithdrawalConfigList"
            :key="item.id"
            :label="item.amount"
            :value="item.amount"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="APP版本">
        <app-version-select
          :app-id="config.appId"
          :multiple="false"
          :isShowTools="false"
          clearable
          select-key="versionName"
          v-model="dataForm.appVersion"
        />
      </el-form-item>

      <el-form-item label="时间">
        <el-date-picker
          clearable
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:00']"
          style="width: 400px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="onSubmit">
          查询
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
      </el-form-item>
    </el-form>
    <div class="table-height">
      <el-table
        :data="dataList"
        stripe
        style="width: 100%"
        border
        v-loading="dataListLoading"
        height="100%"
      >
        <el-table-column type="index" width="50" label="序号" align="center" />

        <!--待给字段-->
        <!--<el-table-column label="提现类型" align="center" width="90" />-->

        <el-table-column
          prop="createdAt"
          label="申请提现时间"
          align="center"
          width="135"
        />
        <el-table-column
          prop="lastWithdrawalTime"
          label="上次提现时间"
          align="center"
          width="135"
        />
        <el-table-column prop="userId" label="用户ID" align="center" />
        <!--<el-table-column-->
        <!--  prop="openid"-->
        <!--  label="微信openId/unionId"-->
        <!--  align="center"-->
        <!--  width="145"-->
        <!--/>-->
        <el-table-column
          prop="withdrawalSerialNo"
          label="提现流水编号"
          align="center"
          width="115"
        />
        <el-table-column
          prop="registerTime"
          label="注册时间"
          align="center"
          width="135"
        />
        <el-table-column
          prop="withdrawalAmount"
          label="提现金额/元"
          align="center"
          width="100"
        />
        <el-table-column
          prop="coinBalance"
          label="当前余额/元"
          align="center"
          width="95"
        />
        <el-table-column
          prop="grandTotalAmount"
          label="累计提现金额/元"
          align="center"
          width="125"
        />
        <el-table-column
          prop="withdrawalTimes"
          label="累计提现次数"
          align="center"
          width="100"
        />
        <el-table-column label="版本" prop="appVersion" align="center" />
        <el-table-column
          prop="status"
          label="提现状态"
          align="center"
          width="100"
        >
          <template slot-scope="{ row }">
            <color-tag :id="row.status">
              {{ withdrawStatus.get(row.status) }}
            </color-tag>
          </template>
        </el-table-column>

        <!--<el-table-column prop="ecpm" label="总ecpm" align="center" />-->
        <el-table-column
          prop="intervalEcpm"
          label="提现区间累计ecpm"
          align="center"
          width="140"
        />
        <el-table-column
          prop="intervalCoin"
          label="提现区间累计金额"
          align="center"
          width="140"
        />
        <el-table-column
          prop="rewardExposure"
          label="激励视频曝光"
          align="center"
          width="100"
        />
        <el-table-column prop="auditBy" label="审核人" align="center" />
        <el-table-column prop="userStatus" label="用户状态" align="center">
          <template slot-scope="{ row }">
            <!--<el-tag v-if="userStatus.get(row.userStatus)">-->
            <!--  {{ userStatus.get(row.userStatus) }}-->
            <!--</el-tag>-->
            <color-tag :id="row.userStatus">
              {{ userStatus.get(row.userStatus) }}
            </color-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" />
        <el-table-column
          prop="atAdvice"
          label="AT建议"
          align="center"
          width="80px"
        >
          <template v-if="row.atAdvice" slot-scope="{ row }">
            <el-tag
              :type="
                row.atAdvice.toUpperCase().includes('PASS') ? '' : 'warning'
              "
            >
              {{ getAtSuggestionLabel(row.atAdvice) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="atDesc"
          label="AT风险建议"
          align="center"
          width="120px"
        >
          <template v-if="getRiskTags(row.atDesc).length" slot-scope="{ row }">
            <el-tag
              v-for="(item, index) in getRiskTags(row.atDesc)"
              :key="index"
              type="warning"
            >
              {{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="cheatCode"
          label="反作弊建议"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">
            <el-tag
              v-for="(item, index) in getAntiCheatingList(row.cheatCode)"
              :key="index"
              type="warning"
            >
              {{ item }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="failedReason" label="错误信息" align="center" />
        <el-table-column
          prop="loginTime"
          label="最近登录时间"
          align="center"
          width="135px"
        />
        <!--fixed="right"-->
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              :disabled="scope.row.status !== 0"
              size="mini"
              type="text"
              @click="confirmMakeMoney(scope.row)"
            >
              确认打款
            </el-button>
            <el-button
              :disabled="
                !(
                  scope.row.status !== 2 &&
                  scope.row.status !== 3 &&
                  scope.row.status !== 4
                )
              "
              size="mini"
              type="text"
              @click="refuseMakeMoney(scope.row)"
            >
              不予打款
            </el-button>
            <el-button
              type="text"
              :disabled="scope.row.status !== 3"
              @click="genOrderAgain(scope.row)"
            >
              重新生成订单
            </el-button>
            <!--<p>-->
            <!--  <router-link :to="`/whowin-appuser?user_id=${scope.row.userId}`">-->
            <!--    账号管理-->
            <!--  </router-link>-->
            <!--</p>-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <el-dialog
      :visible.sync="visibleRejectDialog"
      width="500px"
      title="驳回理由"
    >
      <el-form :model="rejectDataForm" label-width="80px">
        <el-form-item label="选择类型">
          <el-radio-group
            v-model="rejectDataForm.type"
            @change="changeReasonType"
          >
            <el-radio :label="1">预设理由</el-radio>
            <el-radio :label="2">自定义理由</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择理由">
          <el-input v-model="rejectDataForm.reason" />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="visibleRejectDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmReject">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  withdrawStatus,
  riskLevel,
  antiCheating,
  atSuggestion,
  userStatus,
} from '@/map/common'
import config from './config'
import { withdrawalConfigRequest } from '@/api/fishing3'
import AppVersionSelect from '@/components/app-version-select'
import { auditMap } from '@/map/fishing'

const presetReason = '账号存在异常'

export default {
  components: { AppVersionSelect },
  data() {
    return {
      config,
      auditMap,
      pageIndex: 1,
      pageSize: 100,
      dataList: [],
      totalPage: 0,
      dataForm: {
        appCode: config.appCode,
        status: '',
        category: '',
        userStatus: '',
        configId: '',
        endTime: '',
        beginTime: '',
        withdrawalSerialNo: '',
        userId: '',
        withdrawalAmount: '',
        appVersion: '',
      },
      appWithdrawalConfigList: [],
      withdrawStatus,
      riskLevel,
      antiCheating,
      timeRange: '',
      dataListLoading: false,
      atSuggestion,
      userStatus,
      visibleRejectDialog: false,
      rejectDataForm: {
        type: 1,
        reason: presetReason,
        withdrawalSerialNo: '',
      },
    }
  },
  watch: {
    timeRange(t) {
      if (t) {
        this.dataForm.beginTime = t[0]
        this.dataForm.endTime = t[1]
      } else {
        this.dataForm.beginTime = ''
        this.dataForm.endTime = ''
      }
    },
  },
  activated() {
    this.getDataList()
    this.getWithdrawalConfigList()
  },
  methods: {
    changeReasonType(t) {
      this.rejectDataForm.reason = t === 1 ? presetReason : ''
    },
    onSubmit() {
      this.currentChangeHandle(1)
    },
    getWithdrawalConfigList() {
      withdrawalConfigRequest
        .selectAll({ currentPage: 1, pageSize: 10000, version: '', status: '' })
        .then(res => {
          if (res.code === 0 && res.page && res.page.list) {
            this.appWithdrawalConfigList = res.page.list
          } else {
            this.appWithdrawalConfigList = []
          }
        })
        .catch(() => {
          this.appWithdrawalConfigList = []
        })
    },
    getDataList() {
      this.dataListLoading = true

      this.$http({
        url: this.$http.adornUrl(
          '/fishing3/fishinguserwithdrawalrecord/audit_list'
        ),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.$message.error(data.message || '服务器错误')
            this.dataList = []
            this.totalPage = 0
          }
        })
        .catch(() => this.$message.error('服务器错误'))
        .finally(() => (this.dataListLoading = false))
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    refuseMakeMoney(row) {
      this.visibleRejectDialog = true
      this.rejectDataForm.withdrawalSerialNo = row.withdrawalSerialNo
    },
    confirmReject() {
      this.rejectMakeMoney(
        this.rejectDataForm.withdrawalSerialNo,
        this.rejectDataForm.reason
      )
    },
    confirmMakeMoney(row) {
      if (row.status === 4 || row.status === 5) {
        return this.$message.error('此账号已申请注销，不可进行打款！')
      }
      this.$confirm(`确认打款吗?打款成功将不可追回`, '确认打款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.makeMoney(row.withdrawalSerialNo)
      })
    },
    makeMoney(withdrawalSerialNo) {
      this.$http({
        url: this.$http.adornUrl(
          '/fishing3/fishinguserwithdrawalrecord/withdrawalMoney'
        ),
        method: 'put',
        data: this.$http.adornData({
          withdrawalSerialNo,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('打款成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    rejectMakeMoney(withdrawalSerialNo, remark) {
      this.$http({
        url: this.$http.adornUrl(
          '/fishing3/fishinguserwithdrawalrecord/withdrawal_reject'
        ),
        method: 'put',
        data: this.$http.adornData({
          remark,
          withdrawalSerialNo,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('操作成功')
            this.visibleRejectDialog = false
            this.getDataList()
          } else {
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 获取反风险作弊建议
    getAntiCheatingList(cheatCode) {
      try {
        return this.map2Arr(JSON.parse(cheatCode), antiCheating)
      } catch (e) {
        return null
      }
    },
    getRiskTags(tags) {
      if (!tags) return []

      try {
        let arr = []
        const list = JSON.parse(JSON.parse(tags))
        for (let i = 0; i < list.length; i++) {
          const item = auditMap.riskTags.get(list[i])
          item && arr.push(item)
        }
        return arr
      } catch (e) {
        return []
      }
    },
    map2Arr(str, map) {
      if (!str) return []
      let result = []
      const codeList = str.split('/')
      codeList.forEach(it => {
        const item = map.get(Number(it))
        item && result.push(item)
      })
      return result
    },
    genOrderAgain(row) {
      this.$confirm(`确认重新生成订单吗？`, '重新生成订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(
            '/fishing3/fishinguserwithdrawalrecord/reOrder'
          ),
          method: 'post',
          data: this.$http.adornData({
            remark: row.remark,
            withdrawalSerialNo: row.withdrawalSerialNo,
          }),
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.success('成功')
            } else {
              this.$message.error(data.message || '服务器错误')
            }
          })
          .catch(() => this.$message.error('服务器错误'))
      })
    },
    getAtSuggestionLabel(str) {
      try {
        return atSuggestion.get(JSON.parse(str).toUpperCase()) || null
      } catch (e) {
        return null
      }
    },
  },
}
</script>

<style scoped>
.table-height {
  height: Calc(100vh - 135px - 32px - 17px - 94px);
}
</style>
