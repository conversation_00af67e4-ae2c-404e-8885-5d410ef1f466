<template>
  <!--operate-width="330"-->
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    :show-operate="false"
    :before-select="beforeSelect"
  >
    <template #form_appCode>
      <app-select-component
        v-model="selectFormData.appCode"
        :is-show-all="false"
      />
    </template>
    <template #form_status>
      <el-select v-model="selectFormData.status" clearable>
        <el-option
          v-for="(value, key) in statusMap"
          :label="value"
          :value="key"
          :key="key"
        />
      </el-select>
    </template>
    <template #table_item_payType="{row}">
      <color-tag :id="row.payType">
        {{ payTypeMap[row.payType] }}
      </color-tag>
    </template>
    <template #table_item_handleStatus="{row}">
      <color-tag :id="row.handleStatus">
        {{ handleStatusMap[row.handleStatus] }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <color-tag :id="row.status">
        {{ statusMap[row.status] }}
      </color-tag>
    </template>
  </page-table>
</template>

<script>
import { rechargeRecord as request } from '@/api/fishing3'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    const statusMap = {
      1: '未付款',
      2: '已付款',
      3: '系统退款',
      4: '主动退款',
    }
    const handleStatusMap = {
      0: '无需处理',
      1: '处理中',
      2: '已处理',
      3: '待退款',
    }
    const payTypeMap = {
      1: '支付宝',
      2: '微信-H5',
      3: '微信-APP',
    }

    return {
      statusMap,
      handleStatusMap,
      payTypeMap,
      fishList: [],
      drawConfigMap,
      request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          // { type: 'seq', title: '序号', width: 60 },
          { field: 'id', title: 'ID', width: 60 },
          { field: 'userId', title: '用户ID' },
          { field: 'nickName', title: '昵称' },
          { field: 'registerTime', title: '注册时间' },
          { field: 'rechargeAmount', title: '充值金额' },
          { field: 'setId', title: '套餐id' },
          { field: 'setName', title: '充值套餐' },
          { field: 'orderNo', title: '订单号' },
          { field: 'updatedAt', title: '更新时间', width: 180 },
          { field: 'createdAt', title: '充值时间', width: 180 },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          {
            field: 'handleStatus',
            title: '处理状态',
            slots: {
              default: 'table_item_handleStatus',
            },
          },
          {
            field: 'payType',
            title: '支付方式',
            slots: {
              default: 'table_item_payType',
            },
          },
          // {
          //   field: 'type',
          //   title: '规范类型',
          //   formatter: ({ row }) => typeMap[row.type],
          // },
        ],
        formConfig: {
          items: [
            {
              field: 'appCode',
              title: '应用',
              slots: {
                default: 'form_appCode',
              },
            },
            {
              field: 'status',
              title: '状态',
              slots: {
                default: 'form_status',
              },
            },
          ],
        },
      },
      selectFormData: {
        appCode: 10156,
        status: '',
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        // formData: {
        //   id: null,
        //   word: '',
        //   status: '',
        // },
        // formRule: {
        //   word: [{ required: true, message: '不能为空', trigger: 'blur' }],
        //   status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // },
      },
      beforeSelect: () => {
        return {
          ...this.selectFormData,
        }
      },
    }
  },
}
</script>
