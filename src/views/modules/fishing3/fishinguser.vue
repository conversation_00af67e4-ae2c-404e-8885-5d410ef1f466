<template>
  <div class="page-table-height" style="display:flex; flex-direction: column">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="currentChangeHandle(1)"
    >
      <el-form-item prop="userId" label="用户ID">
        <el-input v-model="dataForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item prop="openId" label="微信ID">
        <el-input v-model="dataForm.openId" placeholder="微信ID" clearable />
      </el-form-item>
      <el-form-item label="注册时间">
        <el-date-picker
          clearable
          v-model="rangeTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:00']"
        />
      </el-form-item>
      <el-form-item prop="status" label="账户状态">
        <map-select
          :list="userInfoMap.userStatus"
          v-model="dataForm.status"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          icon="el-icon-search"
          @click="currentChangeHandle(1)"
          type="primary"
        >
          查询
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="$downloadTableToExcel()"
        >
          下载Excel
        </el-button>
        <!--<el-button v-if="isAuth('packet:appuser:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <!--<el-button v-if="isAuth('packet:appuser:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </el-form-item>
    </el-form>

    <div style="flex: 1">
      <el-table
        :data="dataList"
        border
        v-loading="dataListLoading"
        @selection-change="selectionChangeHandle"
        @sort-change="sortChange"
        style="width: 100%;"
        height="100%"
      >
        <!--<el-table-column-->
        <!--  type="selection"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  width="50"-->
        <!--&gt;</el-table-column>-->
        <el-table-column
          type="index"
          header-align="center"
          align="center"
          width="50"
          label="序号"
        />
        <el-table-column
          prop="id"
          header-align="center"
          align="center"
          label="用户ID"
          width="80"
        />
        <el-table-column
          prop="openId"
          header-align="center"
          align="center"
          label="微信openid/unionid"
        />
        <el-table-column
          prop="wechatAvatar"
          header-align="center"
          align="center"
          label="微信头像"
          width="180"
        >
          <template slot-scope="{ row }">
            <img :src="row.wechatAvatar" alt="" />
          </template>
        </el-table-column>
        <el-table-column
          prop="nickName"
          header-align="center"
          align="center"
          label="微信昵称"
        />
        <el-table-column
          prop="createdAt"
          header-align="center"
          align="center"
          label="注册时间"
        />
        <el-table-column
          prop="loginTime"
          header-align="center"
          align="center"
          label="最近登录时间"
        />
        <el-table-column
          prop="status"
          header-align="center"
          align="center"
          label="账户状态"
        >
          <template slot-scope="{ row }">
            <el-tag>
              {{ userInfoMap.userStatus.get(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="riskLevel"
          header-align="center"
          align="center"
          label="风险等级"
        >
          <template #default="{row}">
            <color-tag v-if="row.riskLevel" :id="row.riskLevel">
              {{ userInfoMap.riskLevel.get(row.riskLevel) }}
            </color-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          header-align="center"
          align="center"
          label="备注"
        />
        <!--<el-table-column-->
        <!--  prop="updatedBy"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  label="操作人"-->
        <!--/>-->
        <!--<el-table-column-->
        <!--  fixed="right"-->
        <!--  header-align="center"-->
        <!--  align="center"-->
        <!--  width="160"-->
        <!--  label="操作"-->
        <!--&gt;-->
        <!--  <template slot-scope="scope">-->
        <!--    <el-button type="text" size="small" @click="banOrDismiss(scope.row)">-->
        <!--      {{ scope.row.status === 4 ? '解除' : '封禁' }}-->
        <!--    </el-button>-->
        <!--    <el-button-->
        <!--      type="text"-->
        <!--      size="small"-->
        <!--      @click="withdrawDetail(scope.row)"-->
        <!--    >-->
        <!--      提现记录-->
        <!--    </el-button>-->
        <!--  </template>-->
        <!--</el-table-column>-->
      </el-table>
    </div>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script>
import { toLine } from '@/utils'
import { userStatus, appGroupWithAll } from '@/map/common'
import { userInfoMap } from '@/map/fishing'
import MapSelect from '@/components/map-select'
import config from './config'

export default {
  components: { MapSelect },
  data() {
    return {
      userInfoMap,
      withdrawDetailDialogVisible: true,
      dataForm: {
        key: '',
        // appCode: '',
        userId: '',
        openId: '',
        status: '',
        beginTime: '',
        endTime: '',
        sort: '',
        groupId: this.$store.state.user.groupIdList[0],
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      appList: [],
      userStatus,
      rangeTime: '',
      appGroupWithAll,
    }
  },
  watch: {
    rangeTime(t) {
      if (t) {
        this.dataForm.beginTime = t[0]
        this.dataForm.endTime = t[1]
      } else {
        this.dataForm.beginTime = ''
        this.dataForm.endTime = ''
      }
    },
  },
  activated() {
    if (this.$route.query.user_id) {
      this.dataForm.userId = this.$route.query.user_id
    }
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/fishing3/fishinguser/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: config.appCode,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
          this.dataListLoading = false
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/fishing3/fishinguser/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getAppList() {
      const params = {
        page: 1,
        limit: 100,
      }

      this.$store
        .dispatch('api/app/getAppListWithRole', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.apps
          }
        })
    },
    // 封禁解除
    banOrDismiss(row) {
      const data = {
        userId: row.id,
        remark: '',
        // 封禁类型 ：1:封禁 2: 解禁
        type: row.status === 4 ? 2 : 1,
      }
      // 解除
      if (row.status === 4) {
        this.$confirm(
          '确定要解除封禁吗?解除后此用户将可观看广告与提现！',
          '解禁',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(() => {
          this.handleBanOrDismiss(data)
        })
      } else {
        this.$prompt(
          '确定要封禁吗?封禁后此用户将无法观看广告与提现！',
          '封禁',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputErrorMessage: '必填',
            inputPlaceholder: '请简单描述封禁理由',
            inputValidator: v => !!v,
          }
        ).then(({ value }) => {
          data.remark = value
          this.handleBanOrDismiss(data)
        })
      }
    },
    handleBanOrDismiss(data) {
      this.$http({
        url: this.$http.adornUrl('/fishing3/fishinguser/ban'),
        method: 'post',
        data: this.$http.adornData(data),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.getDataList()
          } else {
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 提现明细
    withdrawDetail(row) {
      this.$router.push('/videoqa-guessuserwithdrawalrecord?user_id=' + row.id)
    },
    sortChange({ prop, order }) {
      let sort = prop
      if (prop === 'coinBalance' || prop === 'createdAt') {
        sort = toLine(prop)
      }
      this.dataForm.sort = order === 'descending' ? sort : ''
      this.getDataList()
    },
  },
}
</script>
