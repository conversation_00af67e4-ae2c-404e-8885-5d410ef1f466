<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
      'select',
    ]"
    operate-width="330"
  >
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>

    <template #model>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实验名称" prop="abName">
            <el-input
              v-model="modelConfig.formData.abName"
              placeholder="实验名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实验标识" prop="abCode">
            <el-input
              v-model="modelConfig.formData.abCode"
              placeholder="实验标识"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="实验描述" prop="abDesc">
            <el-input
              type="textarea"
              v-model="modelConfig.formData.abDesc"
              placeholder="实验描述"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="日期选择" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col
          :span="item.groupType === 'a' ? 24 : 12"
          v-for="(item, index) in modelConfig.formData.groupConfig"
          :key="index"
          style="margin-bottom: 20px;"
        >
          <el-card shadow="never" border>
            <div slot="header">
              <b>{{ item.groupName }}</b>
            </div>
            <el-form-item
              label="流量"
              :prop="`groupConfig[${index}].groupFlow`"
            >
              <el-input
                type="number"
                :min="0"
                :max="100"
                v-model.number="item.groupFlow"
                placeholder="流量"
              />
            </el-form-item>
            <el-form-item label="描述" prop="endDate">
              <el-input
                type="textarea"
                v-model="item.groupDesc"
                placeholder="描述"
              />
            </el-form-item>
            <el-form-item v-if="item.groupType !== 'a'">
              <config-json-editor v-model="item.groupConfig" />
            </el-form-item>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-form-item label="实验生效的最低版本号" prop="minAppVersionCode">
            <el-input
              type="number"
              v-model.number="modelConfig.formData.minAppVersionCode"
              placeholder="实验生效的最低版本号（包含）"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <radio-status v-model="modelConfig.formData.status" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
  </page-table>
</template>

<script>
import { fishingAbTestConfig as request } from '@/api/fishing3'
import { baseConfigMap, drawConfigMap } from '@/map/fishing'
import dayjs from '@/dayjs'
import ConfigJsonEditor from '@/components/config-json-editor'

export default {
  components: { ConfigJsonEditor },
  data() {
    const groupFlowValidator = (rule, value, callback) => {
      const sum = this.modelConfig.formData.groupConfig.reduce(
        (total, currentValue) => total + currentValue.groupFlow,
        0
      )

      const max = 100
      if (sum > max) {
        return callback(new Error('对照组和实验组的流量总和不得超过' + max))
      }
      callback()
    }

    const groupFlowRule = [
      { required: true, message: '不能为空', trigger: 'blur' },
      { validator: groupFlowValidator },
    ]

    return {
      fishList: [],
      drawConfigMap,
      baseConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', minWidth: 60 },
          // { field: 'appCode', title: 'appCode' },

          { field: 'abCode', title: '实验标识', width: 150 },
          { field: 'abName', title: '实验名称', width: 120 },
          { field: 'abDesc', title: '实验描述', width: 120 },
          {
            field: 'minAppVersionCode',
            title: '实验生效的最低版本号',
            width: 200,
          },
          { field: 'startDate', title: '开始时间', minWidth: 160 },
          { field: 'endDate', title: '结束时间', minWidth: 160 },
          // { field: 'groupConfig', title: '分组配置', width: 120 },
          {
            field: 'status',
            title: '状态',
            width: 120,
            slots: { default: 'table_item_status' },
          },
          { field: 'createTime', title: '创建时间', minWidth: 160 },
          { field: 'updateTime', title: '更新时间', minWidth: 160 },
        ],
        formConfig: {
          // items: [
          //   {
          //     field: 'userId',
          //     title: '用户ID',
          //     itemRender: {
          //       name: '$input',
          //       props: { placeholder: '请选择', clearable: true },
          //       defaultValue: '',
          //     },
          //   },
          // ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '900px',
        },
        formConfig: {
          labelWidth: '140px',
          labelPosition: 'top',
        },
        formData: {
          id: null,
          abCode: null,
          abName: null,
          abDesc: null,
          minAppVersionCode: null,
          startDate: null,
          endDate: null,
          groupConfig: [
            {
              groupType: 'a',
              groupName: '对照组',
              groupDesc: '对照组', //可修改
              groupFlow: 0, //可修改
              groupConfig: '',
            },
            {
              groupType: 'b',
              groupName: '实验组',
              groupDesc: '实验组', //可修改
              groupFlow: 0, //可修改
              groupConfig: '', //可修改
            },
            {
              groupType: 'c',
              groupName: '实验组',
              groupDesc: '实验组', //可修改
              groupFlow: 0, //可修改
              groupConfig: '', //可修改
            },
            {
              groupType: 'd',
              groupName: '实验组',
              groupDesc: '实验组', //可修改
              groupFlow: 0, //可修改
              groupConfig: '', //可修改
            },
          ],
          status: null,
          createTime: null,
          updateTime: null,
        },
        formRule: {
          'groupConfig[0].groupFlow': groupFlowRule,
          'groupConfig[1].groupFlow': groupFlowRule,
          'groupConfig[2].groupFlow': groupFlowRule,
          'groupConfig[3].groupFlow': groupFlowRule,
          abCode: [{ required: true, message: '不能为空', trigger: 'blur' }],
          abName: [{ required: true, message: '不能为空', trigger: 'blur' }],
          minAppVersionCode: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  computed: {
    dateRange: {
      set(dateRange) {
        if (dateRange) {
          const formatTmp = 'YYYY-MM-DD HH:mm:ss'
          const startDate = dayjs(dateRange[0]).format(formatTmp)
          const endDate = dayjs(dateRange[1]).format(formatTmp)
          this.modelConfig.formData.startDate = startDate
          this.modelConfig.formData.endDate = endDate
        }
      },
      get() {
        if (this.modelConfig && this.modelConfig.formData) {
          return [
            this.modelConfig.formData.startDate,
            this.modelConfig.formData.endDate,
          ]
        }
        return []
      },
    },
    groupConfig: {
      set(config) {
        this.modelConfig.formData.groupConfig[1].groupConfig = JSON.stringify(
          config
        )
      },
      get() {
        const config = this.modelConfig.formData.groupConfig[1].groupConfig
        if (config) {
          try {
            return JSON.parse(config)
          } catch (e) {
            return {}
          }
        }
        return {}
      },
    },
  },
}
</script>
