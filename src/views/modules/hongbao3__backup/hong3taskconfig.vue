<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item label="任务标题">
        <el-input
          v-model="dataForm.taskTitle"
          placeholder="任务标题"
          clearable
        />
      </el-form-item>
      <el-form-item label="入口类型">
        <el-select
          v-model="dataForm.entranceType"
          placeholder="入口类型"
          clearable
        >
          <el-option
            v-for="[key, label] in entranceTypeList"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="活动类型">
        <el-select v-model="dataForm.taskType" placeholder="活动类型" clearable>
          <el-option
            v-for="[key, label] in taskTypeList"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          v-if="isAuth('packet:taskconfig:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('packet:taskconfig:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      />
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          {{ getAppName(row.appId) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="entranceType"
        header-align="center"
        align="center"
        label="入口类型"
      >
        <template slot-scope="{ row }">
          <span>{{ entranceTypeList.get(row.entranceType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="taskType"
        header-align="center"
        align="center"
        label="任务类型"
      >
        <template slot-scope="{ row }">
          <span>{{ taskTypeList.get(row.taskType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="taskTitle"
        header-align="center"
        align="center"
        label="任务标题"
      />
      <el-table-column
        prop="askTime"
        header-align="center"
        align="center"
        label="任务要求次数"
      />
      <el-table-column
        prop="rewardCoin"
        header-align="center"
        align="center"
        label="奖励金币"
      />
      <el-table-column
        prop="sortValue"
        header-align="center"
        align="center"
        label="排序"
      />
      <el-table-column
        prop="enabled"
        header-align="center"
        align="center"
        label="是否启用"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.enabled === 1 ? 'success' : ''">
            {{ row.enabled === 1 ? '启用' : '未启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="160"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
        width="160"
      />
      <el-table-column
        prop="createdBy"
        header-align="center"
        align="center"
        label="创建者"
      />
      <el-table-column
        prop="updatedBy"
        header-align="center"
        align="center"
        label="更新者"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @closed="addOrUpdateVisible = false"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AddOrUpdate from './hong3taskconfig-add-or-update'
import { entranceTypeList, taskTypeList } from '@/map/common'
import AppSelect from '@/components/app-select'
export default {
  data() {
    return {
      dataForm: {
        key: '',
        entranceType: '',
        taskType: '',
        taskTitle: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      entranceTypeList,
      taskTypeList,
      appList: [],
    }
  },
  components: {
    AppSelect,
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
    this.getAppList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hong3/taskconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          appCode: this.getAppCodeInfo().code,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/hong3/taskconfig/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getAppList() {
      this.$store
        .dispatch('api/app/getAppListWithRole', {})
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.apps
          }
        })
    },
    getAppName(code) {
      if (this.appList && this.appList.length) {
        const res = this.appList.find(it => it.code === code)
        return res ? res.name : ''
      }
    },
    getAppCodeInfo() {
      const res = this.$store.state.ad.appList.find(
        it => it.id === this.$store.state.ad.appId
      )

      return {
        code: res && res.code ? res.code : 0,
        appName: res && res.name ? res.name : '',
      }
    },
  },
}
</script>
