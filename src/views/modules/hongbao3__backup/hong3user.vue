<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="currentChangeHandle(1)"
    >
      <el-form-item prop="userId" label="用户ID">
        <el-input v-model="dataForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item prop="openId" label="微信ID">
        <el-input v-model="dataForm.openId" placeholder="微信ID" clearable />
      </el-form-item>
      <el-form-item label="注册时间">
        <el-date-picker
          clearable
          v-model="rangeTime"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:00']"
        />
      </el-form-item>
      <el-form-item prop="status" label="账户状态">
        <el-select v-model="dataForm.status" clearable>
          <el-option
            v-for="[key, label] in userStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button @click="currentChangeHandle(1)" type="primary">
          查询
        </el-button>
        <!--<el-button v-if="isAuth('packet:appuser:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <!--<el-button v-if="isAuth('packet:appuser:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 20px;">
      <el-button
        type="primary"
        icon="el-icon-download"
        @click="$downloadTableToExcel()"
      >
        下载Excel
      </el-button>
    </div>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      @sort-change="sortChange"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        type="index"
        header-align="center"
        align="center"
        width="50"
        label="序号"
      />
      <el-table-column
        prop="appCode"
        header-align="center"
        align="center"
        label="应用名称"
        width="120px"
      >
        <template>
          <el-tag>每日赚红包</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="用户ID"
        width="60"
      />
      <el-table-column
        prop="openId"
        header-align="center"
        align="center"
        label="微信openid/unionid"
        width="140px"
      />
      <el-table-column
        prop="wechatAvatar"
        header-align="center"
        align="center"
        label="微信头像"
        width="180"
      >
        <template slot-scope="{ row }">
          <img :src="row.wechatAvatar" alt="" />
        </template>
      </el-table-column>
      <el-table-column
        prop="nickName"
        header-align="center"
        align="center"
        label="微信昵称"
      />
      <el-table-column
        prop="coinBalance"
        header-align="center"
        align="center"
        label="金币余额"
        width="120px"
        sortable="custom"
      />
      <el-table-column
        prop="grandTotalCoins"
        header-align="center"
        align="center"
        label="累计金币收益"
        width="160px"
        sortable="custom"
      />
      <el-table-column
        prop="grandTotalAmount"
        header-align="center"
        align="center"
        sortable="custom"
        width="150px"
        label="累计提现金额(元)"
      />
      <el-table-column
        prop="withdrawalTimes"
        header-align="center"
        align="center"
        width="150px"
        label="提现次数"
      />

      <el-table-column
        header-align="center"
        align="center"
        width="100px"
        label="开屏"
      >
        <template slot-scope="{ row }">
          <span v-if="row.splashEcpm || row.splashExposure">
            {{ row.splashEcpm || '-' }}/{{ row.splashExposure || '-' }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        header-align="center"
        align="center"
        width="100px"
        label="插屏"
      >
        <template slot-scope="{ row }">
          <span v-if="row.interactionEcpm || row.interactionExposure">
            {{ row.interactionEcpm || '-' }}/{{
              row.interactionExposure || '-'
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        header-align="center"
        align="center"
        width="100px"
        label="激励视频"
      >
        <template slot-scope="{ row }">
          <span v-if="row.rewardEcpm || row.rewardExposure">
            {{ row.rewardEcpm || '-' }}/{{ row.rewardExposure || '-' }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        header-align="center"
        align="center"
        width="100px"
        label="信息流"
      >
        <template slot-scope="{ row }">
          <span v-if="row.nativeEcpm || row.nativeExposure">
            {{ row.nativeEcpm || '-' }}/{{ row.nativeExposure || '-' }}
          </span>
        </template>
      </el-table-column>

      <!--<el-table-column-->
      <!--  prop="watchTimes"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  sortable="custom"-->
      <!--  width="120px"-->
      <!--  label="观看视频数"-->
      <!--/>-->
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="注册时间"
        width="140px"
        sortable="custom"
      />
      <el-table-column
        prop="loginTime"
        header-align="center"
        align="center"
        width="140px"
        label="最近登录时间"
      />
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="账户状态"
        width="100px"
      >
        <template slot-scope="{ row }">
          <el-tag>
            {{ userStatus.get(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        header-align="center"
        align="center"
        label="备注"
      />
      <el-table-column
        prop="riskLevel"
        header-align="center"
        align="center"
        label="风险建议"
      />
      <el-table-column
        prop="updatedBy"
        header-align="center"
        align="center"
        label="操作人"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="220"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="banOrDismiss(scope.row)">
            {{ scope.row.status === 4 ? '解除' : '封禁' }}
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="withdrawDetail(scope.row)"
          >
            提现记录
          </el-button>
          <el-button type="text" size="small" @click="globDetail(scope.row)">
            金币明细
          </el-button>
          <!--<el-button-->
          <!--  type="text"-->
          <!--  size="small"-->
          <!--  @click="addOrUpdateHandle(scope.row.id)"-->
          <!--&gt;-->
          <!--  修改-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  type="text"-->
          <!--  size="small"-->
          <!--  @click="deleteHandle(scope.row.id)"-->
          <!--&gt;-->
          <!--  删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import { toLine } from '@/utils'
import { userStatus, appGroupWithAll } from '@/map/common'
import AddOrUpdate from './hong3user-add-or-update'

export default {
  data() {
    return {
      withdrawDetailDialogVisible: true,
      dataForm: {
        key: '',
        // appCode: '',
        userId: '',
        openId: '',
        status: '',
        beginTime: '',
        endTime: '',
        sort: '',
        groupId: this.$store.state.user.groupIdList[0],
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      appList: [],
      userStatus,
      rangeTime: '',
      appGroupWithAll,
    }
  },
  watch: {
    rangeTime(t) {
      if (t) {
        this.dataForm.beginTime = t[0]
        this.dataForm.endTime = t[1]
      } else {
        this.dataForm.beginTime = ''
        this.dataForm.endTime = ''
      }
    },
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    if (this.$route.query.user_id) {
      this.dataForm.userId = this.$route.query.user_id
    }
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hong3/appuser/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: '10053',
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
          this.dataListLoading = false
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/hong3/appuser/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getAppList() {
      const params = {
        page: 1,
        limit: 100,
      }

      this.$store
        .dispatch('api/app/getAppListWithRole', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.apps
          }
        })
    },
    // 封禁解除
    banOrDismiss(row) {
      const data = {
        userId: row.id,
        remark: '',
        // 封禁类型 ：1:封禁 2: 解禁
        type: row.status === 4 ? 2 : 1,
      }
      // 解除
      if (row.status === 4) {
        this.$confirm(
          '确定要解除封禁吗?解除后此用户将可观看广告与提现！',
          '解禁',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(() => {
          this.handleBanOrDismiss(data)
        })
      } else {
        this.$prompt(
          '确定要封禁吗?封禁后此用户将无法观看广告与提现！',
          '封禁',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputErrorMessage: '必填',
            inputPlaceholder: '请简单描述封禁理由',
            inputValidator: v => !!v,
          }
        ).then(({ value }) => {
          data.remark = value
          this.handleBanOrDismiss(data)
        })
      }
    },
    handleBanOrDismiss(data) {
      this.$http({
        url: this.$http.adornUrl('/hong3/appuser/ban'),
        method: 'post',
        data: this.$http.adornData(data),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.getDataList()
          } else {
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 提现明细
    withdrawDetail(row) {
      this.$router.push('/hongbao3-honguserwithdrawalrecord?user_id=' + row.id)
    },
    // 金币明细
    globDetail(row) {
      this.$router.push('/hongbao3-hongusercoinrecord?user_id=' + row.id)
    },
    sortChange({ prop, order }) {
      let sort = prop
      if (prop === 'coinBalance' || prop === 'createdAt') {
        sort = toLine(prop)
      }
      this.dataForm.sort = order === 'descending' ? sort : ''
      this.getDataList()
    },
  },
}
</script>
