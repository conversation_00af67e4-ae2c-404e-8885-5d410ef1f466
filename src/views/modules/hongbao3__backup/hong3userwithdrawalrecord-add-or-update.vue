<template>
  <el-dialog
    :title="!dataForm.withdrawalSerialNo ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
      </el-form-item>
      <el-form-item label="应用id" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
      </el-form-item>
      <el-form-item label="提现金额" prop="withdrawalAmount">
        <el-input
          v-model="dataForm.withdrawalAmount"
          placeholder="提现金额"
        ></el-input>
      </el-form-item>
      <el-form-item label="提现设置表id" prop="withdrawalConfigId">
        <el-input
          v-model="dataForm.withdrawalConfigId"
          placeholder="提现设置表id"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="状态：0：待打款，1：打款成功，2：打款失败，3：不予打款，4：注销中，5：注销"
        prop="status"
      >
        <el-input
          v-model="dataForm.status"
          placeholder="状态：0：待打款，1：打款成功，2：打款失败，3：不予打款，4：注销中，5：注销"
        ></el-input>
      </el-form-item>
      <el-form-item label="风险建议" prop="riskSuggest">
        <el-input
          v-model="dataForm.riskSuggest"
          placeholder="风险建议"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        withdrawalSerialNo: 0,
        userId: '',
        appId: '',
        withdrawalAmount: '',
        withdrawalConfigId: '',
        createdAt: '',
        status: '',
        riskSuggest: '',
      },
      dataRule: {
        userId: [
          { required: true, message: '用户id不能为空', trigger: 'blur' },
        ],
        appId: [{ required: true, message: '应用id不能为空', trigger: 'blur' }],
        withdrawalAmount: [
          { required: true, message: '提现金额不能为空', trigger: 'blur' },
        ],
        withdrawalConfigId: [
          { required: true, message: '提现设置表id不能为空', trigger: 'blur' },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        status: [
          {
            required: true,
            message:
              '状态：0：待打款，1：打款成功，2：打款失败，3：不予打款，4：注销中，5：注销不能为空',
            trigger: 'blur',
          },
        ],
        riskSuggest: [
          { required: true, message: '风险建议不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.withdrawalSerialNo = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.withdrawalSerialNo) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/appuserwithdrawalrecord/info/${this.dataForm.withdrawalSerialNo}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.userId = data.appUserWithdrawalRecord.userId
              this.dataForm.appId = data.appUserWithdrawalRecord.appId
              this.dataForm.withdrawalAmount =
                data.appUserWithdrawalRecord.withdrawalAmount
              this.dataForm.withdrawalConfigId =
                data.appUserWithdrawalRecord.withdrawalConfigId
              this.dataForm.createdAt = data.appUserWithdrawalRecord.createdAt
              this.dataForm.status = data.appUserWithdrawalRecord.status
              this.dataForm.riskSuggest =
                data.appUserWithdrawalRecord.riskSuggest
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/appuserwithdrawalrecord/${
                !this.dataForm.withdrawalSerialNo ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              withdrawalSerialNo: this.dataForm.withdrawalSerialNo || undefined,
              userId: this.dataForm.userId,
              appId: this.dataForm.appId,
              withdrawalAmount: this.dataForm.withdrawalAmount,
              withdrawalConfigId: this.dataForm.withdrawalConfigId,
              createdAt: this.dataForm.createdAt,
              status: this.dataForm.status,
              riskSuggest: this.dataForm.riskSuggest,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
