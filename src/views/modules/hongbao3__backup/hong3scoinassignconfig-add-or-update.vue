<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item label="判断条件" prop="judgeCondition">
        <el-input
          v-model="dataForm.judgeCondition"
          :disabled="!!dataForm.id"
          placeholder="判断条件"
        />
      </el-form-item>
      <el-form-item label="权重" prop="weights">
        <el-input
          v-model="dataForm.weights"
          placeholder="权重，此表所有数据权重相加应为1"
        />
        <p>此表所有数据权重相加应为1</p>
      </el-form-item>

      <el-form-item label="提现配置类型" prop="type">
        <el-select v-model="dataForm.type">
          <el-option
            v-for="[key, value] in packetCoinAssignConfigType"
            :key="key"
            :label="value"
            :value="key"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { packetCoinAssignConfigType } from '@/map/common'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appId: '',
        judgeCondition: '',
        weights: '',
        type: '',
      },
      dataRule: {
        appId: [
          {
            required: true,
            message: '应用id不能为空',
            trigger: 'blur',
          },
        ],
        judgeCondition: [
          {
            required: true,
            message: '判断条件不能为空',
            trigger: 'blur',
          },
        ],
        weights: [
          {
            required: true,
            message: '权重，此表所有数据权重相加应为1不能为空',
            trigger: 'blur',
          },
        ],
        type: [
          {
            required: true,
            message:
              '提现配置类型：1：注册时间，2：累计提现情况，3：现有金币数，4：人气值，5：当前观看视频数不能为空',
            trigger: 'blur',
          },
        ],
      },
      packetCoinAssignConfigType,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/appcoinassignconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.appCoinAssignConfig.appCode
              this.dataForm.judgeCondition =
                data.appCoinAssignConfig.judgeCondition
              this.dataForm.weights = data.appCoinAssignConfig.weights
              this.dataForm.type = data.appCoinAssignConfig.type
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/appcoinassignconfig/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              judgeCondition: this.dataForm.judgeCondition,
              weights: this.dataForm.weights,
              type: this.dataForm.type,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
