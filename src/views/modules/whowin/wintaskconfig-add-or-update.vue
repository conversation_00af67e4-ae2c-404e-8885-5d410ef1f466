<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="500px"
    @closed="$emit('closed')"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="110px"
    >
      <el-form-item>
        <h2 slot="label">基本信息</h2>
      </el-form-item>
      <el-form-item v-if="dataForm.id" label="任务ID" prop="appId">
        <el-input v-model="dataForm.id" disabled />
      </el-form-item>
      <el-form-item label="应用id" prop="appId">
        <el-select
          v-model="dataForm.appId"
          placeholder="应用"
          filterable
          @change="changeApp"
        >
          <el-option
            v-for="item in appList"
            :key="item.code"
            :value="item.code"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入口类型" prop="entranceType">
        <el-select
          v-model="dataForm.entranceType"
          placeholder="入口类型"
          @change="changeEntranceType"
        >
          <el-option
            v-for="[key, label] in entranceTypeList"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <h2 slot="label">基本配置</h2>
      </el-form-item>
      <el-form-item label="活动类型" prop="taskType">
        <el-select
          v-model="dataForm.taskType"
          placeholder="活动类型"
          @change="dataForm.rewardType = ''"
        >
          <!--
            移除限制
            :disabled="selectedTaskType.includes(key)"
          -->
          <el-option
            v-for="[key, label] in taskTypeList"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发放货币类型" prop="rewardType">
        <el-select v-model="dataForm.rewardType" placeholder="发放货币类型">
          <el-option
            v-for="[key, label] in comRewardTypeList"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务标题" prop="taskTitle">
        <el-input v-model="dataForm.taskTitle" placeholder="任务标题" />
      </el-form-item>
      <el-form-item label="任务完成要求" prop="askTime">
        <el-input-number
          v-model="dataForm.askTime"
          :min="0"
          placeholder="任务完成要求"
        />
        个
      </el-form-item>
      <el-form-item label="奖励金币/元宝" prop="rewardCoin">
        <el-input-number
          v-model="dataForm.rewardCoin"
          :min="0"
          placeholder="奖励金币/元宝"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sortValue">
        <el-input-number
          v-model="dataForm.sortValue"
          :min="0"
          placeholder="排序"
        />
      </el-form-item>
      <el-form-item label="启用" prop="enabled">
        <el-select v-model="dataForm.enabled" placeholder="启用">
          <el-option :value="0" label="未启用" />
          <el-option :value="1" label="启用" />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { entranceTypeList } from '@/map/common'
import { rewardTypeList, taskTypeList } from '@/map/win'

export default {
  data() {
    const isIntegerValidator = (_, value, callback) => {
      if (Number.isInteger(value)) {
        callback()
      } else {
        callback(new Error('必须为整数'))
      }
    }

    return {
      entranceTypeList,
      taskTypeList,
      visible: false,
      dataForm: {
        id: 0,
        appId: '',
        entranceType: '',
        taskType: '',
        taskTitle: '',
        askTime: '',
        rewardCoin: '',
        enabled: '',
        sortValue: '',
        rewardType: '',
      },
      dataRule: {
        appId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        rewardType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        entranceType: [
          { required: true, message: '不能为空', trigger: 'blur' },
        ],
        taskType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        taskTitle: [{ required: true, message: '不能为空', trigger: 'blur' }],
        askTime: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: isIntegerValidator,
          },
        ],
        rewardCoin: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: isIntegerValidator,
          },
        ],
        enabled: [{ required: true, message: '不能为空', trigger: 'blur' }],
        sortValue: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (this.existSorts.includes(value)) {
                callback(new Error('不能重复'))
              } else {
                callback()
              }
            },
          },
        ],
      },
      appList: [],
      dataList: [],
      selectedTaskType: [],
      existSorts: [],
      rewardTypeList,
    }
  },
  computed: {
    comRewardTypeList() {
      // 移除货币限制
      // if (this.dataForm.taskType === 5 || this.dataForm.taskType === 9) {
      //   return new Map([[2, '元宝']])
      // }
      //
      // return new Map([[1, '金币']])
      return new Map([
        [1, '金币'],
        [2, '元宝'],
      ])
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true

      this.getAppList()

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/taskconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.taskConfig.appId
              this.dataForm.entranceType = data.taskConfig.entranceType
              this.dataForm.taskType = data.taskConfig.taskType
              this.dataForm.taskTitle = data.taskConfig.taskTitle
              this.dataForm.askTime = data.taskConfig.askTime
              this.dataForm.rewardCoin = data.taskConfig.rewardCoin
              this.dataForm.enabled = data.taskConfig.enabled
              this.dataForm.sortValue = data.taskConfig.sortValue
              this.dataForm.rewardType = data.taskConfig.rewardType

              this.getDataList()
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/taskconfig/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              entranceType: this.dataForm.entranceType,
              taskType: this.dataForm.taskType,
              taskTitle: this.dataForm.taskTitle,
              askTime: this.dataForm.askTime,
              rewardCoin: this.dataForm.rewardCoin,
              enabled: this.dataForm.enabled,
              sortValue: this.dataForm.sortValue,
              rewardType: this.dataForm.rewardType,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getAppList() {
      this.$store
        .dispatch('api/app/getAppListWithRole', {})
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.apps
          }
        })
    },
    // 获取对应app的数据
    getDataList() {
      this.$http({
        url: this.$http.adornUrl('/whowin/taskconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 100,
          appCode: this.dataForm.appId,
          entranceType: '',
          taskType: '',
          taskTitle: '',
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.filterActivityType()
        } else {
          this.dataList = []
        }
      })
    },
    changeApp() {
      this.dataForm.taskType = ''
      this.getDataList()
    },
    changeEntranceType() {
      this.filterActivityType()
      this.dataForm.taskType = ''
      this.dataForm.sortValue = ''
    },
    filterActivityType() {
      this.selectedTaskType = []
      this.existSorts = []
      if (this.dataList && this.dataList.length && this.dataForm.entranceType) {
        const res = this.dataList.filter(
          it => it.entranceType === this.dataForm.entranceType
        )
        this.selectedTaskType = res
          .map(it => it.taskType)
          .filter(it => it !== this.dataForm.taskType)
        this.existSorts = res
          .map(it => it.sortValue)
          .filter(it => it !== this.dataForm.sortValue)
      }
    },
  },
}
</script>
