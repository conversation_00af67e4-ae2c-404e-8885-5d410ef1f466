<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="600px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model="dataForm.appId"
          :is-show-all="false"
          :disabled="!!dataForm.id"
        />
      </el-form-item>
      <el-form-item label="打卡形式" prop="clockType">
        <map-select
          v-model="dataForm.clockType"
          :list="clockTypeList"
          :disabled="!!dataForm.id"
        />
      </el-form-item>
      <div style="display: flex;">
        <el-form-item label="打卡条件" prop="clockCondition">
          <map-select
            v-model="dataForm.clockCondition"
            :list="clockConditionList"
          />
        </el-form-item>
        <el-form-item
          label-width="0"
          prop="clockTime"
          style="margin-left: 10px"
        >
          <el-input
            type="number"
            v-model.number="dataForm.clockTime"
            :min="0"
            style="width: 145px;"
            placeholder="请输入"
          >
            <span slot="append">次</span>
          </el-input>
        </el-form-item>
      </div>
      <el-form-item label="打卡任务" prop="clockTaskList">
        <div
          v-for="(item, index) in dataForm.clockTaskList"
          :key="index"
          style="display: flex; align-items: center; margin-bottom: 5px"
        >
          <div>
            <el-input
              type="number"
              v-model.number="item.dayTime"
              :min="0"
              style="width: 100px; margin-right:10px"
            />
            <span>日打卡</span>
          </div>
          <div style="margin: 0 20px">
            <span>奖励金额</span>
            <!--:min="0"-->
            <el-input
              type="number"
              v-model="item.rewardMoney"
              style="width: 100px; margin: 0 10px;"
              @input="handleChangeRewardMoney(item.rewardMoney, index)"
              @blur="handleBlurRewardMoney(item.rewardMoney, index)"
            />
            <span>元</span>
          </div>
          <el-button
            @click="removeClockTaskList(index)"
            type="text"
            icon="el-icon-remove-outline"
            style="color: #F56C6C"
            :disabled="index === 0"
          />
          <el-button
            @click="addClockTaskList(index)"
            type="text"
            icon="el-icon-circle-plus-outline"
          />
        </div>
      </el-form-item>
      <el-form-item label="是否开启" prop="enabled">
        <map-select v-model="dataForm.enabled" :list="taskStatusList" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { clockTypeList, clockConditionList, taskStatusList } from '@/map/win'
import MapSelect from '@/components/map-select'
import AppSelectComponent from '@/components/app-select-component'

export default {
  components: {
    AppSelectComponent,
    MapSelect,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: null,
        appId: null,
        clockType: null,
        clockCondition: null,
        clockTime: null,
        clockTaskList: [{ dayTime: 0, rewardMoney: 0 }],
        enabled: null,
      },
      dataRule: {
        appId: [{ required: true, message: '应用不能为空', trigger: 'blur' }],
        enabled: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
        clockType: [
          { required: true, message: '打卡形式不能为空', trigger: 'blur' },
        ],
        clockCondition: [
          { required: true, message: '打卡条件不能为空', trigger: 'blur' },
        ],
        clockTime: [
          { required: true, message: '次数不能为空', trigger: 'blur' },
        ],
        clockTaskList: [
          {
            validator: (_, value, callback) => {
              if (!value.length) {
                return callback(new Error('不能为空'))
              }
              callback()
            },
          },
        ],
      },
      clockTypeList,
      clockConditionList,
      taskStatusList,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/winpunchtaskconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.winPunchTaskConfig.appId
              this.dataForm.clockType = data.winPunchTaskConfig.clockType
              this.dataForm.clockCondition =
                data.winPunchTaskConfig.clockCondition
              this.dataForm.clockTime = data.winPunchTaskConfig.clockTime
              this.dataForm.clockTaskList =
                data.winPunchTaskConfig.clockTaskList
              this.dataForm.enabled = data.winPunchTaskConfig.enabled
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = { ...this.dataForm }
          if (!data.id) {
            delete data.id
          }
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/winpunchtaskconfig/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData(data),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    addClockTaskList(index) {
      this.dataForm.clockTaskList.splice(index + 1, 0, {
        dayTime: 0,
        rewardMoney: 0,
      })
    },
    removeClockTaskList(index) {
      this.dataForm.clockTaskList.splice(index, 1)
    },
    handleChangeRewardMoney(v, index) {
      const arr = v.split('.')
      if (arr.length > 1) {
        arr[1] = arr[1].substr(0, 2)
      }
      this.dataForm.clockTaskList[index].rewardMoney = arr.join('.')
    },
    handleBlurRewardMoney(v, index) {
      this.dataForm.clockTaskList[index].rewardMoney = Math.max(0, Number(v))
    },
  },
}
</script>
