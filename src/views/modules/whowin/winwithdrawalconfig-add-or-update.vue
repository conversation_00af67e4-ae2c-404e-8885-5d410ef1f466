<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="880px"
    @closed="$emit('closed')"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="应用" prop="appCode">
            <el-select
              v-model="dataForm.appCode"
              :disabled="!!dataForm.id"
              style="width: 100%"
              filterable
            >
              <template v-for="item in $store.state.ad.appList">
                <el-option
                  v-if="item.id"
                  :key="item.code"
                  :value="item.code"
                  :label="item.name"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-select
              v-model="versionMultiple"
              multiple
              collapse-tags
              style="width: 100%"
            >
              <el-option
                v-for="item in versionList"
                :key="item.versionCode"
                :value="item.versionName"
                :label="item.versionName"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号范围">
            <el-select
              v-model="versionRange[0]"
              style="width: 120px;"
              clearable
            >
              <el-option
                v-for="item in versionList"
                :key="item.versionCode"
                :value="item.versionName"
                :label="item.versionName"
              />
            </el-select>
            <span style="padding: 0 8px">至</span>
            <el-select
              v-model="versionRange[1]"
              style="width: 120px;"
              clearable
            >
              <el-option
                v-for="item in versionList"
                :key="item.versionCode"
                :value="item.versionName"
                :label="item.versionName"
              />
            </el-select>
            <div v-if="isShowRangeError" style="color: #F56C6C">
              前值不能大于后值
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="提现金币" prop="coin">
            <el-input
              type="number"
              v-model.trim="dataForm.coin"
              :min="0"
              placeholder="提现金币"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提现金额" prop="amount">
            <el-input
              type="number"
              v-model.trim="dataForm.amount"
              :min="0"
              placeholder="提现金额"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="提现次数" prop="times">
            <el-input
              type="number"
              v-model.trim="dataForm.times"
              :min="0"
              placeholder="提现次数"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重复提现间隔时间" prop="timeInterval">
            <el-input
              type="number"
              v-model.trim="dataForm.timeInterval"
              placeholder="重复提现间隔时间"
              :min="0"
            >
              <template slot="append">小时</template>
            </el-input>
          </el-form-item>
        </el-col>
        <!--<el-col :span="12">-->
        <!--  <el-form-item label="视频观看次数要求" prop="limitNum">-->
        <!--    <el-input-number-->
        <!--      v-model.trim="dataForm.limitNum"-->
        <!--      :min="0"-->
        <!--      placeholder="视频观看次数要求"-->
        <!--    />-->
        <!--  </el-form-item>-->
        <!--</el-col>-->
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序值" prop="sortValue">
            <el-input
              type="number"
              v-model="dataForm.sortValue"
              :min="0"
              placeholder="排序值"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提现类型" prop="type">
            <el-select v-model="dataForm.type" style="width: 100%;">
              <el-option
                v-for="[key, label] in withdrawConfigType"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="提现分类" prop="category">
            <el-select v-model="dataForm.category" style="width: 100%">
              <el-option
                v-for="[key, label] in withdrawTypeList"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否自动审核" prop="needAudit">
            <el-select v-model="dataForm.needAudit" style="width: 100%">
              <el-option
                v-for="[key, label] in isAutoAudit"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="两档提现时间间隔" prop="betweenTimeInterval">
            <el-input
              type="number"
              v-model.number="dataForm.betweenTimeInterval"
              :min="0"
              placeholder="两档提现时间间隔"
            >
              <template slot="append">小时</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="dataForm.status" style="width: 100%">
              <el-option
                v-for="[key, label] in withdrawConfigStatus"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider>提现限制必要条件</el-divider>
      <div style="margin-bottom: 20px">
        <el-card class="box-card" shadow="never">
          <!--提现限制条件-->
          <withdraw-limit
            ref="withdrawLimitComponent"
            @change="handleWLChange"
          />
          <el-form-item label="提现说明" prop="desc">
            <el-input
              v-model.trim="dataForm.description"
              type="textarea"
              placeholder="提现说明"
            />
          </el-form-item>
        </el-card>
      </div>
      <el-divider>附件条件</el-divider>
      <!--附件条件-->
      <div class="c-list">
        <div
          class="c-item"
          v-for="(item, index) in dataForm.extraStages"
          :key="index"
        >
          <el-card shadow="never">
            <div slot="header" class="clearfix">
              <span>附件条件{{ index + 1 }}</span>
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                @click="removeExtraStages(index)"
              >
                移除
              </el-button>
            </div>
            <el-form-item label="标题">
              <el-input v-model="item.title" />
            </el-form-item>
            <withdraw-limit
              :limit-data="item.conditions"
              @change="handleExtraStagesWLChange($event, index)"
            />
            <el-form-item label="提现说明" prop="desc">
              <el-input
                v-model.trim="item.desc"
                type="textarea"
                placeholder="提现说明"
              />
            </el-form-item>
          </el-card>
        </div>
        <div class="c-item">
          <el-card shadow="never">
            <div
              style="height: 405px; display: flex; justify-content: center; align-items: center"
            >
              <i
                @click="addExtraStage"
                class="el-icon-plus"
                style="font-size: 120px; color: #0BB2D4; cursor:pointer; opacity: 0.5;"
              />
            </div>
          </el-card>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="loading">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  withdrawConfigType,
  withdrawConfigStatus,
  isAutoAudit,
} from '@/map/common'
import { withdrawTypeList } from '@/map/win'
import WithdrawLimit from '@/components/withdraw-limit'

export default {
  components: { WithdrawLimit },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        coin: '',
        amount: '',
        times: '',
        type: '',
        timeInterval: '',
        sortValue: '',
        status: '',
        needAudit: 0,
        appCode: '',
        withdrawalLimit: null,
        limitNum: '',
        version: '',
        description: '',
        category: '',
        betweenTimeInterval: '',
        // 附加条件
        extraStages: [
          {
            title: '附加条件',
            desc: '完成XX提现任务xx次，视频播放xx次',
            conditions: [
              // { limitNum: null, limitType: 3 },
              // { limitNum: 1, limitType: 6, withdrawalId: 3 },
            ],
          },
        ],
      },
      dataRule: {
        coin: [
          {
            required: true,
            message: '提现金币不能为空',
            trigger: 'blur',
          },
        ],
        amount: [
          {
            required: true,
            message: '提现金额不能为空',
            trigger: 'blur',
          },
        ],
        times: [
          {
            required: true,
            message: '提现次数不能为空',
            trigger: 'blur',
          },
        ],
        type: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        timeInterval: [
          {
            required: true,
            message: '重复提现间隔时间，单位小时不能为空',
            trigger: 'blur',
          },
        ],
        sortValue: [
          {
            required: true,
            message: '排序值不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        needAudit: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        appCode: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],

        withdrawalLimit: [
          {
            required: true,
            message: '至少勾选一项',
            trigger: 'blur',
          },
        ],
        limitNum: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        category: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
      },
      withdrawConfigType,
      withdrawConfigStatus,
      isAutoAudit,
      versionList: [],
      versionRange: ['', ''],
      versionMultiple: [],
      isShowRangeError: false,
      withdrawTypeList,
      loading: false,
    }
  },
  watch: {
    'dataForm.appCode'() {
      this.versionRange = ['', '']
      this.versionMultiple = []
      if (this.dataForm.appCode) {
        setTimeout(() => {
          this.getVersionList()
        })
      }
    },
    versionRange(range) {
      this.isShowRangeError = range[0] > range[1]
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/appwithdrawalconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.coin = data.appWithdrawalConfig.coin
              this.dataForm.amount = data.appWithdrawalConfig.amount
              this.dataForm.times = data.appWithdrawalConfig.times
              this.dataForm.type = data.appWithdrawalConfig.type
              this.dataForm.timeInterval = data.appWithdrawalConfig.timeInterval
              this.dataForm.sortValue = data.appWithdrawalConfig.sortValue
              this.dataForm.status = data.appWithdrawalConfig.status
              this.dataForm.needAudit = data.appWithdrawalConfig.needAudit
              this.dataForm.appCode = data.appWithdrawalConfig.appCode
              this.dataForm.limitNum = data.appWithdrawalConfig.limitNum
              this.dataForm.withdrawalLimit =
                data.appWithdrawalConfig.withdrawalLimit
              this.dataForm.version = data.appWithdrawalConfig.version
              this.dataForm.description = data.appWithdrawalConfig.description
              this.dataForm.category = data.appWithdrawalConfig.category
              this.dataForm.betweenTimeInterval =
                data.appWithdrawalConfig.betweenTimeInterval
              try {
                this.dataForm.extraStages = JSON.parse(
                  data.appWithdrawalConfig.extraStages
                )
                console.log('解析后的数据', this.dataForm.extraStages)
              } catch (e) {
                console.error('extraStages 解析错误')
              }

              if (this.dataForm.withdrawalLimit) {
                this.$refs.withdrawLimitComponent.setLimitList(
                  this.dataForm.withdrawalLimit
                )
              }

              setTimeout(() => {
                try {
                  const versionJson = JSON.parse(data.appWithdrawalConfig.extra)
                  this.versionRange = [...versionJson.versionRange]
                  this.versionMultiple = [...versionJson.versionMultiple]
                } catch (e) {
                  console.log(e)
                }
              })
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      Promise.all([this.$refs.dataForm.validate()]).then(() => {
        let versionList = []
        if (this.versionRange[0] && this.versionRange[1]) {
          const startVersion = Number(this.versionRange[0].replaceAll('.', ''))
          const endVersion = Number(this.versionRange[1].replaceAll('.', ''))
          for (let i = startVersion; i <= endVersion; i++) {
            versionList.push(
              i
                .toString()
                .split('')
                .join('.')
            )
          }
        }
        versionList = versionList.concat(this.versionMultiple)
        this.dataForm.version = Array.from(new Set(versionList)).map(
          it =>
            this.versionList.find(item => item.versionName === it).versionCode
        )

        if (!this.dataForm.version.length) {
          this.dataForm.version = null
        }

        this.loading = true
        this.$http({
          url: this.$http.adornUrl(
            `/whowin/appwithdrawalconfig/${
              !this.dataForm.id ? 'save' : 'update'
            }`
          ),
          method: 'post',
          data: this.$http.adornData({
            ...this.dataForm,
            id: this.dataForm.id || undefined,
            extra: JSON.stringify({
              versionRange: this.versionRange,
              versionMultiple: this.versionMultiple,
            }),
            extraStages: JSON.stringify(this.dataForm.extraStages),
          }),
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
              })
              this.visible = false
              this.$emit('refreshDataList')
            } else {
              this.$message.error(data.msg || '服务器错误')
            }
          })
          .catch(() => this.$message.error('服务器错误'))
          .finally(() => {
            this.loading = false
          })
      })
    },
    getVersionList() {
      const res = this.$store.state.ad.appList.find(
        it => it.code === this.dataForm.appCode
      )
      const appId = res ? res.id : ''
      const params = {
        page: 1,
        limit: 100,
        key: '',
        app_id: appId,
      }

      this.$store
        .dispatch('api/app/getVersionList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.versionList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.versionList = []
            this.totalPage = 0
          }
        })
    },
    handleWLChange(data) {
      this.dataForm.withdrawalLimit = data
    },
    addExtraStage() {
      this.dataForm.extraStages.push({
        title: '附加条件',
        desc: '完成XX提现任务xx次，视频播放xx次',
        conditions: [
          // { limitNum: null, limitType: 2 },
          // { limitNum: 1, limitType: 3 },
          // { limitNum: 1, limitType: 5, withdrawalId: 3 },
        ],
      })
    },
    removeExtraStages(index) {
      this.dataForm.extraStages.splice(index, 1)
    },
    handleExtraStagesWLChange(data, index) {
      this.dataForm.extraStages[index].conditions = data
    },
  },
}
</script>

<style lang="scss" scoped>
.c-list {
  display: grid;
  overflow: hidden;
  grid-template-columns: repeat(2, 400px);
  /*  声明行间距和列间距  */
  grid-gap: 20px;
  box-sizing: border-box;

  .box-card {
  }
}
</style>
