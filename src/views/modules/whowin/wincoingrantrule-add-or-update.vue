<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item label="应用" prop="appId">
        <app-select-component
          v-model.number="dataForm.appId"
          :is-show-all="false"
        />
      </el-form-item>
      <el-form-item label="阶段类型" prop="levelType">
        <map-select
          v-model="dataForm.levelType"
          :list="coinGrantRuleLevelType"
        />
      </el-form-item>
      <el-form-item label="要求下限值" prop="askLowLimit">
        <el-input
          type="number"
          v-model.number="dataForm.askLowLimit"
          placeholder="要求下限值"
        />
      </el-form-item>
      <el-form-item label="要求上限值" prop="askHighLimit">
        <el-input
          type="number"
          v-model.number="dataForm.askHighLimit"
          placeholder="要求上限值"
        />
      </el-form-item>
      <el-form-item label="配置的下限值" prop="lowLimit">
        <el-input
          type="number"
          v-model.number="dataForm.lowLimit"
          placeholder="配置的下限值"
        />
      </el-form-item>
      <el-form-item label="配置的上限值" prop="highLimit">
        <el-input
          type="number"
          v-model.number="dataForm.highLimit"
          placeholder="配置的上限值"
        />
      </el-form-item>
      <el-form-item label="排序值" prop="sortValue">
        <el-input
          type="number"
          v-model.number="dataForm.sortValue"
          placeholder="排序值"
        />
      </el-form-item>
      <el-form-item label="当前配置的说明" prop="taskTitle">
        <el-input
          type="textarea"
          v-model="dataForm.taskTitle"
          placeholder="当前配置的说明"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <map-select v-model="dataForm.enabled" :list="coinGrantEnabled" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { coinGrantEnabled, coinGrantRuleLevelType } from '@/map/win'
import MapSelect from '@/components/map-select'
import AppSelectComponent from '@/components/app-select-component'

export default {
  components: { AppSelectComponent, MapSelect },
  data() {
    return {
      coinGrantRuleLevelType,
      coinGrantEnabled,
      visible: false,
      dataForm: {
        id: 0,
        appId: null,
        levelType: null,
        taskTitle: null,
        askLowLimit: null,
        askHighLimit: null,
        lowLimit: null,
        highLimit: null,
        sortValue: null,
        enabled: null,
      },
      dataRule: {
        appId: [{ required: true, message: '应用id不能为空', trigger: 'blur' }],
        levelType: [
          {
            required: true,
            message: '阶段类型:1:当前用户金额总数”来判断发放金额不能为空',
            trigger: 'blur',
          },
        ],
        taskTitle: [
          {
            required: true,
            message: '当前配置的说明不能为空',
            trigger: 'blur',
          },
        ],
        askLowLimit: [
          { required: true, message: '要求下限值不能为空', trigger: 'blur' },
        ],
        askHighLimit: [
          { required: true, message: '要求上限值不能为空', trigger: 'blur' },
        ],
        lowLimit: [
          { required: true, message: '配置的下限值不能为空', trigger: 'blur' },
        ],
        highLimit: [
          { required: true, message: '配置的上限值不能为空', trigger: 'blur' },
        ],
        sortValue: [
          { required: true, message: '排序值不能为空', trigger: 'blur' },
        ],
        enabled: [
          {
            required: true,
            message: '是否启用：1：启用，2：不启用不能为空',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/wincoingrantrule/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.winCoinGrantRule.appId
              this.dataForm.levelType = data.winCoinGrantRule.levelType
              this.dataForm.taskTitle = data.winCoinGrantRule.taskTitle
              this.dataForm.askLowLimit = data.winCoinGrantRule.askLowLimit
              this.dataForm.askHighLimit = data.winCoinGrantRule.askHighLimit
              this.dataForm.lowLimit = data.winCoinGrantRule.lowLimit
              this.dataForm.highLimit = data.winCoinGrantRule.highLimit
              this.dataForm.sortValue = data.winCoinGrantRule.sortValue
              this.dataForm.enabled = data.winCoinGrantRule.enabled
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/whowin/wincoingrantrule/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              levelType: this.dataForm.levelType,
              taskTitle: this.dataForm.taskTitle,
              askLowLimit: this.dataForm.askLowLimit,
              askHighLimit: this.dataForm.askHighLimit,
              lowLimit: this.dataForm.lowLimit,
              highLimit: this.dataForm.highLimit,
              sortValue: this.dataForm.sortValue,
              enabled: this.dataForm.enabled,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
