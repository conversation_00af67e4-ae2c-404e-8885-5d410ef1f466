<template>
  <div class="mod-config">
    <base-form
      :inline="true"
      :model="dataForm"
      @submit="currentChangeHandle(1)"
    >
      <el-form-item>
        <el-button
          v-if="isAuth('whowin:winpunchtaskconfig:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          icon="el-icon-plus"
        >
          新增
        </el-button>
      </el-form-item>
    </base-form>
    <el-table :data="dataList" border :loading="dataListLoading">
      <el-table-column
        header-align="center"
        type="index"
        align="center"
        label="序号"
      />
      <el-table-column prop="createdAt" label="创建时间" />
      <el-table-column prop="clockType" label="打卡形式">
        <template #default="{ row }">
          <span>{{ clockTypeList.get(row.clockType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="clockCondition" label="打卡条件">
        <template #default="{ row }">
          <span>{{ clockConditionList.get(row.clockCondition) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="clockTaskList" label="打卡任务">
        <template #default="{ row }">
          <div>
            <p v-for="(item, index) in row.clockTaskList" :key="index">
              <b>{{ item.dayTime }}</b>
              日打卡 / 奖励金额
              <b>{{ item.rewardMoney }}</b>
              元
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100px">
        <template #default="{ row }">
          <el-button type="text" @click="addOrUpdateHandle(row.id)">
            修改
          </el-button>
          <el-button type="text" @click="deleteHandle(row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AddOrUpdate from './winpunchtaskconfig-add-or-update'
import { clockConditionList, clockTypeList } from '@/map/win'
import BaseForm from '@/components/base-form'

export default {
  data() {
    return {
      dataForm: {},
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      clockTypeList,
      clockConditionList,
      // -----------------
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        totalResult: 0,
      },
    }
  },
  components: {
    BaseForm,
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/whowin/winpunchtaskconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/whowin/winpunchtaskconfig/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.pageIndex = currentPage
      this.pageSize = pageSize
      this.getDataList()
    },
  },
}
</script>
