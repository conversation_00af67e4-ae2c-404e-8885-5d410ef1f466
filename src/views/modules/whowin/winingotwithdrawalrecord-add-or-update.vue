<template>
  <el-dialog
    :title="!dataForm.withdrawalSerialNo ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="提现区间金币" prop="intervalCoin">
      <el-input v-model="dataForm.intervalCoin" placeholder="提现区间金币"></el-input>
    </el-form-item>
    <el-form-item label="开屏曝光" prop="splashExposure">
      <el-input v-model="dataForm.splashExposure" placeholder="开屏曝光"></el-input>
    </el-form-item>
    <el-form-item label="插全屏曝光" prop="interactionExposure">
      <el-input v-model="dataForm.interactionExposure" placeholder="插全屏曝光"></el-input>
    </el-form-item>
    <el-form-item label="激励视频曝光" prop="rewardExposure">
      <el-input v-model="dataForm.rewardExposure" placeholder="激励视频曝光"></el-input>
    </el-form-item>
    <el-form-item label="用户提现时的账户余额" prop="coinBalance">
      <el-input v-model="dataForm.coinBalance" placeholder="用户提现时的账户余额"></el-input>
    </el-form-item>
    <el-form-item label="原生模板曝光" prop="nativeExposure">
      <el-input v-model="dataForm.nativeExposure" placeholder="原生模板曝光"></el-input>
    </el-form-item>
    <el-form-item label="提现区间ecpm" prop="intervalEcpm">
      <el-input v-model="dataForm.intervalEcpm" placeholder="提现区间ecpm"></el-input>
    </el-form-item>
    <el-form-item label="关联流水号" prop="relatedWithdrawalSerialNo">
      <el-input v-model="dataForm.relatedWithdrawalSerialNo" placeholder="关联流水号"></el-input>
    </el-form-item>
    <el-form-item label="付款成功，返回的微信付款单号" prop="paymentNo">
      <el-input v-model="dataForm.paymentNo" placeholder="付款成功，返回的微信付款单号"></el-input>
    </el-form-item>
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="应用 code" prop="appCode">
      <el-input v-model="dataForm.appCode" placeholder="应用 code"></el-input>
    </el-form-item>
    <el-form-item label="提现金额" prop="withdrawalAmount">
      <el-input v-model="dataForm.withdrawalAmount" placeholder="提现金额"></el-input>
    </el-form-item>
    <el-form-item label="提现设置表id" prop="withdrawalConfigId">
      <el-input v-model="dataForm.withdrawalConfigId" placeholder="提现设置表id"></el-input>
    </el-form-item>
    <el-form-item label="是否需要审核 1：需要审核，0：不需要审核" prop="needAudit">
      <el-input v-model="dataForm.needAudit" placeholder="是否需要审核 1：需要审核，0：不需要审核"></el-input>
    </el-form-item>
    <el-form-item label="付款成功时间 " prop="paymentTime">
      <el-input v-model="dataForm.paymentTime" placeholder="付款成功时间 "></el-input>
    </el-form-item>
    <el-form-item label="上次提现时间" prop="lastWithdrawalTime">
      <el-input v-model="dataForm.lastWithdrawalTime" placeholder="上次提现时间"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="审核人" prop="auditBy">
      <el-input v-model="dataForm.auditBy" placeholder="审核人"></el-input>
    </el-form-item>
    <el-form-item label="状态：0：待审核，1：待打款,2：打款成功，3：打款失败，4：不予打款，5：注销中，6：已注销,7:作废" prop="status">
      <el-input v-model="dataForm.status" placeholder="状态：0：待审核，1：待打款,2：打款成功，3：打款失败，4：不予打款，5：注销中，6：已注销,7:作废"></el-input>
    </el-form-item>
    <el-form-item label="安天风险建议" prop="riskAdvice">
      <el-input v-model="dataForm.riskAdvice" placeholder="安天风险建议"></el-input>
    </el-form-item>
    <el-form-item label="风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险，6：风控异常" prop="riskLevel">
      <el-input v-model="dataForm.riskLevel" placeholder="风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险，6：风控异常"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          withdrawalSerialNo: 0,
          intervalCoin: '',
          splashExposure: '',
          interactionExposure: '',
          rewardExposure: '',
          coinBalance: '',
          nativeExposure: '',
          intervalEcpm: '',
          relatedWithdrawalSerialNo: '',
          paymentNo: '',
          userId: '',
          appCode: '',
          withdrawalAmount: '',
          withdrawalConfigId: '',
          needAudit: '',
          paymentTime: '',
          lastWithdrawalTime: '',
          createdAt: '',
          auditBy: '',
          status: '',
          riskAdvice: '',
          riskLevel: '',
          remark: ''
        },
        dataRule: {
          intervalCoin: [
            { required: true, message: '提现区间金币不能为空', trigger: 'blur' }
          ],
          splashExposure: [
            { required: true, message: '开屏曝光不能为空', trigger: 'blur' }
          ],
          interactionExposure: [
            { required: true, message: '插全屏曝光不能为空', trigger: 'blur' }
          ],
          rewardExposure: [
            { required: true, message: '激励视频曝光不能为空', trigger: 'blur' }
          ],
          coinBalance: [
            { required: true, message: '用户提现时的账户余额不能为空', trigger: 'blur' }
          ],
          nativeExposure: [
            { required: true, message: '原生模板曝光不能为空', trigger: 'blur' }
          ],
          intervalEcpm: [
            { required: true, message: '提现区间ecpm不能为空', trigger: 'blur' }
          ],
          relatedWithdrawalSerialNo: [
            { required: true, message: '关联流水号不能为空', trigger: 'blur' }
          ],
          paymentNo: [
            { required: true, message: '付款成功，返回的微信付款单号不能为空', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          appCode: [
            { required: true, message: '应用 code不能为空', trigger: 'blur' }
          ],
          withdrawalAmount: [
            { required: true, message: '提现金额不能为空', trigger: 'blur' }
          ],
          withdrawalConfigId: [
            { required: true, message: '提现设置表id不能为空', trigger: 'blur' }
          ],
          needAudit: [
            { required: true, message: '是否需要审核 1：需要审核，0：不需要审核不能为空', trigger: 'blur' }
          ],
          paymentTime: [
            { required: true, message: '付款成功时间 不能为空', trigger: 'blur' }
          ],
          lastWithdrawalTime: [
            { required: true, message: '上次提现时间不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          auditBy: [
            { required: true, message: '审核人不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '状态：0：待审核，1：待打款,2：打款成功，3：打款失败，4：不予打款，5：注销中，6：已注销,7:作废不能为空', trigger: 'blur' }
          ],
          riskAdvice: [
            { required: true, message: '安天风险建议不能为空', trigger: 'blur' }
          ],
          riskLevel: [
            { required: true, message: '风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险，6：风控异常不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.withdrawalSerialNo = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.withdrawalSerialNo) {
            this.$http({
              url: this.$http.adornUrl(`/whowin/winingotwithdrawalrecord/info/${this.dataForm.withdrawalSerialNo}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.intervalCoin = data.winIngotWithdrawalRecord.intervalCoin
                this.dataForm.splashExposure = data.winIngotWithdrawalRecord.splashExposure
                this.dataForm.interactionExposure = data.winIngotWithdrawalRecord.interactionExposure
                this.dataForm.rewardExposure = data.winIngotWithdrawalRecord.rewardExposure
                this.dataForm.coinBalance = data.winIngotWithdrawalRecord.coinBalance
                this.dataForm.nativeExposure = data.winIngotWithdrawalRecord.nativeExposure
                this.dataForm.intervalEcpm = data.winIngotWithdrawalRecord.intervalEcpm
                this.dataForm.relatedWithdrawalSerialNo = data.winIngotWithdrawalRecord.relatedWithdrawalSerialNo
                this.dataForm.paymentNo = data.winIngotWithdrawalRecord.paymentNo
                this.dataForm.userId = data.winIngotWithdrawalRecord.userId
                this.dataForm.appCode = data.winIngotWithdrawalRecord.appCode
                this.dataForm.withdrawalAmount = data.winIngotWithdrawalRecord.withdrawalAmount
                this.dataForm.withdrawalConfigId = data.winIngotWithdrawalRecord.withdrawalConfigId
                this.dataForm.needAudit = data.winIngotWithdrawalRecord.needAudit
                this.dataForm.paymentTime = data.winIngotWithdrawalRecord.paymentTime
                this.dataForm.lastWithdrawalTime = data.winIngotWithdrawalRecord.lastWithdrawalTime
                this.dataForm.createdAt = data.winIngotWithdrawalRecord.createdAt
                this.dataForm.auditBy = data.winIngotWithdrawalRecord.auditBy
                this.dataForm.status = data.winIngotWithdrawalRecord.status
                this.dataForm.riskAdvice = data.winIngotWithdrawalRecord.riskAdvice
                this.dataForm.riskLevel = data.winIngotWithdrawalRecord.riskLevel
                this.dataForm.remark = data.winIngotWithdrawalRecord.remark
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/whowin/winingotwithdrawalrecord/${!this.dataForm.withdrawalSerialNo ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'withdrawalSerialNo': this.dataForm.withdrawalSerialNo || undefined,
                'intervalCoin': this.dataForm.intervalCoin,
                'splashExposure': this.dataForm.splashExposure,
                'interactionExposure': this.dataForm.interactionExposure,
                'rewardExposure': this.dataForm.rewardExposure,
                'coinBalance': this.dataForm.coinBalance,
                'nativeExposure': this.dataForm.nativeExposure,
                'intervalEcpm': this.dataForm.intervalEcpm,
                'relatedWithdrawalSerialNo': this.dataForm.relatedWithdrawalSerialNo,
                'paymentNo': this.dataForm.paymentNo,
                'userId': this.dataForm.userId,
                'appCode': this.dataForm.appCode,
                'withdrawalAmount': this.dataForm.withdrawalAmount,
                'withdrawalConfigId': this.dataForm.withdrawalConfigId,
                'needAudit': this.dataForm.needAudit,
                'paymentTime': this.dataForm.paymentTime,
                'lastWithdrawalTime': this.dataForm.lastWithdrawalTime,
                'createdAt': this.dataForm.createdAt,
                'auditBy': this.dataForm.auditBy,
                'status': this.dataForm.status,
                'riskAdvice': this.dataForm.riskAdvice,
                'riskLevel': this.dataForm.riskLevel,
                'remark': this.dataForm.remark
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
