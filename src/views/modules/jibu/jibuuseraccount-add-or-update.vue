<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="人气值，初始值2500" prop="popularityValue">
      <el-input v-model="dataForm.popularityValue" placeholder="人气值，初始值2500"></el-input>
    </el-form-item>
    <el-form-item label="账户金币余额" prop="coinBalance">
      <el-input v-model="dataForm.coinBalance" placeholder="账户金币余额"></el-input>
    </el-form-item>
    <el-form-item label="冻结金币" prop="frozenCoin">
      <el-input v-model="dataForm.frozenCoin" placeholder="冻结金币"></el-input>
    </el-form-item>
    <el-form-item label="冻结金币状态：0：无冻结，1：有冻结" prop="frozenStatus">
      <el-input v-model="dataForm.frozenStatus" placeholder="冻结金币状态：0：无冻结，1：有冻结"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          userId: '',
          popularityValue: '',
          coinBalance: '',
          frozenCoin: '',
          frozenStatus: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          popularityValue: [
            { required: true, message: '人气值，初始值2500不能为空', trigger: 'blur' }
          ],
          coinBalance: [
            { required: true, message: '账户金币余额不能为空', trigger: 'blur' }
          ],
          frozenCoin: [
            { required: true, message: '冻结金币不能为空', trigger: 'blur' }
          ],
          frozenStatus: [
            { required: true, message: '冻结金币状态：0：无冻结，1：有冻结不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/jibu/jibuuseraccount/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.userId = data.jibuUserAccount.userId
                this.dataForm.popularityValue = data.jibuUserAccount.popularityValue
                this.dataForm.coinBalance = data.jibuUserAccount.coinBalance
                this.dataForm.frozenCoin = data.jibuUserAccount.frozenCoin
                this.dataForm.frozenStatus = data.jibuUserAccount.frozenStatus
                this.dataForm.createdAt = data.jibuUserAccount.createdAt
                this.dataForm.updatedAt = data.jibuUserAccount.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/jibu/jibuuseraccount/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'userId': this.dataForm.userId,
                'popularityValue': this.dataForm.popularityValue,
                'coinBalance': this.dataForm.coinBalance,
                'frozenCoin': this.dataForm.frozenCoin,
                'frozenStatus': this.dataForm.frozenStatus,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
