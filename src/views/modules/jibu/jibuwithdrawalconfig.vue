<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="currentChangeHandle(1)"
    >
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item label="主体">
        <el-select v-model="dataForm.groupId" @change="changeGroupId">
          <el-option
            v-for="groupId in $store.state.user.groupIdList"
            :value="groupId"
            :label="appGroupWithAll.get(groupId)"
            :key="groupId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择应用">
        <app-select
          ref="appSelect"
          @change="getDataList"
          @init-app-id="changeGroupId"
          :has-all="false"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChangeHandle(1)">查询</el-button>
        <el-button
          v-if="isAuth('packet:appwithdrawalconfig:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('packet:appwithdrawalconfig:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="id"
        width="50"
      />
      <el-table-column
        prop="coin"
        header-align="center"
        align="center"
        label="提现金币"
      ></el-table-column>
      <el-table-column
        prop="amount"
        header-align="center"
        align="center"
        label="提现金额"
      ></el-table-column>
      <el-table-column
        prop="times"
        header-align="center"
        align="center"
        label="提现次数"
      ></el-table-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="提现类型"
        width="200"
      >
        <template slot-scope="{ row }">
          <el-tag>{{ withdrawConfigType.get(row.type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="timeInterval"
        header-align="center"
        align="center"
        label="重复提现间隔时间"
      >
        <template slot-scope="{ row }">
          <el-tag type="warning" v-if="row.timeInterval">
            {{ row.timeInterval }}分钟
          </el-tag>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sortValue"
        header-align="center"
        align="center"
        label="排序值"
      ></el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="{ row }">
          <el-tag>{{ withdrawConfigStatus.get(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="needAudit"
        header-align="center"
        align="center"
        label="自动审核"
      >
        <template slot-scope="{ row }">
          <el-tag>{{ isAutoAudit.get(row.needAudit) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      ></el-table-column>
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      ></el-table-column>
      <el-table-column
        prop="createdBy"
        header-align="center"
        align="center"
        label="创建者"
      ></el-table-column>
      <el-table-column
        prop="updatedBy"
        header-align="center"
        align="center"
        label="最新编辑者"
      ></el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import {
  withdrawConfigType,
  withdrawConfigStatus,
  isAutoAudit,
  appGroupWithAll,
} from '@/map/common'
import AppSelect from '@/components/app-select'
import AddOrUpdate from './jibuwithdrawalconfig-add-or-update'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        groupId: this.$store.state.user.groupIdList[0],
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      withdrawConfigType,
      withdrawConfigStatus,
      isAutoAudit,
      appGroupWithAll,
    }
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const res = this.$store.state.ad.appList.find(
        it => it.id === this.$store.state.ad.appId
      )
      this.$http({
        url: this.$http.adornUrl('/jibu/jibuwithdrawalconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: res && res.code ? res.code : '',
          key: this.dataForm.key,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
          this.dataListLoading = false
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/jibu/jibuwithdrawalconfig/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    changeGroupId() {
      this.pageIndex = 1
      const appList = this.$store.state.ad.appList
      const listAfterChange = this.dataForm.groupId
        ? appList.filter(it => it.groupId === this.dataForm.groupId)
        : appList
      this.$refs.appSelect.changeAppList(listAfterChange)
    },
  },
}
</script>
