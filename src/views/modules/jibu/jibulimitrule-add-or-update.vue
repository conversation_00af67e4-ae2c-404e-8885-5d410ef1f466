<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="130px"
    >
      <el-form-item label="应用" prop="appCode">
        <el-input v-model="appName" disabled />
      </el-form-item>
      <el-form-item label="风控规则" prop="ruleType">
        <el-select v-model="dataForm.ruleType" :disabled="!!dataForm.id">
          <el-option
            v-for="[key, label] in jibuRuleType"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <!-- 步数-奖励档位1 ~ 5 -->
      <template v-if="dataForm.ruleType >= 25 && dataForm.ruleType <= 29">
        <el-form-item label="金币数">
          <el-input type="number" v-model="dataForm.coins">
            <template slot="append">个</template>
          </el-input>
        </el-form-item>
        <el-form-item label="目标步数">
          <el-input type="number" v-model="dataForm.videoLimit">
            <template slot="append">步</template>
          </el-input>
        </el-form-item>
        <el-form-item label="直接激励视频">
          <el-select v-model="dataForm.directIncentiveVideo">
            <el-option
              v-for="[key, label] in directIncentiveVideoStatus"
              :key="key"
              :value="key"
              :label="label"
            />
          </el-select>
        </el-form-item>
      </template>
      <!-- 任务-看视频领金币 -->
      <template v-else-if="dataForm.ruleType === 24">
        <el-form-item label="金币数">
          <el-input type="number" v-model="dataForm.coins">
            <template slot="append">个</template>
          </el-input>
        </el-form-item>
        <el-form-item label="时间间隔">
          <el-input type="number" v-model="dataForm.timeInterval">
            <template slot="append">秒</template>
          </el-input>
        </el-form-item>
        <el-form-item label="播放视频上限">
          <el-input type="number" v-model="dataForm.videoLimit">
            <template slot="append">次</template>
          </el-input>
        </el-form-item>
      </template>
      <!--第1杯到8杯-->
      <template v-else-if="dataForm.ruleType >= 16 && dataForm.ruleType <= 23">
        <el-form-item label="金币数">
          <el-input type="number" v-model="dataForm.coins">
            <template slot="append">个</template>
          </el-input>
        </el-form-item>
        <el-form-item label="直接激励视频">
          <el-select v-model="dataForm.directIncentiveVideo">
            <el-option
              v-for="[key, label] in directIncentiveVideoStatus"
              :key="key"
              :value="key"
              :label="label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="3s后自动翻倍">
          <el-select v-model="dataForm.autoDouble">
            <el-option
              v-for="[key, label] in isAutoDouble"
              :key="key"
              :value="key"
              :label="label"
            />
          </el-select>
        </el-form-item>
      </template>
      <!--有些不需要上下限-->
      <template
        v-if="
          !(dataForm.ruleType >= 25 && dataForm.ruleType <= 29) &&
            dataForm.ruleType !== 24
        "
      >
        <!--上限/下限-->
        <template
          v-if="
            dataForm.ruleType === 7 ||
              dataForm.ruleType === 8 ||
              dataForm.ruleType === 10 ||
              dataForm.ruleType === 11 ||
              dataForm.ruleType === 12 ||
              dataForm.ruleType === 16 ||
              dataForm.ruleType === 17 ||
              dataForm.ruleType === 18 ||
              dataForm.ruleType === 19 ||
              dataForm.ruleType === 20 ||
              dataForm.ruleType === 21 ||
              dataForm.ruleType === 22 ||
              dataForm.ruleType === 23
          "
        >
          <el-form-item
            v-if="dataForm.ruleType >= 16 && dataForm.ruleType <= 23"
            label="时间范围"
          >
            <el-time-picker
              is-range
              arrow-control
              v-model="waterTimeRange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            />
          </el-form-item>
          <template v-else>
            <el-form-item :label="minLabel[dataForm.ruleType]" prop="lowValue">
              <el-input
                type="number"
                v-model="dataForm.lowValue"
                style="width:120px"
                placeholder="最小值"
                clearable
              />
            </el-form-item>
            <el-form-item :label="maxLabel[dataForm.ruleType]" prop="highValue">
              <el-input
                type="number"
                v-model="dataForm.highValue"
                style="width: 120px"
                placeholder="最大值"
                clearable
              />
            </el-form-item>
          </template>
        </template>
        <template v-else>
          <el-form-item :label="minLabel[dataForm.ruleType]" prop="lowValue">
            <el-input
              v-model="dataForm.lowValue"
              type="number"
              clearable
              placeholder="数值下限"
            />
          </el-form-item>
        </template>
      </template>
      <el-form-item label="启用状态" prop="status">
        <el-select v-model="dataForm.status">
          <el-option
            v-for="[key, label] in packetRuleStatus"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  jibuRuleType,
  packetRuleStatus,
  directIncentiveVideoStatus,
  isAutoDouble,
} from '@/map/common'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appCode: '',
        ruleType: '',
        ruleName: '',
        lowValue: 0.3,
        highValue: 0.3,
        status: 0,
        coins: '', // 金币数
        videoLimit: '', // 视频观看次数上限，步数
        timeInterval: '', // 视频观看时间间隔 单位秒
        directIncentiveVideo: '', // 直接激励视频 0：否，1：是
        autoDouble: '', // 自动翻倍
      },
      dataRule: {
        appCode: [
          {
            required: true,
            message: 'app表 appCode不能为空',
            trigger: 'blur',
          },
        ],
        ruleType: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        ruleName: [
          {
            required: true,
            message: '风控规则名称不能为空',
            trigger: 'blur',
          },
        ],
        lowValue: [
          {
            required: true,
            message: '数值下限不能为空',
            trigger: 'blur',
          },
        ],
        highValue: [
          {
            required: true,
            message: '数值上限不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '启用状态：0 未开启，1：开启不能为空',
            trigger: 'blur',
          },
        ],
      },
      appName: '千里计步',
      minLabel: {
        1: '发放金额限制(元)',
        2: '观看视频上限',
        3: '获得金币上限',
        4: 'ecmp固定值',
        5: '每日活动上限',
        6: '新人首次奖励',
        7: '最小值',
        8: '大转盘奖励金币最小值',
        9: '大转盘次数',
        10: '次数',
        11: '次数',
        12: '次数',
        13: '金币',
        14: '金币',
        15: '金币',
        16: '开始时间',
        17: '开始时间',
        18: '开始时间',
        19: '开始时间',
        20: '开始时间',
        21: '开始时间',
        22: '开始时间',
        23: '开始时间',
        24: '金币',
        25: '金币',
        26: '金币',
        27: '金币',
        28: '金币',
        29: '金币',
      },
      maxLabel: {
        7: '最大值',
        8: '金币最大值',
        10: '金币',
        11: '金币',
        12: '金币',
        16: '结束时间',
        17: '结束时间',
        18: '结束时间',
        19: '结束时间',
        20: '结束时间',
        21: '结束时间',
        22: '结束时间',
        23: '结束时间',
      },
      packetRuleStatus,
      jibuRuleType,
      directIncentiveVideoStatus,
      isAutoDouble,
      waterTimeRange: [
        new Date(2021, 12, 22, 8, 40),
        new Date(2021, 12, 22, 9, 40),
      ],
    }
  },
  watch: {
    'dataForm.ruleType'(rule) {
      this.dataForm.ruleName = this.jibuRuleType.get(rule)
    },
    'dataForm.lowValue'(n) {
      if (
        this.dataForm.ruleType !== 7 &&
        this.dataForm.ruleType !== 8 &&
        this.dataForm.ruleType !== 10 &&
        this.dataForm.ruleType !== 11 &&
        this.dataForm.ruleType !== 12 &&
        this.dataForm.ruleType !== 16 &&
        this.dataForm.ruleType !== 17 &&
        this.dataForm.ruleType !== 18 &&
        this.dataForm.ruleType !== 19 &&
        this.dataForm.ruleType !== 20 &&
        this.dataForm.ruleType !== 21 &&
        this.dataForm.ruleType !== 22 &&
        this.dataForm.ruleType !== 23
      ) {
        this.dataForm.highValue = n
      }
    },
    visible(visible) {
      this.$emit('toggle-visible', visible)
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/jibu/jibulimitrule/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              let lowValue = data.jibuLimitRule.lowValue
              let highValue = data.jibuLimitRule.highValue
              const ruleType = data.jibuLimitRule.ruleType

              const toTime = v => {
                const date = new Date()
                const arr = v
                  .toFixed(2)
                  .toString()
                  .split('.')
                const hours = arr[0]
                const minutes = arr[1] || 0
                return new Date(
                  date.getFullYear(),
                  date.getMonth(),
                  date.getDate(),
                  Number(hours),
                  Number(minutes)
                )
              }
              if (ruleType >= 16 && ruleType <= 23) {
                this.waterTimeRange[0] = toTime(lowValue)
                this.waterTimeRange[1] = toTime(highValue)
              }

              this.dataForm.appCode = data.jibuLimitRule.appCode
              this.dataForm.ruleType = ruleType
              this.dataForm.ruleName = data.jibuLimitRule.ruleName
              this.dataForm.lowValue = lowValue
              this.dataForm.highValue = highValue
              this.dataForm.status = data.jibuLimitRule.status
              this.dataForm.coins = data.jibuLimitRule.coins
              this.dataForm.videoLimit = data.jibuLimitRule.videoLimit
              this.dataForm.timeInterval = data.jibuLimitRule.timeInterval
              this.dataForm.directIncentiveVideo =
                data.jibuLimitRule.directIncentiveVideo
              this.dataForm.autoDouble = data.jibuLimitRule.autoDouble
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          let lowValue = this.dataForm.lowValue
          let highValue = this.dataForm.highValue
          if (this.dataForm.ruleType >= 16 && this.dataForm.ruleType <= 23) {
            const startTime = this.waterTimeRange[0]
            const endTime = this.waterTimeRange[1]
            const startHours =
              startTime.getHours() < 10
                ? `0${startTime.getHours()}`
                : startTime.getHours()
            const startMinutes =
              startTime.getMinutes() < 10
                ? `0${startTime.getMinutes()}`
                : startTime.getMinutes()
            const endHours =
              startTime.getHours() < 10
                ? `0${endTime.getHours()}`
                : endTime.getHours()
            const endMinutes =
              endTime.getMinutes() < 10
                ? `0${endTime.getMinutes()}`
                : endTime.getMinutes()

            lowValue = Number(`${startHours}.${startMinutes}`)
            highValue = Number(`${endHours}.${endMinutes}`)
          }

          if (highValue < lowValue) {
            return this.$message.error('最大值不能小于最小值')
          }

          this.$http({
            url: this.$http.adornUrl(
              `/jibu/jibulimitrule/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appCode: this.dataForm.appCode,
              ruleType: this.dataForm.ruleType,
              ruleName: this.dataForm.ruleName,
              lowValue,
              highValue,
              status: this.dataForm.status,
              coins: this.dataForm.coins,
              videoLimit: this.dataForm.videoLimit,
              timeInterval: this.dataForm.timeInterval,
              directIncentiveVideo: this.dataForm.directIncentiveVideo,
              autoDouble: this.dataForm.autoDouble,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
