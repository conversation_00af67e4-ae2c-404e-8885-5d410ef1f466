<template>
  <el-dialog
    :title="!dataForm.withdrawalSerialNo ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="付款成功，返回的微信付款单号" prop="paymentNo">
      <el-input v-model="dataForm.paymentNo" placeholder="付款成功，返回的微信付款单号"></el-input>
    </el-form-item>
    <el-form-item label="用户id" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
    </el-form-item>
    <el-form-item label="应用 code" prop="appCode">
      <el-input v-model="dataForm.appCode" placeholder="应用 code"></el-input>
    </el-form-item>
    <el-form-item label="提现金额" prop="withdrawalAmount">
      <el-input v-model="dataForm.withdrawalAmount" placeholder="提现金额"></el-input>
    </el-form-item>
    <el-form-item label="提现设置表id" prop="withdrawalConfigId">
      <el-input v-model="dataForm.withdrawalConfigId" placeholder="提现设置表id"></el-input>
    </el-form-item>
    <el-form-item label="是否需要审核 1：需要审核，0：不需要审核" prop="needAudit">
      <el-input v-model="dataForm.needAudit" placeholder="是否需要审核 1：需要审核，0：不需要审核"></el-input>
    </el-form-item>
    <el-form-item label="付款成功时间 " prop="paymentTime">
      <el-input v-model="dataForm.paymentTime" placeholder="付款成功时间 "></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="审核人" prop="auditBy">
      <el-input v-model="dataForm.auditBy" placeholder="审核人"></el-input>
    </el-form-item>
    <el-form-item label="状态：0：待审核，1：待打款,2：打款成功，3：打款失败，4：不予打款，4：注销中，6：注销,7:审核异常" prop="status">
      <el-input v-model="dataForm.status" placeholder="状态：0：待审核，1：待打款,2：打款成功，3：打款失败，4：不予打款，4：注销中，6：注销,7:审核异常"></el-input>
    </el-form-item>
    <el-form-item label="风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险，6：风控异常" prop="riskLevel">
      <el-input v-model="dataForm.riskLevel" placeholder="风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险，6：风控异常"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          withdrawalSerialNo: 0,
          paymentNo: '',
          userId: '',
          appCode: '',
          withdrawalAmount: '',
          withdrawalConfigId: '',
          needAudit: '',
          paymentTime: '',
          createdAt: '',
          auditBy: '',
          status: '',
          riskLevel: '',
          remark: ''
        },
        dataRule: {
          paymentNo: [
            { required: true, message: '付款成功，返回的微信付款单号不能为空', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '用户id不能为空', trigger: 'blur' }
          ],
          appCode: [
            { required: true, message: '应用 code不能为空', trigger: 'blur' }
          ],
          withdrawalAmount: [
            { required: true, message: '提现金额不能为空', trigger: 'blur' }
          ],
          withdrawalConfigId: [
            { required: true, message: '提现设置表id不能为空', trigger: 'blur' }
          ],
          needAudit: [
            { required: true, message: '是否需要审核 1：需要审核，0：不需要审核不能为空', trigger: 'blur' }
          ],
          paymentTime: [
            { required: true, message: '付款成功时间 不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          auditBy: [
            { required: true, message: '审核人不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '状态：0：待审核，1：待打款,2：打款成功，3：打款失败，4：不予打款，4：注销中，6：注销,7:审核异常不能为空', trigger: 'blur' }
          ],
          riskLevel: [
            { required: true, message: '风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险，6：风控异常不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.withdrawalSerialNo = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.withdrawalSerialNo) {
            this.$http({
              url: this.$http.adornUrl(`/jibu/jibuuserwithdrawalrecord/info/${this.dataForm.withdrawalSerialNo}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.paymentNo = data.jibuUserWithdrawalRecord.paymentNo
                this.dataForm.userId = data.jibuUserWithdrawalRecord.userId
                this.dataForm.appCode = data.jibuUserWithdrawalRecord.appCode
                this.dataForm.withdrawalAmount = data.jibuUserWithdrawalRecord.withdrawalAmount
                this.dataForm.withdrawalConfigId = data.jibuUserWithdrawalRecord.withdrawalConfigId
                this.dataForm.needAudit = data.jibuUserWithdrawalRecord.needAudit
                this.dataForm.paymentTime = data.jibuUserWithdrawalRecord.paymentTime
                this.dataForm.createdAt = data.jibuUserWithdrawalRecord.createdAt
                this.dataForm.auditBy = data.jibuUserWithdrawalRecord.auditBy
                this.dataForm.status = data.jibuUserWithdrawalRecord.status
                this.dataForm.riskLevel = data.jibuUserWithdrawalRecord.riskLevel
                this.dataForm.remark = data.jibuUserWithdrawalRecord.remark
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/jibu/jibuuserwithdrawalrecord/${!this.dataForm.withdrawalSerialNo ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'withdrawalSerialNo': this.dataForm.withdrawalSerialNo || undefined,
                'paymentNo': this.dataForm.paymentNo,
                'userId': this.dataForm.userId,
                'appCode': this.dataForm.appCode,
                'withdrawalAmount': this.dataForm.withdrawalAmount,
                'withdrawalConfigId': this.dataForm.withdrawalConfigId,
                'needAudit': this.dataForm.needAudit,
                'paymentTime': this.dataForm.paymentTime,
                'createdAt': this.dataForm.createdAt,
                'auditBy': this.dataForm.auditBy,
                'status': this.dataForm.status,
                'riskLevel': this.dataForm.riskLevel,
                'remark': this.dataForm.remark
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
