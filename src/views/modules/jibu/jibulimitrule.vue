<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="currentChangeHandle(1)"
    >
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="currentChangeHandle(1)">
          查询
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('packet:applimitrule:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('packet:applimitrule:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="ID"
        width="50"
      />
      <el-table-column
        prop="appCode"
        header-align="center"
        align="center"
        label="应用"
      >
        <template>
          <el-tag>千里记步</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="ruleName"
        header-align="center"
        align="center"
        label="风控规则名称"
      />
      <el-table-column
        prop="lowValue"
        header-align="center"
        align="center"
        label="数值下限"
      />
      <el-table-column
        prop="highValue"
        header-align="center"
        align="center"
        label="数值上限"
      />
      <el-table-column
        prop="coins"
        header-align="center"
        align="center"
        label="金币数"
      />
      <el-table-column
        prop="videoLimit"
        header-align="center"
        align="center"
        label="视频上限"
      />
      <el-table-column
        prop="videoLimit"
        header-align="center"
        align="center"
        label="步数"
      />
      <el-table-column
        prop="timeInterval"
        header-align="center"
        align="center"
        label="视频时间间隔"
      />
      <el-table-column
        prop="directIncentiveVideo"
        header-align="center"
        align="center"
        label="直接激励视频"
      >
        <template slot-scope="{ row }">
          <el-tag
            v-if="directIncentiveVideoStatus.get(row.directIncentiveVideo)"
          >
            {{ directIncentiveVideoStatus.get(row.directIncentiveVideo) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="autoDouble"
        header-align="center"
        align="center"
        label="自动翻倍"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="isAutoDouble.get(row.autoDouble)">
            {{ isAutoDouble.get(row.autoDouble) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="packetRuleStatus.get(row.status)">
            {{ packetRuleStatus.get(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        prop="createdBy"
        header-align="center"
        align="center"
        label="创建者"
      />
      <el-table-column
        prop="updatedBy"
        header-align="center"
        align="center"
        label="更新者"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <!--<el-button-->
          <!--  type="text"-->
          <!--  size="small"-->
          <!--  @click="deleteHandle(scope.row.id)"-->
          <!--&gt;-->
          <!--  删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
      @toggle-visible="addOrUpdateVisible = $event"
    />
  </div>
</template>

<script>
import AddOrUpdate from './jibulimitrule-add-or-update'

import {
  packetRuleType,
  packetRuleStatus,
  appGroupWithAll,
  directIncentiveVideoStatus,
  isAutoDouble,
} from '@/map/common'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        groupId: this.$store.state.user.groupIdList[0],
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      packetRuleType,
      packetRuleStatus,
      appGroupWithAll,
      directIncentiveVideoStatus,
      isAutoDouble,
    }
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/jibu/jibulimitrule/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: 10025,
          key: this.dataForm.key,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      if (!this.$store.state.ad.appId && !id) {
        return this.$message.error('请先选择应用')
      }

      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/jibu/jibulimitrule/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    changeGroupId() {
      this.pageIndex = 1
      const appList = this.$store.state.ad.appList
      const listAfterChange = this.dataForm.groupId
        ? appList.filter(it => it.groupId === this.dataForm.groupId)
        : appList
      this.$refs.appSelect.changeAppList(listAfterChange)
    },
  },
}
</script>
