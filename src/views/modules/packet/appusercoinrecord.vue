<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="currentChangeHandle(1)"
    >
      <!--<el-form-item>-->
      <!--  <el-input v-model="dataForm.key" placeholder="参数名" clearable />-->
      <!--</el-form-item>-->
      <el-form-item prop="userId" label="用户ID">
        <el-input v-model="dataForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item prop="appId" label="应用">
        <app-select
          :category="2"
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item label="操作明细">
        <el-select v-model="dataForm.operatorType" clearable>
          <el-option
            v-for="[key, label] in goldOperatorType"
            :value="key"
            :label="label"
            :key="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="currentChangeHandle(1)" type="primary ">
          查询
        </el-button>
        <!--<el-button v-if="isAuth('packet:appusercoinrecord:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>-->
        <!--<el-button v-if="isAuth('packet:appusercoinrecord:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>-->
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 20px;">
      <el-button
        type="primary"
        icon="el-icon-download"
        @click="$downloadTableToExcel()"
      >
        下载Excel
      </el-button>
    </div>
    <el-table
      :data="dataList"
      class="adapter-height"
      :max-height="tableHeight"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        type="index"
        header-align="center"
        align="center"
        label="序号"
      />
      <el-table-column
        prop="coinSerialNo"
        header-align="center"
        align="center"
        label="金币流水编号"
      />
      <el-table-column
        prop="userId"
        header-align="center"
        align="center"
        label="用户表id"
      />
      <el-table-column
        prop="appCode"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <el-tag>
            {{
              row.appCode
                | getListLabel($store.state.ad.appList, 'code', 'name')
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="coins"
        header-align="center"
        align="center"
        label="获得金币数，负数为扣除金币"
      />
      <el-table-column
        prop="operatorType"
        header-align="center"
        align="center"
        label="金币操作类型"
      >
        <template slot-scope="{ row }">
          <el-tag>
            {{ goldOperatorType.get(row.operatorType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="relatedCoinSerialNo"
        header-align="center"
        align="center"
        label="翻倍-关联流水号"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <!--<el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.coinSerialNo)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.coinSerialNo)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import { goldOperatorType } from '@/map/common'
import AddOrUpdate from './appusercoinrecord-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'
import AppSelect from '@/components/app-select'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
        userId: '',
        operatorType: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      goldOperatorType,
    }
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    this.init()
    this.getDataList()
  },
  methods: {
    init() {
      this.dataForm.userId = this.$route.query.user_id || ''
    },
    // 获取数据列表
    getDataList() {
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)

      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/packet/appusercoinrecord/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: res ? res.code : '',
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
          this.dataListLoading = false
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.coinSerialNo
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/packet/appusercoinrecord/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
