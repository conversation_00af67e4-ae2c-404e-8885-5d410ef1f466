<template>
  <div>
    <el-form :inline="true" :model="dataForm" class="demo-form-inline">
      <el-form-item prop="appId" label="应用">
        <app-select
          :category="2"
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item prop="status" label="账户状态">
        <el-select v-model="dataForm.userStatus" clearable>
          <el-option
            v-for="[key, label] in userStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="userId" label="用户id">
        <el-input
          v-model.trim="dataForm.userId"
          clearable
          placeholder="用户id"
        />
      </el-form-item>
      <el-form-item prop="withdrawalSerialNo" label="提现流水号">
        <el-input
          v-model.trim="dataForm.withdrawalSerialNo"
          clearable
          placeholder="提现流水号"
        />
      </el-form-item>
      <el-form-item label="提现状态">
        <el-select v-model="dataForm.status" clearable>
          <el-option
            v-for="[key, label] in withdrawStatus"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提现金币">
        <el-select v-model="dataForm.configId" clearable>
          <el-option
            v-for="item in appWithdrawalConfigList"
            :key="item.id"
            :label="item.coin"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          clearable
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:00']"
          style="width: 400px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">查询</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 20px;">
      <el-button
        type="primary"
        icon="el-icon-download"
        @click="$downloadTableToExcel()"
      >
        下载Excel
      </el-button>
    </div>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      stripe
      style="width: 100%"
      border
      v-loading="dataListLoading"
    >
      <el-table-column type="index" width="50" label="序号" align="center" />
      <el-table-column prop="createdAt" label="提现时间" align="center" />
      <el-table-column
        prop="lastWithdrawalTime"
        label="上次提现时间"
        align="center"
      />
      <el-table-column prop="registerTime" label="注册时间" align="center" />
      <el-table-column prop="userId" label="用户ID" align="center" />
      <el-table-column
        prop="openid"
        label="微信openId/unionId"
        align="center"
      />
      <el-table-column
        prop="withdrawalSerialNo"
        label="提现流水编号"
        align="center"
      />
      <el-table-column prop="coinBalance" label="金币余额" align="center" />
      <!--<el-table-column-->
      <!--  prop="grandTotalCoins"-->
      <!--  label="累计收益金币"-->
      <!--  align="center"-->
      <!--/>-->
      <el-table-column
        prop="grandTotalAmount"
        label="累计提现金额（元）"
        align="center"
      />
      <el-table-column
        prop="withdrawalTimes"
        label="累计提现次数"
        align="center"
      />
      <el-table-column
        prop="intervalEcpm"
        label="提现区间累计ecpm"
        align="center"
      />
      <el-table-column
        prop="intervalCoin"
        label="提现区间累计金币数"
        align="center"
      />
      <!--<el-table-column prop="grandTotalEcpm" label="累计ecpm" align="center" />-->
      <el-table-column
        prop="withdrawalAmount"
        label="提取金额（元）"
        align="center"
      />

      <el-table-column prop="splashExposure" label="开屏曝光" align="center" />
      <el-table-column
        prop="interactionExposure"
        label="插全屏曝光"
        align="center"
      />
      <el-table-column
        prop="rewardExposure"
        label="激励视频曝光"
        align="center"
      />
      <el-table-column
        prop="nativeExposure"
        label="原生模板曝光"
        align="center"
      />

      <el-table-column prop="auditBy" label="审核人" align="center" />
      <el-table-column prop="status" label="提现状态" align="center">
        <template slot-scope="{ row }">
          <el-tag>
            {{ withdrawStatus.get(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="userStatus" label="用户状态" align="center">
        <template slot-scope="{ row }">
          <el-tag v-if="userStatus.get(row.userStatus)">
            {{ userStatus.get(row.userStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" />
      <!--<el-table-column prop="riskLevel" label="风险建议" align="center">-->
      <!--  <template slot-scope="{ row }">-->
      <!--    <el-tag>-->
      <!--      {{ riskLevel.get(row.riskLevel) }}-->
      <!--    </el-tag>-->
      <!--  </template>-->
      <!--</el-table-column>-->
      <el-table-column
        prop="atAdvice"
        label="AT建议"
        align="center"
        width="60px"
      >
        <template v-if="row.atAdvice" slot-scope="{ row }">
          <el-tag
            :type="row.atAdvice.toUpperCase().includes('PASS') ? '' : 'warning'"
          >
            {{ getAtSuggestionLabel(row.atAdvice) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="atDesc"
        label="AT风险建议"
        align="center"
        width="120px"
      >
        <template v-if="getRiskTags(row.atDesc).length" slot-scope="{ row }">
          <el-tag
            v-for="(item, index) in getRiskTags(row.atDesc)"
            :key="index"
            type="warning"
          >
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="cheatCode"
        label="反作弊建议"
        align="center"
        width="120px"
      >
        <template slot-scope="{ row }">
          <el-tag
            v-for="(item, index) in getAntiCheatingList(row.cheatCode)"
            :key="index"
            type="warning"
          >
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="failedReason" label="错误信息" align="center" />
      <el-table-column prop="loginTime" label="最近登录时间" align="center" />
      <el-table-column label="操作" width="160" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            :disabled="scope.row.status !== 0"
            size="mini"
            type="text"
            @click="confirmMakeMoney(scope.row)"
          >
            确认打款
          </el-button>
          <el-button
            :disabled="
              !(
                scope.row.status !== 2 &&
                scope.row.status !== 3 &&
                scope.row.status !== 4
              )
            "
            size="mini"
            type="text"
            @click="refuseMakeMoney(scope.row)"
          >
            不予打款
          </el-button>
          <el-button
            type="text"
            :disabled="scope.row.status !== 3"
            @click="genOrderAgain(scope.row)"
          >
            重新生成订单
          </el-button>
          <p>
            <router-link :to="`/packet-appuser?user_id=${scope.row.userId}`">
              账号管理
            </router-link>
          </p>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script>
import {
  withdrawStatus,
  riskLevel,
  antiCheating,
  appGroupWithAll,
  riskTags,
  atSuggestion,
  userStatus,
} from '@/map/common'
import { mixinElTableAdapterHeight } from '@/mixins'
import AppSelect from '@/components/app-select'

export default {
  mixins: [mixinElTableAdapterHeight],
  name: 'apppaudit',
  components: {
    AppSelect,
  },
  data() {
    return {
      pageIndex: 1,
      pageSize: 100,
      dataList: [],
      totalPage: 0,
      dataForm: {
        // appCode: '',
        status: '',
        userStatus: '',
        configId: '',
        endTime: '',
        beginTime: '',
        withdrawalSerialNo: '',
        groupId: this.$store.state.user.groupIdList[0],
        userId: '',
      },
      appList: [],
      appWithdrawalConfigList: [],
      withdrawStatus,
      riskLevel,
      antiCheating,
      timeRange: '',
      dataListLoading: false,
      appGroupWithAll,
      atSuggestion,
      userStatus,
    }
  },
  watch: {
    timeRange(t) {
      if (t) {
        this.dataForm.beginTime = t[0]
        this.dataForm.endTime = t[1]
      } else {
        this.dataForm.beginTime = ''
        this.dataForm.endTime = ''
      }
    },
  },
  activated() {
    // this.getAppList()
    this.getAppWithdrawalConfigList()
    // this.getDataList()
  },
  methods: {
    onSubmit() {
      // this.getDataList()
      this.currentChangeHandle(1)
    },
    getDataList() {
      this.dataListLoading = true
      const appList = this.$store.state.ad.appList
      const res = appList.find(it => it.id === this.$store.state.ad.appId)

      this.$http({
        url: this.$http.adornUrl('/packet/appuserwithdrawalrecord/audit_list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          appCode: res ? res.code : '',
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.$message.error(data.message || '服务器错误')
            this.dataList = []
            this.totalPage = 0
          }
        })
        .catch(() => this.$message.error('服务器错误'))
        .finally(() => (this.dataListLoading = false))
    },
    getAppWithdrawalConfigList() {
      this.$http({
        url: this.$http.adornUrl('/packet/appwithdrawalconfig/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 100,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.appWithdrawalConfigList = data.page.list
        } else {
          this.appWithdrawalConfigList = []
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    refuseMakeMoney(row) {
      console.log(row)
      this.$prompt('确认不予打款吗？', '不予打款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: value => !!value,
        inputErrorMessage: '不能为空',
        inputPlaceholder: '请简单描述不予打款的理由',
      }).then(({ value }) => {
        this.rejectMakeMoney(row.withdrawalSerialNo, value)
      })
    },
    getAppList() {
      const params = {
        page: 1,
        limit: 100,
      }

      this.$store.dispatch('api/app/getAppList', params).then(({ data }) => {
        if (data && data.code === 0) {
          this.appList = data.page.list
        }
      })
    },
    confirmMakeMoney(row) {
      console.log(row)
      if (row.status === 4 || row.status === 5) {
        return this.$message.error('此账号已申请注销，不可进行打款！')
      }

      this.$confirm(`确认打款吗?打款成功将不可追回`, '确认打款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.makeMoney(row.withdrawalSerialNo)
      })
    },
    makeMoney(withdrawalSerialNo) {
      this.$http({
        url: this.$http.adornUrl(
          '/packet/appuserwithdrawalrecord/withdrawalMoney'
        ),
        method: 'put',
        data: this.$http.adornData({
          withdrawalSerialNo,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('打款成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    rejectMakeMoney(withdrawalSerialNo, remark) {
      this.$http({
        url: this.$http.adornUrl(
          '/packet/appuserwithdrawalrecord/withdrawal_reject'
        ),
        method: 'put',
        data: this.$http.adornData({
          remark,
          withdrawalSerialNo,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('操作成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    // 获取反风险作弊建议
    getAntiCheatingList(cheatCode) {
      try {
        return this.map2Arr(JSON.parse(cheatCode), antiCheating)
      } catch (e) {
        return null
      }
    },
    getRiskTags(tags) {
      if (!tags) return []

      try {
        let arr = []
        const list = JSON.parse(JSON.parse(tags))
        for (let i = 0; i < list.length; i++) {
          const item = riskTags.get(list[i])
          item && arr.push(item)
        }
        return arr
      } catch (e) {
        return []
      }
    },
    map2Arr(str, map) {
      if (!str) return []
      let result = []
      const codeList = str.split('/')
      codeList.forEach(it => {
        const item = map.get(Number(it))
        item && result.push(item)
      })
      return result
    },
    genOrderAgain(row) {
      this.$http({
        url: this.$http.adornUrl('/packet/appuserwithdrawalrecord/reOrder'),
        method: 'post',
        data: this.$http.adornData({
          remark: row.remark,
          withdrawalSerialNo: row.withdrawalSerialNo,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('成功')
          } else {
            this.$message.error(data.message || '服务器错误')
          }
        })
        .catch(() => this.$message.error('服务器错误'))
    },
    getAtSuggestionLabel(str) {
      try {
        return atSuggestion.get(JSON.parse(str).toUpperCase()) || null
      } catch (e) {
        return null
      }
    },
  },
}
</script>

<style scoped></style>
