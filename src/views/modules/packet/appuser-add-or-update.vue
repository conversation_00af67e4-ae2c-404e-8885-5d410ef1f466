<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id,用户注册在哪个app" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用id,用户注册在哪个app"></el-input>
    </el-form-item>
    <el-form-item label="微信openid" prop="openId">
      <el-input v-model="dataForm.openId" placeholder="微信openid"></el-input>
    </el-form-item>
    <el-form-item label="微信头像" prop="wechatAvatar">
      <el-input v-model="dataForm.wechatAvatar" placeholder="微信头像"></el-input>
    </el-form-item>
    <el-form-item label="注册时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="注册时间"></el-input>
    </el-form-item>
    <el-form-item label="登录时间" prop="loginTime">
      <el-input v-model="dataForm.loginTime" placeholder="登录时间"></el-input>
    </el-form-item>
    <el-form-item label="微信昵称" prop="nickName">
      <el-input v-model="dataForm.nickName" placeholder="微信昵称"></el-input>
    </el-form-item>
    <el-form-item label="账户状态：1：正常，2：注销待审核，3：已注销，4：封禁" prop="status">
      <el-input v-model="dataForm.status" placeholder="账户状态：1：正常，2：注销待审核，3：已注销，4：封禁"></el-input>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
    </el-form-item>
    <el-form-item label="风险建议" prop="riskSuggest">
      <el-input v-model="dataForm.riskSuggest" placeholder="风险建议"></el-input>
    </el-form-item>
    <el-form-item label="操作人" prop="updatedBy">
      <el-input v-model="dataForm.updatedBy" placeholder="操作人"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          openId: '',
          wechatAvatar: '',
          createdAt: '',
          loginTime: '',
          nickName: '',
          status: '',
          remark: '',
          riskSuggest: '',
          updatedBy: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用id,用户注册在哪个app不能为空', trigger: 'blur' }
          ],
          openId: [
            { required: true, message: '微信openid不能为空', trigger: 'blur' }
          ],
          wechatAvatar: [
            { required: true, message: '微信头像不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '注册时间不能为空', trigger: 'blur' }
          ],
          loginTime: [
            { required: true, message: '登录时间不能为空', trigger: 'blur' }
          ],
          nickName: [
            { required: true, message: '微信昵称不能为空', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '账户状态：1：正常，2：注销待审核，3：已注销，4：封禁不能为空', trigger: 'blur' }
          ],
          remark: [
            { required: true, message: '备注不能为空', trigger: 'blur' }
          ],
          riskSuggest: [
            { required: true, message: '风险建议不能为空', trigger: 'blur' }
          ],
          updatedBy: [
            { required: true, message: '操作人不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/packet/appuser/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.appUser.appId
                this.dataForm.openId = data.appUser.openId
                this.dataForm.wechatAvatar = data.appUser.wechatAvatar
                this.dataForm.createdAt = data.appUser.createdAt
                this.dataForm.loginTime = data.appUser.loginTime
                this.dataForm.nickName = data.appUser.nickName
                this.dataForm.status = data.appUser.status
                this.dataForm.remark = data.appUser.remark
                this.dataForm.riskSuggest = data.appUser.riskSuggest
                this.dataForm.updatedBy = data.appUser.updatedBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/packet/appuser/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'openId': this.dataForm.openId,
                'wechatAvatar': this.dataForm.wechatAvatar,
                'createdAt': this.dataForm.createdAt,
                'loginTime': this.dataForm.loginTime,
                'nickName': this.dataForm.nickName,
                'status': this.dataForm.status,
                'remark': this.dataForm.remark,
                'riskSuggest': this.dataForm.riskSuggest,
                'updatedBy': this.dataForm.updatedBy
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
