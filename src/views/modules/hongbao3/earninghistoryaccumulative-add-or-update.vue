<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="应用id" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
      </el-form-item>
      <el-form-item label="日期" prop="day">
        <el-input v-model="dataForm.day" placeholder="日期"></el-input>
      </el-form-item>
      <el-form-item label="历史总发放金币" prop="totalGiveOutCoins">
        <el-input
          v-model="dataForm.totalGiveOutCoins"
          placeholder="历史总发放金币"
        ></el-input>
      </el-form-item>
      <el-form-item label="历史30天清零总金币" prop="historyCleanCoins">
        <el-input
          v-model="dataForm.historyCleanCoins"
          placeholder="历史30天清零总金币"
        ></el-input>
      </el-form-item>
      <el-form-item label="未体现金币数" prop="noWithdrawalCoins">
        <el-input
          v-model="dataForm.noWithdrawalCoins"
          placeholder="未体现金币数"
        ></el-input>
      </el-form-item>
      <el-form-item label="历史提现成功总金额" prop="totalWithdrawalAmount">
        <el-input
          v-model="dataForm.totalWithdrawalAmount"
          placeholder="历史提现成功总金额"
        ></el-input>
      </el-form-item>
      <el-form-item label="历史提现待审核总金额" prop="waitAuditAmount">
        <el-input
          v-model="dataForm.waitAuditAmount"
          placeholder="历史提现待审核总金额"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-input
          v-model="dataForm.updatedAt"
          placeholder="更新时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建者" prop="createdBy">
        <el-input v-model="dataForm.createdBy" placeholder="创建者"></el-input>
      </el-form-item>
      <el-form-item label="更新者" prop="updatedBy">
        <el-input v-model="dataForm.updatedBy" placeholder="更新者"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appId: '',
        day: '',
        totalGiveOutCoins: '',
        historyCleanCoins: '',
        noWithdrawalCoins: '',
        totalWithdrawalAmount: '',
        waitAuditAmount: '',
        createdAt: '',
        updatedAt: '',
        createdBy: '',
        updatedBy: '',
      },
      dataRule: {
        appId: [{ required: true, message: '应用id不能为空', trigger: 'blur' }],
        day: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        totalGiveOutCoins: [
          {
            required: true,
            message: '历史总发放金币不能为空',
            trigger: 'blur',
          },
        ],
        historyCleanCoins: [
          {
            required: true,
            message: '历史30天清零总金币不能为空',
            trigger: 'blur',
          },
        ],
        noWithdrawalCoins: [
          { required: true, message: '未体现金币数不能为空', trigger: 'blur' },
        ],
        totalWithdrawalAmount: [
          {
            required: true,
            message: '历史提现成功总金额不能为空',
            trigger: 'blur',
          },
        ],
        waitAuditAmount: [
          {
            required: true,
            message: '历史提现待审核总金额不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        updatedAt: [
          { required: true, message: '更新时间不能为空', trigger: 'blur' },
        ],
        createdBy: [
          { required: true, message: '创建者不能为空', trigger: 'blur' },
        ],
        updatedBy: [
          { required: true, message: '更新者不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/earningReport/earninghistoryaccumulative/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.earningHistoryAccumulative.appId
              this.dataForm.day = data.earningHistoryAccumulative.day
              this.dataForm.totalGiveOutCoins =
                data.earningHistoryAccumulative.totalGiveOutCoins
              this.dataForm.historyCleanCoins =
                data.earningHistoryAccumulative.historyCleanCoins
              this.dataForm.noWithdrawalCoins =
                data.earningHistoryAccumulative.noWithdrawalCoins
              this.dataForm.totalWithdrawalAmount =
                data.earningHistoryAccumulative.totalWithdrawalAmount
              this.dataForm.waitAuditAmount =
                data.earningHistoryAccumulative.waitAuditAmount
              this.dataForm.createdAt =
                data.earningHistoryAccumulative.createdAt
              this.dataForm.updatedAt =
                data.earningHistoryAccumulative.updatedAt
              this.dataForm.createdBy =
                data.earningHistoryAccumulative.createdBy
              this.dataForm.updatedBy =
                data.earningHistoryAccumulative.updatedBy
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/earningReport/earninghistoryaccumulative/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              day: this.dataForm.day,
              totalGiveOutCoins: this.dataForm.totalGiveOutCoins,
              historyCleanCoins: this.dataForm.historyCleanCoins,
              noWithdrawalCoins: this.dataForm.noWithdrawalCoins,
              totalWithdrawalAmount: this.dataForm.totalWithdrawalAmount,
              waitAuditAmount: this.dataForm.waitAuditAmount,
              createdAt: this.dataForm.createdAt,
              updatedAt: this.dataForm.updatedAt,
              createdBy: this.dataForm.createdBy,
              updatedBy: this.dataForm.updatedBy,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
