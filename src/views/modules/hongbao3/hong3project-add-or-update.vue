<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="dataForm.projectName"
          placeholder="项目名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-input
          v-model="dataForm.updatedAt"
          placeholder="更新时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建者" prop="createdBy">
        <el-input v-model="dataForm.createdBy" placeholder="创建者"></el-input>
      </el-form-item>
      <el-form-item label="更新者" prop="updatedBy">
        <el-input v-model="dataForm.updatedBy" placeholder="更新者"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        projectName: '',
        createdAt: '',
        updatedAt: '',
        createdBy: '',
        updatedBy: '',
      },
      dataRule: {
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        updatedAt: [
          { required: true, message: '更新时间不能为空', trigger: 'blur' },
        ],
        createdBy: [
          { required: true, message: '创建者不能为空', trigger: 'blur' },
        ],
        updatedBy: [
          { required: true, message: '更新者不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/appproject/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.projectName = data.appProject.projectName
              this.dataForm.createdAt = data.appProject.createdAt
              this.dataForm.updatedAt = data.appProject.updatedAt
              this.dataForm.createdBy = data.appProject.createdBy
              this.dataForm.updatedBy = data.appProject.updatedBy
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/appproject/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              projectName: this.dataForm.projectName,
              createdAt: this.dataForm.createdAt,
              updatedAt: this.dataForm.updatedAt,
              createdBy: this.dataForm.createdBy,
              updatedBy: this.dataForm.updatedBy,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
