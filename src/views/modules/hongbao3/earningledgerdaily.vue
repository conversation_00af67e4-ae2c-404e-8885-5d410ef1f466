<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="应用">-->
      <!--  <app-select-->
      <!--    :category="2"-->
      <!--    @change="currentChangeHandle(1)"-->
      <!--    @init-app-id="currentChangeHandle(1)"-->
      <!--  />-->
      <!--</el-form-item>-->
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('earningReport:earningledgerdaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('earningReport:earningledgerdaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--&gt;</el-table-column>-->
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="id"
        width="50"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用id"
      />
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      />
      <el-table-column
        prop="giveOutCoins"
        header-align="center"
        align="center"
        label="发放金币数"
      />
      <el-table-column
        prop="withdrawalTimes"
        header-align="center"
        align="center"
        label="提现总次数"
      />
      <el-table-column
        prop="withdrawalAmount"
        header-align="center"
        align="center"
        label="提现总金额"
      />
      <el-table-column
        prop="splashExposure"
        header-align="center"
        align="center"
        label="开屏曝光数"
      />
      <el-table-column
        prop="nativeExposure"
        header-align="center"
        align="center"
        label="信息流曝光数"
      />
      <el-table-column
        prop="interactionExposure"
        header-align="center"
        align="center"
        label="插屏曝光数"
      />
      <el-table-column
        prop="rewardExposure"
        header-align="center"
        align="center"
        label="激励视频曝光数"
      />
      <!--<el-table-column-->
      <!--  prop="createdAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="创建时间"-->
      <!--&gt;</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="createdBy"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="创建者"-->
      <!--&gt;</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="updatedAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="更新时间"-->
      <!--&gt;</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="updatedBy"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="更新者"-->
      <!--&gt;</el-table-column>-->
      <!--<el-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.id)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.id)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './earningledgerdaily-add-or-update'
// import AppSelect from '@/components/app-select'
export default {
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
    }
  },
  components: {
    // AppSelect,
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      // const res = this.$store.state.ad.appList.find(
      //   it => it.id === this.$store.state.ad.appId
      // )
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/earningReport/earningledgerdaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
          // appCode: res ? res.code : '',
          appCode: 10053,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/earningReport/earningledgerdaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
