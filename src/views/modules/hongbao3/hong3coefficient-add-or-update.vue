<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="600px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="应用" prop="appId">
        <el-select
          v-model="dataForm.appId"
          placeholder="请选择当前应用"
          filterable
          disabled
        >
          <el-option
            v-for="item in appList"
            :key="item.id"
            :value="item.code"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规则名称" prop="ruleName">
        <el-input v-model.trim="dataForm.ruleName" placeholder="规则名称" />
      </el-form-item>
      <el-form-item label="金币下限" prop="coinLowLimit">
        <el-input-number
          v-model="dataForm.coinLowLimit"
          placeholder="金币下限"
          :min="0"
        />
      </el-form-item>
      <el-form-item label="金币上限" prop="coinHighLimit">
        <el-input-number
          v-model="dataForm.coinHighLimit"
          placeholder="金币上限"
          :min="1"
        />
      </el-form-item>
      <el-form-item label="衰减系数" prop="coefficient">
        <el-input-number
          :min="0"
          :max="100"
          v-model="dataForm.coefficient"
          placeholder="衰减系数"
        />
        %
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-select v-model="dataForm.enabled">
          <el-option :value="1" label="启用" />
          <el-option :value="0" label="未启用" />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    const coinValidate = (_, value, callback) => {
      if (!Number.isInteger(value)) {
        return callback(new Error('必须是整数'))
      }
      if (this.dataForm.coinLowLimit > this.dataForm.coinHighLimit) {
        return callback(new Error('上限不能小于下限'))
      } else {
        this.$refs.dataForm.clearValidate(['coinLowLimit', 'coinHighLimit'])
      }
      callback()
    }

    const coefficientValidate = (_, value, callback) => {
      if (!Number.isInteger(value)) {
        return callback(new Error('必须是整数'))
      }
      callback()
    }

    return {
      visible: false,
      dataForm: {
        id: 0,
        appId: 10053,
        ruleName: '',
        coinLowLimit: '',
        coinHighLimit: '',
        coefficient: '',
        enabled: 1,
      },
      dataRule: {
        appId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        ruleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        coinLowLimit: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: coinValidate },
        ],
        coinHighLimit: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { validator: coinValidate },
        ],
        coefficient: [
          { required: true, message: '衰减系数不能为空', trigger: 'blur' },
          { validator: coefficientValidate },
        ],
        enabled: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
      appList: [],
    }
  },
  methods: {
    init(id) {
      this.getAppList()

      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/coincoefficient/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appId = data.coinCoefficient.appId
              this.dataForm.ruleName = data.coinCoefficient.ruleName
              this.dataForm.coinLowLimit = data.coinCoefficient.coinLowLimit
              this.dataForm.coinHighLimit = data.coinCoefficient.coinHighLimit
              this.dataForm.coefficient = data.coinCoefficient.coefficient * 100
              this.dataForm.enabled = data.coinCoefficient.enabled
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong3/coincoefficient/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appId: this.dataForm.appId,
              ruleName: this.dataForm.ruleName,
              coinLowLimit: this.dataForm.coinLowLimit,
              coinHighLimit: this.dataForm.coinHighLimit,
              coefficient: this.dataForm.coefficient / 100,
              enabled: this.dataForm.enabled,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    getAppList() {
      const params = {}

      this.$store
        .dispatch('api/app/getAppListWithRole', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.apps
          }
        })
    },
  },
}
</script>
