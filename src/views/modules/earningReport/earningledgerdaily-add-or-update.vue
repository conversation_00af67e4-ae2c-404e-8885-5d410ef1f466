<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="应用id" prop="appId">
      <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
    </el-form-item>
    <el-form-item label="日期" prop="day">
      <el-input v-model="dataForm.day" placeholder="日期"></el-input>
    </el-form-item>
    <el-form-item label="发放金币数" prop="giveOutCoins">
      <el-input v-model="dataForm.giveOutCoins" placeholder="发放金币数"></el-input>
    </el-form-item>
    <el-form-item label="提现总次数" prop="withdrawalTimes">
      <el-input v-model="dataForm.withdrawalTimes" placeholder="提现总次数"></el-input>
    </el-form-item>
    <el-form-item label="提现总金额" prop="withdrwalAmount">
      <el-input v-model="dataForm.withdrwalAmount" placeholder="提现总金额"></el-input>
    </el-form-item>
    <el-form-item label="开屏曝光数" prop="splashexposure">
      <el-input v-model="dataForm.splashexposure" placeholder="开屏曝光数"></el-input>
    </el-form-item>
    <el-form-item label="信息流曝光数" prop="nativeexposure">
      <el-input v-model="dataForm.nativeexposure" placeholder="信息流曝光数"></el-input>
    </el-form-item>
    <el-form-item label="插屏曝光数" prop="interactionexposure">
      <el-input v-model="dataForm.interactionexposure" placeholder="插屏曝光数"></el-input>
    </el-form-item>
    <el-form-item label="激励视频曝光数" prop="rewardexposure">
      <el-input v-model="dataForm.rewardexposure" placeholder="激励视频曝光数"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="创建者" prop="createdBy">
      <el-input v-model="dataForm.createdBy" placeholder="创建者"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="更新者" prop="updatedBy">
      <el-input v-model="dataForm.updatedBy" placeholder="更新者"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          appId: '',
          day: '',
          giveOutCoins: '',
          withdrawalTimes: '',
          withdrwalAmount: '',
          splashexposure: '',
          nativeexposure: '',
          interactionexposure: '',
          rewardexposure: '',
          createdAt: '',
          createdBy: '',
          updatedAt: '',
          updatedBy: ''
        },
        dataRule: {
          appId: [
            { required: true, message: '应用id不能为空', trigger: 'blur' }
          ],
          day: [
            { required: true, message: '日期不能为空', trigger: 'blur' }
          ],
          giveOutCoins: [
            { required: true, message: '发放金币数不能为空', trigger: 'blur' }
          ],
          withdrawalTimes: [
            { required: true, message: '提现总次数不能为空', trigger: 'blur' }
          ],
          withdrwalAmount: [
            { required: true, message: '提现总金额不能为空', trigger: 'blur' }
          ],
          splashexposure: [
            { required: true, message: '开屏曝光数不能为空', trigger: 'blur' }
          ],
          nativeexposure: [
            { required: true, message: '信息流曝光数不能为空', trigger: 'blur' }
          ],
          interactionexposure: [
            { required: true, message: '插屏曝光数不能为空', trigger: 'blur' }
          ],
          rewardexposure: [
            { required: true, message: '激励视频曝光数不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          createdBy: [
            { required: true, message: '创建者不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ],
          updatedBy: [
            { required: true, message: '更新者不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/earningReport/earningledgerdaily/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.appId = data.earningLedgerDaily.appId
                this.dataForm.day = data.earningLedgerDaily.day
                this.dataForm.giveOutCoins = data.earningLedgerDaily.giveOutCoins
                this.dataForm.withdrawalTimes = data.earningLedgerDaily.withdrawalTimes
                this.dataForm.withdrwalAmount = data.earningLedgerDaily.withdrwalAmount
                this.dataForm.splashexposure = data.earningLedgerDaily.splashexposure
                this.dataForm.nativeexposure = data.earningLedgerDaily.nativeexposure
                this.dataForm.interactionexposure = data.earningLedgerDaily.interactionexposure
                this.dataForm.rewardexposure = data.earningLedgerDaily.rewardexposure
                this.dataForm.createdAt = data.earningLedgerDaily.createdAt
                this.dataForm.createdBy = data.earningLedgerDaily.createdBy
                this.dataForm.updatedAt = data.earningLedgerDaily.updatedAt
                this.dataForm.updatedBy = data.earningLedgerDaily.updatedBy
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/earningReport/earningledgerdaily/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'appId': this.dataForm.appId,
                'day': this.dataForm.day,
                'giveOutCoins': this.dataForm.giveOutCoins,
                'withdrawalTimes': this.dataForm.withdrawalTimes,
                'withdrwalAmount': this.dataForm.withdrwalAmount,
                'splashexposure': this.dataForm.splashexposure,
                'nativeexposure': this.dataForm.nativeexposure,
                'interactionexposure': this.dataForm.interactionexposure,
                'rewardexposure': this.dataForm.rewardexposure,
                'createdAt': this.dataForm.createdAt,
                'createdBy': this.dataForm.createdBy,
                'updatedAt': this.dataForm.updatedAt,
                'updatedBy': this.dataForm.updatedBy
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
