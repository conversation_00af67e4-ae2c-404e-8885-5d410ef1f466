<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    @closed="$emit('closed')"
    width="600px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="160px"
      label-position="top"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用" prop="appId">
            <el-select
              v-model="dataForm.appId"
              :disabled="!!dataForm.id"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in appList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
                :disabled="item.id === disabledAppId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场景类型" prop="type">
            <el-select
              v-model="dataForm.type"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="[key, label] in sceneType"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场景名" prop="name">
            <el-input v-model.trim="dataForm.name" placeholder="场景名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="场景代码" prop="code">
            <el-input v-model.trim="dataForm.code" placeholder="场景代码" />
          </el-form-item>
        </el-col>
      </el-row>

      <!--系统化-->
      <template v-if="dataForm.type === 1">
        <el-row v-if="dataForm.code !== 'external_conceal'" :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大次数（次）" prop="maxShow">
              <el-input
                v-model.number="dataForm.extra.max_show"
                label="请输入"
                type="number"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最小间隔（秒）" prop="minInterval">
              <el-input
                v-model.number="dataForm.extra.min_interval"
                label="请输入"
                type="number"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <!--场景化-->
      <template v-else-if="dataForm.type === 2">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="每天展示次数" prop="showCount">
              <el-input
                v-model.number="dataForm.extra.show_count"
                type="number"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="dataForm.code !== 'external_time'"
              label="每天解锁多少次后显示"
              prop="unlockCount"
            >
              <el-input
                :min="0"
                v-model.number="dataForm.extra.unlock_count"
                type="number"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间插屏检查间隔" prop="timeCheckInterval">
              <el-input
                :min="0"
                v-model.number="dataForm.extra.time_check_interval"
                type="number"
              />
            </el-form-item>
          </el-col>
          <template v-if="dataForm.code !== 'external_time'">
            <el-col :span="8">
              <el-form-item label="按home次数" prop="homeCount">
                <el-input
                  :min="0"
                  v-model.number="dataForm.extra.home_count"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="home间隔" prop="homeInterval">
                <el-input
                  :min="0"
                  v-model.number="dataForm.extra.home_interval"
                  label="请输入"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="亮屏多少秒后显示" prop="openSecond">
                <el-input
                  :min="0"
                  v-model.number="dataForm.extra.bright_second"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                v-if="dataForm.type === 2"
                label="功能倒计时关闭"
                prop="autoTriggerTime"
              >
                <el-input
                  :min="0"
                  v-model.number="dataForm.extra.auto_trigger_time"
                  type="number"
                />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="8">
            <el-form-item label="时间插屏外部场景" prop="timeSceneTrigger">
              <el-select
                v-model="dataForm.extra.time_scene_trigger"
                style="width: 100%;"
                clearable
              >
                <el-option
                  v-for="[key, label] in timeSceneTriggerStatus"
                  :key="key"
                  :value="key"
                  :label="label"
                />
              </el-select>
              <!--直接弹广告【0】 直接跳出内存清理动效页（2s结束）【1】-->
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <!-- 虚拟关闭 -->
      <template v-else-if="dataForm.type === 3">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="虚假关闭出现概率">
              <el-input
                v-model="dataForm.external_fake_close.fake_close_rate"
                type="number"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触发广告点击的概率">
              <el-input
                v-model="dataForm.external_fake_close.fake_close_click_ad_rate"
                type="number"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="假按钮的次数上限">
              <el-input
                v-model="dataForm.external_fake_close.fake_close_up_limit"
                type="number"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="假按钮展示几秒后消失">
              <el-input
                v-model="dataForm.external_fake_close.fake_close_show_sec"
                type="number"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生效的广告类型">
              <el-select
                v-model="dataForm.external_fake_close.fake_close_ad_types"
                multiple
                collapse-tags
                style="width: 100%"
              >
                <el-option :value="1" label="全屏插屏" />
                <el-option :value="2" label="激励视频" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!--  交互引导 -->
      <template v-else-if="dataForm.type === 4">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交互引导文案">
              <el-input
                v-model="dataForm.external_interact_guide.interact_guide_text"
                clearable
                placeholder="交互引导文案"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出现概率">
              <el-input
                v-model.number="
                  dataForm.external_interact_guide.interact_guide_rate
                "
                type="number"
                clearable
                placeholder="出现概率"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单日上限">
              <el-input
                v-model.number="
                  dataForm.external_interact_guide.interact_guide_up_limit
                "
                type="number"
                clearable
                placeholder="单日上限"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效的广告类型">
              <el-select
                v-model="
                  dataForm.external_interact_guide.interact_guide_ad_types
                "
                multiple
                clearable
                collapse-tags
                style="width: 100%"
              >
                <el-option value="1" label="插屏" />
                <el-option value="2" label="激励视频" />
                <el-option value="3" label="沉浸视频" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 承诺涵 -->
      <template v-else-if="dataForm.type === 5">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="承诺函链接地址">
              <el-input
                v-model="dataForm.external_promise.promise_script_url"
                clearable
                placeholder="承诺函链接地址"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 广告联盟 -->
      <template v-else-if="dataForm.type === 7">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="屏蔽的联盟广告id">
              <el-select
                v-model="dataForm.adAlliance.union_block"
                placeholder="屏蔽的联盟广告id"
                clearable
                style="width: 100%"
              >
                <el-option value="1" label="穿山甲" />
                <el-option value="2" label="优量汇" />
                <el-option value="3" label="快手" />
                <el-option value="4" label="百度" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <el-form-item label="屏蔽规则" prop="blockRuleId">
        <div style="display: flex">
          <el-select v-model="dataForm.blockRuleId" clearable style="flex: 1">
            <el-option
              v-for="{ id, name } in rules"
              :key="id"
              :value="id"
              :label="name"
            />
          </el-select>
          <span style="margin-left: 10px">
            <el-button
              icon="el-icon-plus"
              @click="showRuleExtra = true"
              :disabled="showRuleExtra"
            >
              新增规则
            </el-button>
            <el-button
              icon="el-icon-delete"
              @click="showRuleExtra = false"
              type="danger"
              :disabled="!showRuleExtra"
            >
              移除规则
            </el-button>
          </span>
        </div>
      </el-form-item>

      <el-row :gutter="20" v-show="showRuleExtra">
        <el-col :span="12">
          <el-form-item
            label="平均ecpm"
            label-width="100px"
            prop="block_rule_extra.block_arv_ecpm"
          >
            <el-input
              placeholder="ecpm"
              v-model.number="dataForm.block_rule_extra.block_arv_ecpm"
              style="width: 100%"
              type="number"
              :min="0"
            >
              <el-select
                v-model="ecpmSymbol"
                slot="prepend"
                placeholder="请选择"
                style="width: 80px"
              >
                <el-option label="小于" value="0" />
                <el-option label="大于" value="1" />
              </el-select>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="屏蔽非广告用户"
            label-width="140px"
            prop="block_rule_extra.block_market"
          >
            <el-select
              v-model="dataForm.block_rule_extra.block_market"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="不屏蔽" :value="0" />
              <el-option label="屏蔽" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="屏蔽黑设备"
            label-width="120px"
            prop="block_rule_extra.block_black_device"
          >
            <el-select
              v-model="dataForm.block_rule_extra.block_black_device"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="不屏蔽" :value="0" />
              <el-option label="屏蔽" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市">
            <city-select
              v-model="dataForm.block_rule_extra.block_black_cities"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row
        :gutter="20"
        v-if="
          dataForm.code === 'external_desktop_rest' ||
            dataForm.code === 'external_desktop_water'
        "
      >
        <el-col :span="24">
          <el-form-item label="时间">
            <el-time-select
              @change="_handleChangeStartTime"
              placeholder="起始时间"
              v-model="dataForm.extra.start_time"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '23:59',
              }"
              style="margin-right: 20px;"
            />
            <el-time-select
              @change="_handleChangeEndTime"
              placeholder="结束时间"
              v-model="dataForm.extra.end_time"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '23:59',
                minTime: dataForm.extra.start_time,
              }"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="dataForm.status"
              @change="_changeStatus"
              placeholder="请选择"
              style="width: 100%;"
            >
              <el-option
                v-for="[key, label] in sceneStatus"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-alert
        v-if="dataForm.status !== 1 && dataForm.code === 'external_conceal'"
        show-icon
        :closable="false"
        :title="
          `【危险操作】警告你已经关闭了${sceneStatus.get(
            dataForm.status
          )}隐藏图标功能`
        "
        type="error"
      />
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="_dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { sceneType, sceneStatus, timeSceneTriggerStatus } from '@/map/common'
import { isEmpty } from 'lodash'
import citySelect from '@/components/city-select'

export default {
  components: {
    citySelect,
  },
  props: {
    rules: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        type: '',
        name: '',
        code: '',
        extra: {
          show_count: 0,
          unlock_count: 0,
          home_count: 0,
          bright_second: 0,
          auto_trigger_time: 0,
          time_scene_trigger: 0, // 只能 0 和 1
          home_interval: 0,
          time_check_interval: 0,
          start_time: 0,
          end_time: 0,
          // ------------ 以上是场景化数据 ------------
          // ---------------------------------------
          // ------------ 以下是系统化数据 ------------
          max_show: 50,
          min_interval: 60,
        },

        // 虚拟关闭 3
        external_fake_close: {
          fake_close_rate: 0,
          fake_close_click_ad_rate: 0,
          fake_close_up_limit: 0,
          fake_close_show_sec: 0,
          fake_close_ad_types: [],
        },

        // 交互引导 4
        external_interact_guide: {
          interact_guide_text: '',
          interact_guide_rate: null,
          interact_guide_up_limit: null,
          interact_guide_ad_types: [],
        },

        // 承诺涵 5
        external_promise: {
          promise_script_url: '',
        },

        // 广告联盟 7
        adAlliance: {
          union_block: '',
        },

        block_rule_extra: {
          block_arv_ecpm: null, // 平均ecpm
          block_market: 0, // 屏蔽非广告用户
          block_black_device: 0, // 屏蔽黑设备
          block_black_cities: [], // 屏蔽黑设备
        },
        blockRuleId: '',
        appId: '',
        status: '',
        createdAt: '',
        updatedAt: '',
      },
      showRuleExtra: false,
      dataRule: {
        type: [
          {
            required: true,
            message: '场景类型不能为空',
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            message: '场景名不能为空',
            trigger: 'blur',
          },
        ],
        code: [
          {
            required: true,
            message: '场景代码不能为空',
            trigger: 'blur',
          },
        ],
        showCount: [
          {
            required: false,
            message: '每天展示次数不能为空',
            trigger: 'blur',
          },
        ],
        blockRuleId: [
          {
            required: false,
            message: '屏蔽规则ID不能为空',
            trigger: 'blur',
          },
        ],
        appId: [
          {
            required: true,
            message: '应用ID不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '状态不能为空',
            trigger: 'blur',
          },
        ],
      },
      appList: [],
      sceneType,
      sceneStatus,
      timeSceneTriggerStatus,
      disabledAppId: '',
      ecpmSymbol: '1',
    }
  },

  watch: {
    'dataForm.type'(type) {
      if (type !== 2) {
        this.dataForm.extra.time_scene_trigger = 0
        this.dataForm.extra.auto_trigger_time = 0
      }
    },
  },
  methods: {
    init(id, isCopy = false) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        // this._getRules()
        this._getAppList()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/scene/externalscene/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          })
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.name = data.externalScene.name
                this.dataForm.code = data.externalScene.code
                this.dataForm.type = data.externalScene.type
                if (
                  data.externalScene.blockRuleExtra &&
                  !isEmpty(data.externalScene.blockRuleExtra)
                ) {
                  this.dataForm.block_rule_extra =
                    data.externalScene.blockRuleExtra

                  if (data.externalScene.blockRuleExtra.block_black_cities) {
                    this.dataForm.block_rule_extra.block_black_cities = data.externalScene.blockRuleExtra.block_black_cities.split(
                      ','
                    )
                  }

                  if (this.dataForm.block_rule_extra.block_arv_ecpm > 0) {
                    this.ecpmSymbol = '1'
                  } else {
                    this.ecpmSymbol = '0'
                    this.dataForm.block_rule_extra.block_arv_ecpm =
                      -1 * this.dataForm.block_rule_extra.block_arv_ecpm
                  }
                  this.showRuleExtra = true
                } else {
                  this.showRuleExtra = false
                }

                // parse json
                if (data.externalScene.extra != null) {
                  if (data.externalScene.extra.show_count) {
                    this.dataForm.extra.show_count =
                      data.externalScene.extra.show_count
                  }
                  if (data.externalScene.extra.unlock_count) {
                    this.dataForm.extra.unlock_count =
                      data.externalScene.extra.unlock_count
                  }
                  if (data.externalScene.extra.home_count) {
                    this.dataForm.extra.home_count =
                      data.externalScene.extra.home_count
                  }
                  if (data.externalScene.extra.bright_second) {
                    this.dataForm.extra.bright_second =
                      data.externalScene.extra.bright_second
                  }
                  if (data.externalScene.extra.home_interval) {
                    this.dataForm.extra.home_interval =
                      data.externalScene.extra.home_interval
                  }
                  if (data.externalScene.extra.time_check_interval) {
                    this.dataForm.extra.time_check_interval =
                      data.externalScene.extra.time_check_interval
                  }
                  if (data.externalScene.extra.auto_trigger_time) {
                    this.dataForm.extra.auto_trigger_time =
                      data.externalScene.extra.auto_trigger_time
                  }
                  if (data.externalScene.extra.time_scene_trigger) {
                    this.dataForm.extra.time_scene_trigger =
                      data.externalScene.extra.time_scene_trigger
                  }
                  if (data.externalScene.extra.max_show) {
                    this.dataForm.extra.max_show =
                      data.externalScene.extra.max_show
                  }
                  if (data.externalScene.extra.min_interval) {
                    this.dataForm.extra.min_interval =
                      data.externalScene.extra.min_interval
                  }
                  if (data.externalScene.extra.start_time) {
                    this.dataForm.extra.start_time =
                      data.externalScene.extra.start_time
                  }
                  if (data.externalScene.extra.end_time) {
                    this.dataForm.extra.end_time =
                      data.externalScene.extra.end_time
                  }

                  switch (this.dataForm.type) {
                    case 3:
                      this.dataForm.external_fake_close =
                        data.externalScene.extra
                      break
                    case 4:
                      this.dataForm.external_interact_guide = {
                        ...data.externalScene.extra,
                        interact_guide_ad_types: data.externalScene.extra
                          .interact_guide_ad_types
                          ? data.externalScene.extra.interact_guide_ad_types.split(
                              ','
                            )
                          : [],
                      }

                      break
                    case 5:
                      this.dataForm.external_promise = data.externalScene.extra
                      break
                    case 7:
                      this.dataForm.adAlliance = data.externalScene.extra
                      break
                  }
                }
                //
                this.dataForm.blockRuleId = data.externalScene.blockRuleId
                if (!isCopy) {
                  this.dataForm.appId = data.externalScene.appId
                } else {
                  this.disabledAppId = data.externalScene.appId
                }
                this.dataForm.status = data.externalScene.status
              }
            })
            .finally(() => {
              if (isCopy) {
                this.dataForm.id = null
              }
            })
        }
      })
    },
    // 表单提交
    _dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (
            this.dataForm.status !== 1 &&
            this.dataForm.code === 'external_conceal'
          ) {
            this.$confirm(
              `<p style='color: #f56c6c'>【危险操作】图标隐藏状态被你【${this.sceneStatus.get(
                this.dataForm.status
              )}】,确认是否提交?</p>`,
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error',
                dangerouslyUseHTMLString: true,
              }
            )
              .then(() => {
                this._submitData()
              })
              .catch(() => {})
          } else {
            this._submitData()
          }
        }
      })
    },
    _submitData() {
      let extraJson = {}
      // 场景化
      if (this.dataForm.type === 2 && this.dataForm.extra.show_count > 0) {
        extraJson = {
          show_count: this.dataForm.extra.show_count,
          unlock_count: this.dataForm.extra.unlock_count,
          home_count: this.dataForm.extra.home_count,
          bright_second: this.dataForm.extra.bright_second,
          auto_trigger_time: this.dataForm.extra.auto_trigger_time,
          time_scene_trigger: this.dataForm.extra.time_scene_trigger, // 只能 0 和 1
          home_interval: this.dataForm.extra.home_interval,
          time_check_interval: this.dataForm.extra.time_check_interval,
        }
        !extraJson.home_interval && delete extraJson.home_interval
        !extraJson.time_check_interval && delete extraJson.time_check_interval
      }
      // 系统化
      else if (this.dataForm.type === 1) {
        // 特殊情况: code 为 external_conceal 时不传
        if (this.dataForm.code !== 'external_conceal') {
          extraJson = {
            max_show: this.dataForm.extra.max_show,
            min_interval: this.dataForm.extra.min_interval,
          }
        }
      }
      // 虚拟关闭
      else if (this.dataForm.type === 3) {
        extraJson = this.dataForm.external_fake_close
      }
      // 交互引导
      else if (this.dataForm.type === 4) {
        extraJson = this.dataForm.external_interact_guide
        extraJson.interact_guide_ad_types = extraJson.interact_guide_ad_types.join(
          ','
        )
      }
      // 承诺涵
      else if (this.dataForm.type === 5) {
        extraJson = this.dataForm.external_promise
      }
      // 广告联盟
      else if (this.dataForm.type === 7) {
        extraJson = this.dataForm.adAlliance
      }

      // 喝水，走路
      if (
        this.dataForm.code === 'external_desktop_rest' ||
        this.dataForm.code === 'external_desktop_water'
      ) {
        extraJson.start_time = this.dataForm.extra.start_time
        extraJson.end_time = this.dataForm.extra.end_time
      }

      const params = {
        id: this.dataForm.id || undefined,
        name: this.dataForm.name,
        code: this.dataForm.code,
        type: this.dataForm.type,
        extra: extraJson,
        blockRuleId: this.dataForm.blockRuleId,
        appId: this.dataForm.appId,
        status: this.dataForm.status,
      }

      if (this.showRuleExtra) {
        params.blockRuleExtra = {
          ...this.dataForm.block_rule_extra,
        }
        if (params.blockRuleExtra.block_black_cities) {
          params.blockRuleExtra.block_black_cities = params.blockRuleExtra.block_black_cities.join(
            ','
          )
        }
        // 小于就传负值
        if (this.ecpmSymbol === '0') {
          params.blockRuleExtra.block_arv_ecpm =
            -1 * params.blockRuleExtra.block_arv_ecpm
        }
      } else {
        params.blockRuleExtra = {}
      }

      this.$http({
        url: this.$http.adornUrl(
          `/scene/externalscene/${!this.dataForm.id ? 'save' : 'update'}`
        ),
        method: 'post',
        data: this.$http.adornData(params),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            },
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    /**
     * 获取屏蔽规则
     * @private
     */
    _getRules() {
      this.$http({
        url: this.$http.adornUrl('/block/blockrule/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 100,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.rules = data.page.list
        } else {
          this.rules = []
        }
      })
    },
    /**
     * 获取app列表
     * @private
     */
    _getAppList() {
      this.$store.dispatch('api/app/getAppListWithRole').then(({ data }) => {
        if (data && data.code === 0) {
          this.appList = data.apps
        }
      })
    },
    _changeStatus(v) {
      console.log(v, '--------')
    },
    _handleChangeStartTime(startTime) {
      if (
        !startTime ||
        parseFloat(startTime) >= parseFloat(this.dataForm.extra.end_time)
      ) {
        this.dataForm.extra.end_time = ''
      }
    },
    _handleChangeEndTime(endTime) {
      if (!endTime) {
        this.dataForm.extra.start_time = ''
      }
    },
  },
}
</script>
