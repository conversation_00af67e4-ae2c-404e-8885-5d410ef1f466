<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <el-form-item label="应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()" type="primary" icon="el-icon-search">
          查询
        </el-button>
        <el-button
          v-if="isAuth('scene:externalscene:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          icon="el-icon-plus"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('scene:externalscene:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
        <el-button type="primary" @click="showJsonDialog = true">
          显示json
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      />
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="自增ID"
      />
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="场景名"
      />
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="场景代码"
      />
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="场景类型"
      >
        <template slot-scope="{ row }">
          <el-tag type="success">
            <span>{{ sceneType.get(row.type) }}</span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="extra.show_count"
        header-align="center"
        align="left"
        label="场景配置"
        min-width="188px"
      >
        <template slot-scope="{ row }">
          <ul v-if="!jsonIsEmpty(row.extra)" style="white-space:nowrap;">
            <template v-if="row.type === 2">
              <li>
                每天展示次数: {{ row.extra ? row.extra.show_count : '/' }}
              </li>
              <li v-if="row.code !== 'external_time'">
                每天解锁多少次后显示:
                {{ row.extra ? row.extra.unlock_count : '/' }}
              </li>
              <li v-if="row.code !== 'external_time'">
                按home次数: {{ row.extra ? row.extra.home_count : '/' }}
              </li>
              <li v-if="row.code !== 'external_time'">
                亮屏多少秒后显示:
                {{ row.extra ? row.extra.bright_second : '/' }}
              </li>
              <li v-if="row.code !== 'external_time'">
                home间隔: {{ row.extra ? row.extra.home_interval : '/' }}
              </li>
              <li>
                时间插屏检查间隔:
                {{ row.extra ? row.extra.time_check_interval : '/' }}
              </li>
              <li v-if="row.code !== 'external_time'">
                功能倒计时关闭:
                {{ row.extra ? row.extra.auto_trigger_time : '/' }}
              </li>
              <li v-if="row.code">
                时间插屏外部场景:
                {{
                  row.extra
                    ? timeSceneTriggerStatus.get(row.extra.time_scene_trigger)
                    : '/'
                }}
              </li>
            </template>
            <template v-else-if="row.type === 1">
              <li>最大次数: {{ row.extra ? row.extra.max_show : '/' }}</li>
              <li>最小间隔: {{ row.extra ? row.extra.min_interval : '/' }}</li>
            </template>
          </ul>
          <div v-else style="padding-left: 25px;">无配置</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="blockRuleId"
        header-align="center"
        align="center"
        label="屏蔽规则"
        width="120px"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.blockRuleId" type="success">
            {{ row.blockRuleId | getListLabel(rules, 'id', 'name') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <el-tag type="success">
            {{
              row.appId | getListLabel($store.state.ad.appList, 'id', 'name')
            }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="{ row }">
          <span>{{ sceneStatus.get(row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
        width="160"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
        width="160"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id, true)"
          >
            <span style="color: red">复制</span>
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      :rules="rules"
      @refreshDataList="getDataList"
      @closed="addOrUpdateVisible = false"
    />
    <el-dialog :visible.sync="showJsonDialog">
      <div style="position: relative">
        <vue-json-editor
          v-model="jsonDataList"
          :expandedOnStart="true"
          mode="code"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import { sceneType, sceneStatus, timeSceneTriggerStatus } from '@/map/common'
import AddOrUpdate from './externalscene-add-or-update'
// import { mixinElTableAdapterHeight } from '@/mixins'
import { jsonIsEmpty } from '@/utils'
import vueJsonEditor from '@/components/my-vue-json-editor'

export default {
  // mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      showJsonDialog: false,
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      sceneType,
      sceneStatus,
      timeSceneTriggerStatus,
      rules: [],
    }
  },
  computed: {
    jsonDataList() {
      if (this.dataList && this.dataList.length) {
        return this.dataList.map(it => {
          const extra = {}
          if (it.extra) {
            for (const key in it.extra) {
              extra[window.btoa(key)] = it.extra[key]
            }
          }
          return {
            ...it,
            extra,
          }
        })
      }
      return null
    },
  },
  components: {
    AddOrUpdate,
    AppSelect,
    vueJsonEditor,
  },
  activated() {
    // this.getDataList()
    this.getRules()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/scene/externalscene/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          key: this.dataForm.key,
          app_id: this.$store.state.ad.appId || '',
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id, isCopy = false) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, isCopy)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/scene/externalscene/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    /**
     * 获取屏蔽规则
     * @private
     */
    getRules() {
      this.$http({
        url: this.$http.adornUrl('/block/blockrule/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 100,
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.rules = data.page.list
        } else {
          this.rules = []
        }
      })
    },
    jsonIsEmpty,
  },
}
</script>
