<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
      </el-form-item>
      <el-form-item label="当日领取金币" prop="coinBalance">
        <el-input
          v-model="dataForm.coinBalance"
          placeholder="当日领取金币"
        ></el-input>
      </el-form-item>
      <el-form-item label="金币领取状态：0：未领取，1：已领取" prop="status">
        <el-input
          v-model="dataForm.status"
          placeholder="金币领取状态：0：未领取，1：已领取"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="过期时间" prop="expiredAt">
        <el-input
          v-model="dataForm.expiredAt"
          placeholder="过期时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-input
          v-model="dataForm.updatedAt"
          placeholder="更新时间"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        userId: '',
        coinBalance: '',
        status: '',
        createdAt: '',
        expiredAt: '',
        updatedAt: '',
      },
      dataRule: {
        userId: [
          { required: true, message: '用户id不能为空', trigger: 'blur' },
        ],
        coinBalance: [
          { required: true, message: '当日领取金币不能为空', trigger: 'blur' },
        ],
        status: [
          {
            required: true,
            message: '金币领取状态：0：未领取，1：已领取不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        expiredAt: [
          { required: true, message: '过期时间不能为空', trigger: 'blur' },
        ],
        updatedAt: [
          { required: true, message: '更新时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao/honggoldpigcoinrecord/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.userId = data.winGoldPigCoinRecord.userId
              this.dataForm.coinBalance = data.winGoldPigCoinRecord.coinBalance
              this.dataForm.status = data.winGoldPigCoinRecord.status
              this.dataForm.createdAt = data.winGoldPigCoinRecord.createdAt
              this.dataForm.expiredAt = data.winGoldPigCoinRecord.expiredAt
              this.dataForm.updatedAt = data.winGoldPigCoinRecord.updatedAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hongbao/honggoldpigcoinrecord/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              userId: this.dataForm.userId,
              coinBalance: this.dataForm.coinBalance,
              status: this.dataForm.status,
              createdAt: this.dataForm.createdAt,
              expiredAt: this.dataForm.expiredAt,
              updatedAt: this.dataForm.updatedAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
