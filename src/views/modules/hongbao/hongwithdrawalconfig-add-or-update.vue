<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="880px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="提现金币" prop="coin">
            <el-input-number
              v-model.trim="dataForm.coin"
              :min="0"
              placeholder="提现金币"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提现金额" prop="amount">
            <el-input-number
              v-model.trim="dataForm.amount"
              :min="0"
              placeholder="提现金额"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="提现次数" prop="times">
            <el-input-number
              v-model.trim="dataForm.times"
              :min="0"
              placeholder="提现次数"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="重复提现间隔时间" prop="timeInterval">
            <el-input-number
              v-model.trim="dataForm.timeInterval"
              placeholder="重复提现间隔时间"
              :min="0"
            >
              <template slot="append">小时</template>
            </el-input-number>
            <span>小时</span>
          </el-form-item>
        </el-col>

        <!--<el-col :span="12">-->
        <!--  <el-form-item label="视频观看次数要求" prop="limitNum">-->
        <!--    <el-input-number-->
        <!--      v-model.trim="dataForm.limitNum"-->
        <!--      :min="0"-->
        <!--      placeholder="视频观看次数要求"-->
        <!--    />-->
        <!--  </el-form-item>-->
        <!--</el-col>-->
      </el-row>
      <el-row :span="20">
        <el-col :span="12">
          <el-form-item label="两档提现时间间隔" prop="betweenTimeInterval">
            <el-input-number
              v-model.trim="dataForm.betweenTimeInterval"
              placeholder="两档提现时间间隔"
              :min="0"
            />
            <span>小时</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序值" prop="sortValue">
            <el-input-number
              v-model="dataForm.sortValue"
              :min="1"
              placeholder="排序值"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提现类型" prop="type">
            <el-select v-model="dataForm.type">
              <el-option
                v-for="[key, label] in withdrawConfigType"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <!--<el-col :span="12">-->
        <!--  <el-form-item label="提现显示类型" prop="withdrawalLimit">-->
        <!--    <el-select v-model="dataForm.withdrawalLimit">-->
        <!--      <el-option-->
        <!--        v-for="[key, label] in withdrawalLimitList"-->
        <!--        :key="key"-->
        <!--        :value="key"-->
        <!--        :label="label"-->
        <!--      />-->
        <!--    </el-select>-->
        <!--  </el-form-item>-->
        <!--</el-col>-->
      </el-row>
      <el-row :gutter="20">
        <el-col>
          <el-form-item label="提现限制条件" prop="withdrawalLimit">
            <el-form
              ref="withdrawalLimitDataForm"
              :model="withdrawalLimitDataForm"
            >
              <el-checkbox-group v-model="withdrawalLimitDataForm.select">
                <el-form-item
                  v-for="[key, label] in withdrawalLimitList"
                  :key="key"
                >
                  <el-checkbox
                    :label="key"
                    @change="handleSelectLimit($event, key)"
                  >
                    <el-input
                      v-model.number="withdrawalLimitDataForm.list[key]"
                      type="number"
                      :min="0"
                      :disabled="key === 1 || key === 2"
                    >
                      <template slot="prepend">
                        <div style="width: 95px;">
                          {{ label }}
                        </div>
                      </template>
                    </el-input>
                  </el-checkbox>
                </el-form-item>
              </el-checkbox-group>
            </el-form>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12"></el-col>
        <el-col :span="12">
          <el-form-item label="是否自动审核" prop="needAudit">
            <el-select v-model="dataForm.needAudit">
              <el-option
                v-for="[key, label] in isAutoAudit"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="应用" prop="appCode">
            <el-select v-model="dataForm.appCode" :disabled="!!dataForm.id">
              <template v-for="item in $store.state.ad.appList">
                <el-option
                  v-if="item.id"
                  :key="item.code"
                  :value="item.code"
                  :label="item.name"
                />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8"></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="9">
          <el-form-item label="版本号" prop="version">
            <el-select v-model="versionMultiple" multiple>
              <el-option
                v-for="item in versionList"
                :key="item.versionCode"
                :value="item.versionName"
                :label="item.versionName"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="版本号范围" label-width="90px">
            <el-select
              v-model="versionRange[0]"
              style="width: 120px;"
              clearable
            >
              <el-option
                v-for="item in versionList"
                :key="item.versionCode"
                :value="item.versionName"
                :label="item.versionName"
              />
            </el-select>
            <span style="padding: 0 5px">至</span>
            <el-select
              v-model="versionRange[1]"
              style="width: 120px;"
              clearable
            >
              <el-option
                v-for="item in versionList"
                :key="item.versionCode"
                :value="item.versionName"
                :label="item.versionName"
              />
            </el-select>
            <div v-if="isShowRangeError" style="color: #F56C6C">
              前值不能大于后值
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="提现说明" prop="desc">
            <el-input
              v-model.trim="dataForm.description"
              type="textarea"
              placeholder="提现说明"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="dataForm.status">
              <el-option
                v-for="[key, label] in withdrawConfigStatus"
                :key="key"
                :value="key"
                :label="label"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()" :loading="loading">
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  withdrawConfigType,
  withdrawConfigStatus,
  isAutoAudit,
  withdrawalLimitList,
} from '@/map/common'
import { difference } from 'lodash'

const withdrawalLimitDataFormList = {}
withdrawalLimitList.forEach((_, key) => {
  withdrawalLimitDataFormList[key] = 0
})

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        coin: '',
        amount: '',
        times: '',
        type: '',
        timeInterval: '',
        sortValue: '',
        status: '',
        needAudit: 0,
        appCode: '',
        withdrawalLimit: '',
        limitNum: '',
        version: '',
        description: '',
        betweenTimeInterval: '',
      },
      dataRule: {
        coin: [
          {
            required: true,
            message: '提现金币不能为空',
            trigger: 'blur',
          },
        ],
        amount: [
          {
            required: true,
            message: '提现金额不能为空',
            trigger: 'blur',
          },
        ],
        times: [
          {
            required: true,
            message: '提现次数不能为空',
            trigger: 'blur',
          },
        ],
        type: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        timeInterval: [
          {
            required: true,
            message: '重复提现间隔时间，单位小时不能为空',
            trigger: 'blur',
          },
        ],
        sortValue: [
          {
            required: true,
            message: '排序值不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        needAudit: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        appCode: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],

        withdrawalLimit: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        limitNum: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
        betweenTimeInterval: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur',
          },
        ],
      },
      withdrawConfigType,
      withdrawConfigStatus,
      isAutoAudit,
      withdrawalLimitList,
      versionList: [],
      versionRange: ['', ''],
      versionMultiple: [],
      isShowRangeError: false,
      withdrawalLimitDataForm: {
        select: [],
        list: withdrawalLimitDataFormList,
      },
      loading: false,
    }
  },
  watch: {
    'dataForm.appCode'() {
      this.versionRange = ['', '']
      this.versionMultiple = []
      if (this.dataForm.appCode) {
        setTimeout(() => {
          this.getVersionList()
        })
      }
    },
    versionRange(range) {
      this.isShowRangeError = range[0] > range[1]
    },
    'withdrawalLimitDataForm.select'() {
      this.buildWithdrawalLimit()
      this.$refs.dataForm.validateField('withdrawalLimit')
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong/appwithdrawalconfig/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.coin = data.appWithdrawalConfig.coin
              this.dataForm.amount = data.appWithdrawalConfig.amount
              this.dataForm.times = data.appWithdrawalConfig.times
              this.dataForm.type = data.appWithdrawalConfig.type
              this.dataForm.timeInterval = data.appWithdrawalConfig.timeInterval
              this.dataForm.sortValue = data.appWithdrawalConfig.sortValue
              this.dataForm.status = data.appWithdrawalConfig.status
              this.dataForm.needAudit = data.appWithdrawalConfig.needAudit
              this.dataForm.appCode = data.appWithdrawalConfig.appCode
              this.dataForm.limitNum = data.appWithdrawalConfig.limitNum
              this.dataForm.withdrawalLimit =
                data.appWithdrawalConfig.withdrawalLimit
              this.dataForm.version = data.appWithdrawalConfig.version
              this.dataForm.description = data.appWithdrawalConfig.description
              this.dataForm.betweenTimeInterval =
                data.appWithdrawalConfig.betweenTimeInterval

              if (this.dataForm.withdrawalLimit) {
                this.withdrawalLimitDataForm.select = this.dataForm.withdrawalLimit.map(
                  it => it.limitType
                )
                this.dataForm.withdrawalLimit.forEach(it => {
                  this.withdrawalLimitDataForm.list[it.limitType] = it.limitNum
                })
                console.log(this.withdrawalLimitDataForm.list, '====')
              }

              setTimeout(() => {
                try {
                  const versionJson = JSON.parse(data.appWithdrawalConfig.extra)
                  this.versionRange = [...versionJson.versionRange]
                  this.versionMultiple = [...versionJson.versionMultiple]
                } catch (e) {
                  console.log(e)
                }
              })
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      Promise.all([
        this.$refs.withdrawalLimitDataForm.validate(),
        this.$refs.dataForm.validate(),
      ]).then(() => {
        this.buildWithdrawalLimit()
        let versionList = []
        if (this.versionRange[0] && this.versionRange[1]) {
          const startVersion = Number(this.versionRange[0].replaceAll('.', ''))
          const endVersion = Number(this.versionRange[1].replaceAll('.', ''))
          for (let i = startVersion; i <= endVersion; i++) {
            versionList.push(
              i
                .toString()
                .split('')
                .join('.')
            )
          }
        }
        versionList = versionList.concat(this.versionMultiple)
        this.dataForm.version = Array.from(new Set(versionList)).map(
          it =>
            this.versionList.find(item => item.versionName === it).versionCode
        )

        if (!this.dataForm.version.length) {
          this.dataForm.version = null
        }

        this.loading = true
        this.$http({
          url: this.$http.adornUrl(
            `/hong/appwithdrawalconfig/${!this.dataForm.id ? 'save' : 'update'}`
          ),
          method: 'post',
          data: this.$http.adornData({
            ...this.dataForm,
            id: this.dataForm.id || undefined,
            extra: JSON.stringify({
              versionRange: this.versionRange,
              versionMultiple: this.versionMultiple,
            }),
          }),
        })
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
              })
              this.visible = false
              this.$emit('refreshDataList')
            } else {
              this.$message.error(data.msg || '服务器错误')
            }
          })
          .catch(() => this.$message.error('服务器错误'))
          .finally(() => {
            this.loading = false
          })
      })
    },
    getVersionList() {
      const res = this.$store.state.ad.appList.find(
        it => it.code === this.dataForm.appCode
      )
      const appId = res ? res.id : ''
      const params = {
        page: 1,
        limit: 100,
        key: '',
        app_id: appId,
      }

      this.$store
        .dispatch('api/app/getVersionList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.versionList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.versionList = []
            this.totalPage = 0
          }
        })
    },
    buildWithdrawalLimit() {
      const arr = []
      const select = this.withdrawalLimitDataForm.select
      for (let listKey in this.withdrawalLimitDataForm.list) {
        if (select.includes(Number(listKey))) {
          arr.push({
            limitType: Number(listKey),
            limitNum: this.withdrawalLimitDataForm.list[listKey],
          })
        }
      }
      this.dataForm.withdrawalLimit = arr
    },
    handleSelectLimit(flag, key) {
      const noLimitKey = 1
      const noLimitKeyArr = [noLimitKey]

      if (flag) {
        if (key === noLimitKey) {
          this.withdrawalLimitDataForm.select = noLimitKeyArr
        } else {
          this.withdrawalLimitDataForm.select = difference(
            this.withdrawalLimitDataForm.select,
            noLimitKeyArr
          )
        }
      }
    },
  },
}
</script>
