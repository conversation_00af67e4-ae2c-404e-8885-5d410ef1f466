<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="用户ID">
        <el-input v-model="dataForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          clearable
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyyMMdd"
          :default-time="['00:00:00', '23:59:00']"
          style="width: 400px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          @click="getDataList()"
          type="primary"
          icon="el-icon-search"
          :loading="dataListLoading"
        >
          查询
        </el-button>
        <!--<el-button-->
        <!--  v-if="isAuth('hongbao:hongtaskdatadaily:save')"-->
        <!--  type="primary"-->
        <!--  @click="addOrUpdateHandle()"-->
        <!--&gt;-->
        <!--  新增-->
        <!--</el-button>-->
        <!--<el-button-->
        <!--  v-if="isAuth('hongbao:hongtaskdatadaily:delete')"-->
        <!--  type="danger"-->
        <!--  @click="deleteHandle()"-->
        <!--  :disabled="dataListSelections.length <= 0"-->
        <!--&gt;-->
        <!--  批量删除-->
        <!--</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      class="adapter-height"
      :max-height="tableHeight"
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <!--<el-table-column-->
      <!--  type="selection"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="50"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="id"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="ID"-->
      <!--/>-->
      <el-table-column
        prop="day"
        header-align="center"
        align="center"
        label="日期"
      >
        <template slot-scope="{ row }">
          <span>{{ $dayjs(String(row.day)).format('YYYY-MM-DD') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用"
      >
        <template slot-scope="{ row }">
          <span>{{ row.appId | getAppName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="userId"
        header-align="center"
        align="center"
        label="用户ID"
      />
      <el-table-column
        prop="videoTimes"
        header-align="center"
        align="center"
        label="激励视频数字"
      />
      <el-table-column
        prop="taskType"
        header-align="center"
        align="center"
        label="任务类型"
      >
        <template slot-scope="{ row }">
          <span>{{ taskTypeList.get(row.taskType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="finishTimes"
        header-align="center"
        align="center"
        label="任务次数完成"
      />
      <el-table-column
        prop="askTime"
        header-align="center"
        align="center"
        label="任务要求次数"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updateAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <!--<el-table-column-->
      <!--  fixed="right"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  width="150"-->
      <!--  label="操作"-->
      <!--&gt;-->
      <!--  <template slot-scope="scope">-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="addOrUpdateHandle(scope.row.id)"-->
      <!--    >-->
      <!--      修改-->
      <!--    </el-button>-->
      <!--    <el-button-->
      <!--      type="text"-->
      <!--      size="small"-->
      <!--      @click="deleteHandle(scope.row.id)"-->
      <!--    >-->
      <!--      删除-->
      <!--    </el-button>-->
      <!--  </template>-->
      <!--</el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './hongtaskdatadaily-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'
import { taskTypeList } from '@/map/common'
export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        userId: '',
        startTime: '',
        endTime: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      taskTypeList,
    }
  },
  computed: {
    timeRange: {
      set(t) {
        if (t) {
          this.dataForm.startTime = t[0] || ''
          this.dataForm.endTime = t[1] || ''
        } else {
          this.dataForm.startTime = ''
          this.dataForm.endTime = ''
        }
      },
      get() {
        return [this.dataForm.startTime, this.dataForm.endTime]
      },
    },
  },
  components: {
    AddOrUpdate,
  },
  activated() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/hongbao/hongtaskdatadaily/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: this.pageIndex,
          limit: this.pageSize,
          ...this.dataForm,
        }),
      })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务器错误')
          }
        })
        .catch(e => {
          this.$message.error(e.msg || '服务器错误')
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$http({
          url: this.$http.adornUrl('/hongbao/hongtaskdatadaily/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false),
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              },
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
