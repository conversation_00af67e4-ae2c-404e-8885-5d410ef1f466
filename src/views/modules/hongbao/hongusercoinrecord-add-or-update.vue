<template>
  <el-dialog
    :title="!dataForm.coinSerialNo ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-form-item label="用户表id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户表id"></el-input>
      </el-form-item>
      <el-form-item label="应用id" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
      </el-form-item>
      <el-form-item label="获得金币数，负数为扣除金币" prop="coins">
        <el-input
          v-model="dataForm.coins"
          placeholder="获得金币数，负数为扣除金币"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="金币操作类型：1：超过30天清零，2：视频红包，3：提现成功，4：注销账号"
        prop="operatorType"
      >
        <el-input
          v-model="dataForm.operatorType"
          placeholder="金币操作类型：1：超过30天清零，2：视频红包，3：提现成功，4：注销账号"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        coinSerialNo: 0,
        userId: '',
        appId: '',
        coins: '',
        operatorType: '',
        createdAt: '',
      },
      dataRule: {
        userId: [
          { required: true, message: '用户表id不能为空', trigger: 'blur' },
        ],
        appId: [{ required: true, message: '应用id不能为空', trigger: 'blur' }],
        coins: [
          {
            required: true,
            message: '获得金币数，负数为扣除金币不能为空',
            trigger: 'blur',
          },
        ],
        operatorType: [
          {
            required: true,
            message:
              '金币操作类型：1：超过30天清零，2：视频红包，3：提现成功，4：注销账号不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.coinSerialNo = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.coinSerialNo) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong/appusercoinrecord/info/${this.dataForm.coinSerialNo}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.userId = data.appUserCoinRecord.userId
              this.dataForm.appId = data.appUserCoinRecord.appId
              this.dataForm.coins = data.appUserCoinRecord.coins
              this.dataForm.operatorType = data.appUserCoinRecord.operatorType
              this.dataForm.createdAt = data.appUserCoinRecord.createdAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/hong/appusercoinrecord/${
                !this.dataForm.coinSerialNo ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              coinSerialNo: this.dataForm.coinSerialNo || undefined,
              userId: this.dataForm.userId,
              appId: this.dataForm.appId,
              coins: this.dataForm.coins,
              operatorType: this.dataForm.operatorType,
              createdAt: this.dataForm.createdAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
