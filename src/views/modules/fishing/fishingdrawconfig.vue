<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      'delete',
      'insert',
      'update',
      'batch_offline',
      'batch_online',
      'offline',
      'online',
    ]"
    operate-width="330"
  >
    <template #table_item_type="{row}">
      <color-tag :id="row.drawType">
        {{ drawConfigMap.typeList.get(row.drawType) }}
      </color-tag>
    </template>
    <template #table_item_status="{row}">
      <tag-status :status="row.status" />
    </template>
    <template #table_item_fishId="{row}">
      <color-tag :id="row.fishId">
        {{ getFishName(row.fishId) }}
      </color-tag>
    </template>
  </page-table>
</template>

<script>
import { fishingDrawConfig as request, baseConfigRequest } from '@/api/fishing'
import { drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          {
            field: 'drawType',
            title: '类型',
            width: 100,
            slots: {
              default: 'table_item_type',
            },
          },
          {
            field: 'fishId',
            title: '鱼id',
            slots: {
              default: 'table_item_fishId',
            },
          },
          { field: 'probability', title: '概率' },
          {
            field: 'status',
            title: '状态',
            slots: {
              default: 'table_item_status',
            },
          },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '60px',
        },
        formData: {
          id: null,
          drawType: '',
          fishId: '',
          probability: '',
          status: '',
        },
        formItemMap: {
          drawType: {
            title: '类型',
            itemRender: {
              name: 'map-select',
              attrs: {
                list: drawConfigMap.typeList,
              },
            },
          },
          fishId: {
            title: '鱼',
            itemRender: {
              name: 'arr-select',
              attrs: {
                type: 'number',
                list: [],
                valueKey: 'fishId',
                labelKey: 'name',
              },
            },
          },
          probability: {
            title: '概率',
            itemRender: {
              attrs: {
                type: 'number',
              },
            },
          },
          status: {
            title: '状态',
            itemRender: {
              name: 'radio-status',
            },
          },
        },
        formRule: {
          drawType: [{ required: true, message: '不能为空', trigger: 'blur' }],
          fishId: [{ required: true, message: '不能为空', trigger: 'blur' }],
          probability: [
            { required: true, message: '不能为空', trigger: 'blur' },
          ],
          status: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
  activated() {
    baseConfigRequest
      .selectAll({ currentPage: 1, pageSize: 10000 })
      .then(res => {
        if (res.code === 0 && res.page && res.page.list) {
          this.modelConfig.formItemMap.fishId.itemRender.attrs.list =
            res.page.list
          this.fishList = res.page.list
        }
      })
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
