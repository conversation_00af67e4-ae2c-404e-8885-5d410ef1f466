<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :model-config="modelConfig"
    :features="[
      // 'delete',
      // 'insert',
      // 'update',
      // 'batch_offline',
      // 'batch_online',
      // 'offline',
      // 'online',
      'select',
    ]"
    operate-width="180"
    :show-operate="false"
  >
    <template #table_item_type="{row}">
      <color-tag :id="row.drawType">
        {{ drawConfigMap.typeList.get(row.drawType) }}
      </color-tag>
    </template>
    <template #table_item_rewardFishType="{row}">
      <tag-status
        :status="row.rewardFishType"
        active-text="是"
        inactive-text="否"
      />
    </template>
    <template #table_item_rewardType="{row}">
      <color-tag :id="row.rewardType">
        {{ baseConfigMap.rewardType.get(row.rewardType) }}
      </color-tag>
    </template>
    <template #table_item_icon="{row}">
      <img width="80" :src="row.icon" alt="" />
    </template>
  </page-table>
</template>

<script>
import { fishingAbTestResult as request } from '@/api/fishing'
import { baseConfigMap, drawConfigMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      drawConfigMap,
      baseConfigMap,
      request: request,
      gridOptions: {
        columns: [
          // { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          // { field: 'appCode', title: 'appCode' },
          { field: 'userId', title: '用户ID', width: 120 },
          { field: 'udid', title: '设备ID' },
          { field: 'abTestId', title: 'AB实验ID', width: 120 },
          { field: 'abTestGroup', title: 'AB实验组', width: 120 },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
        formConfig: {
          items: [
            {
              field: 'userId',
              title: '用户ID',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
          ],
        },
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '140px',
        },
        formData: {
          id: null,
          appCode: null,
          userId: null,
          udid: null,
          abTestId: null,
          abTestGroup: null,
        },
      },
    }
  },
}
</script>
