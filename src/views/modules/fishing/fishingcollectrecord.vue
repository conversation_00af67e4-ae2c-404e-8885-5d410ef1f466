<template>
  <page-table
    :grid-config="gridOptions"
    :request-collection="request"
    :features="['select']"
    :show-operate="false"
  >
    <template #table_item_fishId="{row}">
      <color-tag :id="row.fishId">
        {{ getFishName(row.fishId) }}
      </color-tag>
    </template>
  </page-table>
</template>

<script>
import {
  fishingCollectRecord as request,
  baseConfigRequest,
} from '@/api/fishing'
import { drawConfigMap, userCoinRecordMap } from '@/map/fishing'

export default {
  data() {
    return {
      fishList: [],
      userCoinRecordMap,
      drawConfigMap,
      request: request,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { type: 'seq', title: '序号', width: 60 },
          { field: 'userId', title: '用户ID' },
          {
            field: 'fishId',
            title: '鱼',
            slots: {
              default: 'table_item_fishId',
            },
          },
          { field: 'currentNum', title: '当前数量' },
          { field: 'totalNum', title: '总数量' },
          { field: 'createTime', title: '创建时间' },
          { field: 'updateTime', title: '更新时间' },
        ],
        formConfig: {
          items: [
            {
              field: 'userId',
              title: '用户ID',
              itemRender: {
                name: '$input',
                props: { placeholder: '请选择', clearable: true },
                defaultValue: '',
              },
            },
          ],
        },
      },
    }
  },

  activated() {
    baseConfigRequest
      .selectAll({ currentPage: 1, pageSize: 10000 })
      .then(res => {
        if (res.code === 0 && res.page && res.page.list) {
          this.fishList = res.page.list
        }
      })
  },
  methods: {
    getFishName(fishId) {
      const res = this.fishList.find(it => it.fishId === fishId)
      return res ? res.name : '-'
    },
  },
}
</script>
