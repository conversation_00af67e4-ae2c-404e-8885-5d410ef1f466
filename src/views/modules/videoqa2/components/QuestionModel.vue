<template>
  <model-in-table
    :visible.sync="showModel"
    :form-data.sync="formData"
    :select-item-request="questionStoreRequest.selectItem"
    :insert-or-update-request="questionStoreRequest.insertOrUpdate"
    :data-id="dataId"
    :form-rule="formRule"
    :form-config="formConfig"
    :model-config="modelConfig"
    @refresh="$emit('refresh')"
  >
    <el-form-item label="题目分类" prop="category">
      <map-select
        v-model="formData.category"
        placeholder="请选择"
        :list="questionMap.categoryList"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="题目标签" prop="tags">
      <el-input
        v-model="formData.tags"
        placeholder="请输入标签,标签逗号分割"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="上传封面" prop="coverUrl">
      <!--暂时先填写-->
      <el-input
        v-model="formData.coverUrl"
        placeholder="上传封面"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="输入链接" prop="audioVideoUrl">
      <el-input
        v-model="formData.audioVideoUrl"
        placeholder="请输入音频或视频链接"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="输入题目" prop="question">
      <el-input
        v-model="formData.question"
        placeholder="请输入题目"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="题目类型" prop="type">
      <map-radio
        v-model="formData.type"
        placeholder="请选择"
        :list="questionMap.typeList"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="正确答案" prop="answer1">
      <el-input
        v-model="formData.answer1"
        placeholder="请输入正确答案"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="错误答案1" prop="answer2">
      <el-input
        v-model="formData.answer2"
        placeholder="请输入错误答案1"
        style="width: 100%"
      />
    </el-form-item>
    <template v-if="showMoreAnswer">
      <el-form-item label="错误答案2" prop="answer3">
        <el-input
          v-model="formData.answer3"
          placeholder="请输入错误答案2"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="错误答案3" prop="answer4">
        <el-input
          v-model="formData.answer4"
          placeholder="请输入错误答案3"
          style="width: 100%"
        />
      </el-form-item>
    </template>
    <el-form-item label="权重" prop="weights">
      <el-input
        v-model="formData.weights"
        placeholder="请输入权重"
        style="width: 100%"
      />
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <map-radio
        v-model="formData.status"
        placeholder="请选择"
        :list="questionMap.statusList"
      />
    </el-form-item>
  </model-in-table>
</template>

<script>
import ModelInTable from '@/components/model-in-table'
import { questionStoreRequest } from '@/api/videoQa2'
import { questionMap } from '@/map/videoQa2'
import MapSelect from '@/components/map-select'
import MapRadio from '@/components/map-redio'

export default {
  name: 'QuestionModel',
  components: {
    MapRadio,
    MapSelect,
    ModelInTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dataId: {
      type: [Number, String],
    },
  },
  data() {
    return {
      questionMap,
      questionStoreRequest,
      formData: {
        id: null,
        category: 2,
        tags: '电影----',
        coverUrl:
          'https://img-blog.csdnimg.cn/20201014180756724.png?x-oss-process=image/resize,m_fixed,h_64,w_64',
        audioVideoUrl: '',
        question: '来自哪里',
        type: 1,
        answer1: '足球',
        answer2: '篮球1',
        answer3: '篮球2',
        answer4: '篮球3',
        weights: 1,
        status: 1,
      },
      formRule: {
        category: [this.ruleRequired()],
        coverUrl: [this.ruleRequired()],
        question: [this.ruleRequired()],
        type: [this.ruleRequired()],
        answer1: [this.ruleRequired()],
        answer2: [this.ruleRequired()],
        answer3: [this.ruleRequired()],
        answer4: [this.ruleRequired()],
        weights: [this.ruleRequired()],
        status: [this.ruleRequired()],
      },
      formConfig: {
        labelWidth: '85px',
      },
      modelConfig: {
        width: '450px',
      },
    }
  },
  computed: {
    showModel: {
      set(v) {
        this.$emit('update:visible', v)
      },
      get() {
        return this.visible
      },
    },
    showMoreAnswer() {
      return this.formData.type === 2
    },
  },
  watch: {
    showMoreAnswer(flag) {
      if (!flag) {
        this.formData.answer3 = ''
        this.formData.answer4 = ''
      }
    },
  },
  methods: {
    ruleRequired() {
      return { required: true, message: '不能为空', trigger: 'blur' }
    },
  },
}
</script>
