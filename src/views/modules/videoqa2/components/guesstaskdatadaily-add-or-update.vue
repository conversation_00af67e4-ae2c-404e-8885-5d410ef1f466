<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="日期" prop="day">
        <el-input v-model="dataForm.day" placeholder="日期"></el-input>
      </el-form-item>
      <el-form-item label="应用id" prop="appId">
        <el-input v-model="dataForm.appId" placeholder="应用id"></el-input>
      </el-form-item>
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
      </el-form-item>
      <el-form-item label="激励视频数字" prop="videoTimes">
        <el-input
          v-model="dataForm.videoTimes"
          placeholder="激励视频数字"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="任务类型，110：每日任务 1：签到：2：大转盘，3：红包群，4：刮刮卡，5：看视频，6：首页悬浮现金币,7:红包雨"
        prop="taskType"
      >
        <el-input
          v-model="dataForm.taskType"
          placeholder="任务类型，110：每日任务 1：签到：2：大转盘，3：红包群，4：刮刮卡，5：看视频，6：首页悬浮现金币,7:红包雨"
        ></el-input>
      </el-form-item>
      <el-form-item label="任务次数完成" prop="finishTimes">
        <el-input
          v-model="dataForm.finishTimes"
          placeholder="任务次数完成"
        ></el-input>
      </el-form-item>
      <el-form-item label="任务要求次数" prop="askTime">
        <el-input
          v-model="dataForm.askTime"
          placeholder="任务要求次数"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
      <el-form-item label="更新时间" prop="updateAt">
        <el-input v-model="dataForm.updateAt" placeholder="更新时间"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        day: '',
        appId: '',
        userId: '',
        videoTimes: '',
        taskType: '',
        finishTimes: '',
        askTime: '',
        createdAt: '',
        updateAt: '',
      },
      dataRule: {
        day: [{ required: true, message: '日期不能为空', trigger: 'blur' }],
        appId: [{ required: true, message: '应用id不能为空', trigger: 'blur' }],
        userId: [
          { required: true, message: '用户id不能为空', trigger: 'blur' },
        ],
        videoTimes: [
          { required: true, message: '激励视频数字不能为空', trigger: 'blur' },
        ],
        taskType: [
          {
            required: true,
            message:
              '任务类型，110：每日任务 1：签到：2：大转盘，3：红包群，4：刮刮卡，5：看视频，6：首页悬浮现金币,7:红包雨不能为空',
            trigger: 'blur',
          },
        ],
        finishTimes: [
          { required: true, message: '任务次数完成不能为空', trigger: 'blur' },
        ],
        askTime: [
          { required: true, message: '任务要求次数不能为空', trigger: 'blur' },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
        updateAt: [
          { required: true, message: '更新时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guesstaskdatadaily/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.day = data.guessTaskDataDaily.day
              this.dataForm.appId = data.guessTaskDataDaily.appId
              this.dataForm.userId = data.guessTaskDataDaily.userId
              this.dataForm.videoTimes = data.guessTaskDataDaily.videoTimes
              this.dataForm.taskType = data.guessTaskDataDaily.taskType
              this.dataForm.finishTimes = data.guessTaskDataDaily.finishTimes
              this.dataForm.askTime = data.guessTaskDataDaily.askTime
              this.dataForm.createdAt = data.guessTaskDataDaily.createdAt
              this.dataForm.updateAt = data.guessTaskDataDaily.updateAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guesstaskdatadaily/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              day: this.dataForm.day,
              appId: this.dataForm.appId,
              userId: this.dataForm.userId,
              videoTimes: this.dataForm.videoTimes,
              taskType: this.dataForm.taskType,
              finishTimes: this.dataForm.finishTimes,
              askTime: this.dataForm.askTime,
              createdAt: this.dataForm.createdAt,
              updateAt: this.dataForm.updateAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
