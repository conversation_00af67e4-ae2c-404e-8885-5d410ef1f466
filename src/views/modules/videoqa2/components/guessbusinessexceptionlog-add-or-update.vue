<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="提现流水号" prop="withdrawalSerialNo">
        <el-input
          v-model="dataForm.withdrawalSerialNo"
          placeholder="提现流水号"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
      </el-form-item>
      <el-form-item
        label="业务类型,1：注册相关 2：金币相关，3：提现相关,4:打款相关"
        prop="businessType"
      >
        <el-input
          v-model="dataForm.businessType"
          placeholder="业务类型,1：注册相关 2：金币相关，3：提现相关,4:打款相关"
        ></el-input>
      </el-form-item>
      <el-form-item label="异常原因" prop="reason">
        <el-input v-model="dataForm.reason" placeholder="异常原因"></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        withdrawalSerialNo: '',
        userId: '',
        businessType: '',
        reason: '',
        createdAt: '',
      },
      dataRule: {
        withdrawalSerialNo: [
          { required: true, message: '提现流水号不能为空', trigger: 'blur' },
        ],
        userId: [
          { required: true, message: '用户id不能为空', trigger: 'blur' },
        ],
        businessType: [
          {
            required: true,
            message:
              '业务类型,1：注册相关 2：金币相关，3：提现相关,4:打款相关不能为空',
            trigger: 'blur',
          },
        ],
        reason: [
          { required: true, message: '异常原因不能为空', trigger: 'blur' },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guessbusinessexceptionlog/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.withdrawalSerialNo =
                data.guessBusinessExceptionLog.withdrawalSerialNo
              this.dataForm.userId = data.guessBusinessExceptionLog.userId
              this.dataForm.businessType =
                data.guessBusinessExceptionLog.businessType
              this.dataForm.reason = data.guessBusinessExceptionLog.reason
              this.dataForm.createdAt = data.guessBusinessExceptionLog.createdAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guessbusinessexceptionlog/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              withdrawalSerialNo: this.dataForm.withdrawalSerialNo,
              userId: this.dataForm.userId,
              businessType: this.dataForm.businessType,
              reason: this.dataForm.reason,
              createdAt: this.dataForm.createdAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
