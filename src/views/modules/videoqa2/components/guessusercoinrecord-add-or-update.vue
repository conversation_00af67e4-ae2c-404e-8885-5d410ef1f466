<template>
  <el-dialog
    :title="!dataForm.coinSerialNo ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="奖励关联的金币流水编号" prop="relatedCoinSerialNo">
        <el-input
          v-model="dataForm.relatedCoinSerialNo"
          placeholder="奖励关联的金币流水编号"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户表id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户表id"></el-input>
      </el-form-item>
      <el-form-item label="应用 code" prop="appCode">
        <el-input v-model="dataForm.appCode" placeholder="应用 code"></el-input>
      </el-form-item>
      <el-form-item label="获得金币数，负数为扣除金币" prop="coins">
        <el-input
          v-model="dataForm.coins"
          placeholder="获得金币数，负数为扣除金币"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="金币操作类型：1：超过30天清零，2：新人奖励，3：首页悬浮金币，4：提现成功,5:注销账号，6：刮刮卡,7:大转盘，8:首页-推荐观看视频，9:普通签到，10：高级签到（需要接收ecpm参数）,11:累计签到，12:翻倍奖励（要求ecpm）"
        prop="operatorType"
      >
        <el-input
          v-model="dataForm.operatorType"
          placeholder="金币操作类型：1：超过30天清零，2：新人奖励，3：首页悬浮金币，4：提现成功,5:注销账号，6：刮刮卡,7:大转盘，8:首页-推荐观看视频，9:普通签到，10：高级签到（需要接收ecpm参数）,11:累计签到，12:翻倍奖励（要求ecpm）"
        ></el-input>
      </el-form-item>
      <el-form-item label="广告返回的ecpm" prop="ecpm">
        <el-input
          v-model="dataForm.ecpm"
          placeholder="广告返回的ecpm"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        coinSerialNo: 0,
        relatedCoinSerialNo: '',
        userId: '',
        appCode: '',
        coins: '',
        operatorType: '',
        ecpm: '',
        createdAt: '',
      },
      dataRule: {
        relatedCoinSerialNo: [
          {
            required: true,
            message: '奖励关联的金币流水编号不能为空',
            trigger: 'blur',
          },
        ],
        userId: [
          { required: true, message: '用户表id不能为空', trigger: 'blur' },
        ],
        appCode: [
          { required: true, message: '应用 code不能为空', trigger: 'blur' },
        ],
        coins: [
          {
            required: true,
            message: '获得金币数，负数为扣除金币不能为空',
            trigger: 'blur',
          },
        ],
        operatorType: [
          {
            required: true,
            message:
              '金币操作类型：1：超过30天清零，2：新人奖励，3：首页悬浮金币，4：提现成功,5:注销账号，6：刮刮卡,7:大转盘，8:首页-推荐观看视频，9:普通签到，10：高级签到（需要接收ecpm参数）,11:累计签到，12:翻倍奖励（要求ecpm）不能为空',
            trigger: 'blur',
          },
        ],
        ecpm: [
          {
            required: true,
            message: '广告返回的ecpm不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.coinSerialNo = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.coinSerialNo) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guessusercoinrecord/info/${this.dataForm.coinSerialNo}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.relatedCoinSerialNo =
                data.guessUserCoinRecord.relatedCoinSerialNo
              this.dataForm.userId = data.guessUserCoinRecord.userId
              this.dataForm.appCode = data.guessUserCoinRecord.appCode
              this.dataForm.coins = data.guessUserCoinRecord.coins
              this.dataForm.operatorType = data.guessUserCoinRecord.operatorType
              this.dataForm.ecpm = data.guessUserCoinRecord.ecpm
              this.dataForm.createdAt = data.guessUserCoinRecord.createdAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guessusercoinrecord/${
                !this.dataForm.coinSerialNo ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              coinSerialNo: this.dataForm.coinSerialNo || undefined,
              relatedCoinSerialNo: this.dataForm.relatedCoinSerialNo,
              userId: this.dataForm.userId,
              appCode: this.dataForm.appCode,
              coins: this.dataForm.coins,
              operatorType: this.dataForm.operatorType,
              ecpm: this.dataForm.ecpm,
              createdAt: this.dataForm.createdAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
