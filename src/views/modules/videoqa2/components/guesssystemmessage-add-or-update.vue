<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="用户id" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户id"></el-input>
      </el-form-item>
      <el-form-item
        label="消息类型：1：已提交提现申请，2：提现成功，3：刮刮卡提醒，4：账号提醒"
        prop="type"
      >
        <el-input
          v-model="dataForm.type"
          placeholder="消息类型：1：已提交提现申请，2：提现成功，3：刮刮卡提醒，4：账号提醒"
        ></el-input>
      </el-form-item>
      <el-form-item label="消息文案" prop="msg">
        <el-input v-model="dataForm.msg" placeholder="消息文案"></el-input>
      </el-form-item>
      <el-form-item label="0:未读，1：已读" prop="status">
        <el-input
          v-model="dataForm.status"
          placeholder="0:未读，1：已读"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-input
          v-model="dataForm.createdAt"
          placeholder="创建时间"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        userId: '',
        type: '',
        msg: '',
        status: '',
        createdAt: '',
      },
      dataRule: {
        userId: [
          { required: true, message: '用户id不能为空', trigger: 'blur' },
        ],
        type: [
          {
            required: true,
            message:
              '消息类型：1：已提交提现申请，2：提现成功，3：刮刮卡提醒，4：账号提醒不能为空',
            trigger: 'blur',
          },
        ],
        msg: [{ required: true, message: '消息文案不能为空', trigger: 'blur' }],
        status: [
          {
            required: true,
            message: '0:未读，1：已读不能为空',
            trigger: 'blur',
          },
        ],
        createdAt: [
          { required: true, message: '创建时间不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guesssystemmessage/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.userId = data.guessSystemMessage.userId
              this.dataForm.type = data.guessSystemMessage.type
              this.dataForm.msg = data.guessSystemMessage.msg
              this.dataForm.status = data.guessSystemMessage.status
              this.dataForm.createdAt = data.guessSystemMessage.createdAt
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/videoqa2/guesssystemmessage/${
                !this.dataForm.id ? 'save' : 'update'
              }`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              userId: this.dataForm.userId,
              type: this.dataForm.type,
              msg: this.dataForm.msg,
              status: this.dataForm.status,
              createdAt: this.dataForm.createdAt,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
