<template>
  <div class="page-table-height">
    <vxe-grid v-bind="gridOptions" ref="vxe-grid">
      <template #version_item="{ data }">
        <app-version-select
          v-model="data.version"
          :multiple="false"
          :collapse-tags="false"
          :is-show-tools="false"
          :app-id="appId"
        />
      </template>
      <template #enabled_item="{data}">
        <map-select v-model="data.enabled" :list="taskEnabledList" clearable />
      </template>
      <template #title_item="{data}">
        <vxe-input v-model="data.taskTitle" placeholder="请输入" />
      </template>
      <template #toolbar_buttons>
        <vxe-button
          status="success"
          icon="el-icon-sort-up"
          @click="handleBatchOnline"
        >
          批量上线
        </vxe-button>
        <vxe-button
          status="danger"
          icon="el-icon-sort-down"
          @click="handleBatchOffline"
        >
          批量下线
        </vxe-button>
        <vxe-button icon="el-icon-plus" @click="addTask">新增任务</vxe-button>
      </template>
      <template #table_item_task="{ row }">
        <color-tag :id="row.taskType">
          {{ taskTypeList.get(row.taskType) }}
        </color-tag>
      </template>
      <template #table_item_enabled="{ row }">
        <color-tag :id="row.enabled">
          {{ taskEnabled.get(row.enabled) }}
        </color-tag>
      </template>
      <template #table_item_award="{row}">
        <span>{{ row.lowLimit }} ~ {{ row.highLimit }}</span>
      </template>
      <template #table_item_version="{row}">
        <template v-if="row.version">
          <el-popover
            placement="top-start"
            title="全部版本"
            width="200"
            trigger="hover"
          >
            <div>
              {{ row.version | getVersionNameList(appVersionList) }}
            </div>
            <div slot="reference">
              <template v-for="(item, index) in row.version">
                <color-tag
                  v-if="index < 4"
                  :key="index"
                  :id="item"
                  style="margin-right: 5px;"
                >
                  {{ item | getVersionName(appVersionList) }}
                </color-tag>
              </template>
            </div>
          </el-popover>
        </template>
      </template>
      <template #table_item_operate="{row}">
        <vxe-button icon="el-icon-edit" content="编辑" @click="editTask(row)" />
        <vxe-button
          v-if="row.enabled === 0"
          status="success"
          icon="el-icon-sort-up"
          :content="'上线'"
          @click="toggleStatus('online', [row.id], '确定上线')"
        />
        <vxe-button
          v-else
          status="danger"
          icon="el-icon-sort-down"
          :content="'下线'"
          @click="toggleStatus('offline', [row.id], '确定下线')"
        />
      </template>
      <template #submit_item>
        <vxe-button
          type="submit"
          icon="vxe-icon--search"
          status="primary"
          content="查询"
        />
      </template>
    </vxe-grid>
    <TaskModel
      v-if="showModel"
      :visible.sync="showModel"
      :data-id="taskId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script>
import { taskConfigRequest } from '@/api/videoQa2'
import { taskEnabledList, taskTypeList } from '@/map/videoQa2'
import ColorTag from '@/components/color-tag'
import AppVersionSelect from '@/components/app-version-select'
import TaskModel from './components/TaskModel'
import config from './config'
import MapSelect from '@/components/map-select'
import { getAllAppVersion } from '@/repository/app'

export default {
  components: {
    MapSelect,
    ColorTag,
    TaskModel,
    AppVersionSelect,
  },
  data() {
    return {
      taskEnabledList,
      appId: config.appId,
      showModel: false,
      taskTypeList,
      taskEnabled: taskEnabledList,
      taskId: null,
      gridOptions: {
        maxHeight: 'auto',
        exportConfig: {},
        pagerConfig: {
          // pageSize: 10,
        },
        formConfig: {
          items: [
            {
              field: 'version',
              title: '版本',
              itemRender: { defaultValue: '' },
              slots: { default: 'version_item' },
            },
            {
              field: 'enabled',
              title: '是否启用',
              itemRender: { defaultValue: '' },
              slots: { default: 'enabled_item' },
            },
            {
              field: 'taskTitle',
              title: '名称',
              itemRender: { defaultValue: '' },
              slots: { default: 'title_item' },
            },
            { itemRender: {}, slots: { default: 'submit_item' } },
          ],
        },
        // 不配置的话，必须要写上，不然全局配置不起作用
        toolbarConfig: {
          slots: {
            buttons: 'toolbar_buttons',
          },
        },
        proxyConfig: {
          form: true, // 启用表单代理
          ajax: {
            query: ({ page, form }) => {
              return taskConfigRequest.selectAll({
                currentPage: page.currentPage,
                pageSize: page.pageSize,
                ...form,
              })
            },
          },
        },
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', width: 50, title: 'ID' },
          {
            field: 'enabled',
            title: '状态',
            width: 80,
            align: 'center',
            slots: { default: 'table_item_enabled' },
          },
          { field: 'taskTitle', title: '任务名称' },
          {
            field: 'taskType',
            title: '任务类型',
            width: 120,
            slots: { default: 'table_item_task' },
          },
          { field: 'rewardShow', title: '展示奖励' },
          {
            title: '实际奖励',
            slots: { default: 'table_item_award' },
          },
          { field: 'buttonTitle', title: '按钮文案' },
          { field: 'jumpUrl', title: '跳转页面' },
          { field: 'sortValue', title: '权重', width: 120 },
          {
            field: 'version',
            title: '版本',
            slots: { default: 'table_item_version' },
          },
          {
            title: '操作',
            slots: { default: 'table_item_operate' },
            width: 180,
          },
        ],
      },
      appVersionList: [],
    }
  },
  activated() {
    this.handleRefresh()

    getAllAppVersion(config.appId).then(list => {
      this.appVersionList = list
    })
  },
  methods: {
    addTask() {
      this.taskId = null
      this.showModel = true
    },
    editTask(row) {
      this.taskId = row.id
      this.showModel = true
    },
    handleRefresh() {
      this.reloadQuery()
    },

    handleBatchOnline() {
      this.toggleStatus(
        'online',
        this.getCheckboxRecords().map(it => it.id),
        '确定批量上线?'
      )
    },
    handleBatchOffline() {
      this.toggleStatus(
        'offline',
        this.getCheckboxRecords().map(it => it.id),
        '确定批量下线?'
      )
    },
    toggleStatus(type, ids, confirmMsg = '确定批量上线?') {
      if (!ids || !ids.length) {
        return this.$vxeMessage({
          status: 'warning',
          message: '请选选择',
        })
      }

      this.$confirm(confirmMsg, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let request = null

        if (type === 'online') {
          request = taskConfigRequest.batchOnline
        } else if (type === 'offline') {
          request = taskConfigRequest.batchOffline
        } else {
          throw 'type 值不对'
        }

        request(ids).then(() => {
          this.$vxeMessage({
            status: 'success',
            message: '更新成功!',
          })
          this.reloadQuery()
        })
      })
    },
    reloadQuery() {
      this.$refs['vxe-grid'].commitProxy('query')
    },
    getCheckboxRecords() {
      return this.$refs['vxe-grid'].getCheckboxRecords()
    },
  },
}
</script>
