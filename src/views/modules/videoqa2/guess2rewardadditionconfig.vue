<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="rewardAdditionConfig"
      :model-config="modelConfig"
      :features="['insert', 'delete', 'batch_delete', 'update']"
    >
      <template #table_item_enabled="{row}">
        <color-tag :id="row.enabled">
          {{ rewardAdditionConfigMap.enabledList.get(row.enabled) }}
        </color-tag>
      </template>
      <template #model>
        <el-form-item label="开始" prop="lowLimit">
          <el-input
            v-model="modelConfig.formData.lowLimit"
            placeholder="开始"
          />
        </el-form-item>
        <el-form-item label="结束" prop="highLimit">
          <el-input
            v-model="modelConfig.formData.highLimit"
            placeholder="结束"
          />
        </el-form-item>
        <el-form-item label="加成比例" prop="addition">
          <el-input
            v-model="modelConfig.formData.addition"
            placeholder="加成比例"
          />
        </el-form-item>
        <el-form-item label="排序值" prop="sortValue">
          <el-input
            v-model="modelConfig.formData.sortValue"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <map-radio
            v-model="modelConfig.formData.enabled"
            :list="rewardAdditionConfigMap.enabledList"
            placeholder="是否启用"
          />
        </el-form-item>
      </template>
    </page-table>
  </div>
</template>

<script>
import { rewardAdditionConfigRequest } from '@/api/videoQa2'
import { rewardAdditionConfigMap } from '@/map/videoQa2'
import config from './config'

export default {
  data() {
    return {
      config,
      rewardAdditionConfigMap,
      rewardAdditionConfig: rewardAdditionConfigRequest,
      gridOptions: {
        columns: [
          { type: 'checkbox', width: 35 },
          { field: 'id', width: 50, title: 'ID' },
          { field: 'lowLimit', title: '开始' },
          { field: 'highLimit', title: '结束' },
          { field: 'addition', title: '加成比例' },
          { field: 'sortValue', title: '排序值' },
          {
            field: 'enabled',
            title: '是否启用',
            slots: { default: 'table_item_enabled' },
          },
          { field: 'createdAt', title: '创建时间' },
          { field: 'updatedAt', title: '更新时间' },
          { field: 'createdBy', title: '创建者' },
          { field: 'updatedBy', title: '更新者' },
        ],
      },
      modelConfig: {
        modelConfig: {
          width: '500px',
        },
        formConfig: {
          labelWidth: '80px',
        },
        formData: {
          id: null,
          appId: config.appCode, // 它其实是一个 code
          lowLimit: '',
          highLimit: '',
          addition: '',
          sortValue: '',
          enabled: 1,
        },
        formRule: {
          lowLimit: [{ required: true, message: '不能为空', trigger: 'blur' }],
          highLimit: [{ required: true, message: '不能为空', trigger: 'blur' }],
          addition: [{ required: true, message: '不能为空', trigger: 'blur' }],
          sortValue: [{ required: true, message: '不能为空', trigger: 'blur' }],
          enabled: [{ required: true, message: '不能为空', trigger: 'blur' }],
        },
      },
    }
  },
}
</script>
