<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="130px"
    >
      <el-form-item label="应用" prop="appCode">
        <el-input v-model="appName" disabled />
      </el-form-item>
      <el-form-item label="风控规则" prop="ruleType">
        <el-select v-model="dataForm.ruleType" :disabled="!!dataForm.id">
          <el-option
            v-for="[key, label] in packetRuleType"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
      <template
        v-if="
          dataForm.ruleType === 5 ||
            dataForm.ruleType === 6 ||
            dataForm.ruleType === 7 ||
            dataForm.ruleType === 9 ||
            dataForm.ruleType === 10 ||
            dataForm.ruleType === 12 ||
            dataForm.ruleType === 13 ||
            dataForm.ruleType === 15 ||
            dataForm.ruleType === 16 ||
            dataForm.ruleType === 17 ||
            dataForm.ruleType === 34 ||
            dataForm.ruleType === 35 ||
            dataForm.ruleType === 30 ||
            dataForm.ruleType === 25 ||
            dataForm.ruleType === 26 ||
            dataForm.ruleType === 36
        "
      >
        <el-form-item :label="minLabel[dataForm.ruleType]" prop="highValue">
          <div style="display: flex;">
            <el-input
              type="number"
              v-model="dataForm.lowValue"
              style="width:120px"
              placeholder="最小值"
              clearable
            />
            <span style="padding: 0 10px">-</span>
            <el-input
              type="number"
              v-model="dataForm.highValue"
              style="width: 120px"
              placeholder="最大值"
              clearable
            />
          </div>
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item :label="minLabel[dataForm.ruleType]" prop="lowValue">
          <el-input
            v-model="dataForm.lowValue"
            type="number"
            clearable
            placeholder="数值下限"
          />
        </el-form-item>
      </template>
      <el-form-item label="启用状态" prop="status">
        <el-select v-model="dataForm.status">
          <el-option
            v-for="[key, label] in packetRuleStatus"
            :key="key"
            :value="key"
            :label="label"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { packetRuleType, packetRuleStatus } from '@/map/common'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        appCode: '',
        ruleType: '',
        ruleName: '',
        lowValue: 0.3,
        highValue: 0.3,
        status: 0,
      },
      dataRule: {
        appCode: [
          {
            required: true,
            message: 'app表 appCode不能为空',
            trigger: 'blur',
          },
        ],
        ruleType: [
          {
            required: true,
            message:
              '1:自动审核发放金额限制，2：单个用户每日观看视频数上限，3：单个用户每日获得金币上限，4：ecmp固定值，5：人气值增加，6：人气值减少不能为空',
            trigger: 'blur',
          },
        ],
        ruleName: [
          {
            required: true,
            message: '风控规则名称不能为空',
            trigger: 'blur',
          },
        ],
        lowValue: [
          {
            required: true,
            message: '数值下限不能为空',
            trigger: 'blur',
          },
        ],
        highValue: [
          {
            required: true,
            message: '数值上限不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '启用状态：0 未开启，1：开启不能为空',
            trigger: 'blur',
          },
        ],
      },
      appName: '当地天气',
      minLabel: {
        1: '发放金额限制(元)',
        2: '观看视频上限',
        3: '获得金币上限',
        4: 'ecmp固定值',
        5: '人气值',
        6: '人气值',
      },
      maxLabel: {},
      packetRuleType,
      packetRuleStatus,
    }
  },
  watch: {
    'dataForm.ruleType'(rule) {
      this.dataForm.ruleName = this.packetRuleType.get(rule)
    },
    'dataForm.lowValue'(n) {
      if (
        this.dataForm.ruleType !== 5 &&
        this.dataForm.ruleType !== 6 &&
        this.dataForm.ruleType !== 7 &&
        this.dataForm.ruleType !== 9 &&
        this.dataForm.ruleType !== 10 &&
        this.dataForm.ruleType !== 12 &&
        this.dataForm.ruleType !== 13 &&
        this.dataForm.ruleType !== 15 &&
        this.dataForm.ruleType !== 16 &&
        this.dataForm.ruleType !== 34 &&
        this.dataForm.ruleType !== 35 &&
        this.dataForm.ruleType !== 36 &&
        this.dataForm.ruleType !== 30 &&
        this.dataForm.ruleType !== 25 &&
        this.dataForm.ruleType !== 26 &&
        this.dataForm.ruleType !== 17
      ) {
        this.dataForm.highValue = n
      }
    },
  },
  methods: {
    init(id) {
      console.log(this.$store.state.ad.appList)
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/weather/applimitrule/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.appCode = data.appLimitRule.appCode
              this.dataForm.ruleType = data.appLimitRule.ruleType
              this.dataForm.ruleName = data.appLimitRule.ruleName
              this.dataForm.lowValue = data.appLimitRule.lowValue
              this.dataForm.highValue = data.appLimitRule.highValue
              this.dataForm.status = data.appLimitRule.status
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (
            Number(this.dataForm.highValue) < Number(this.dataForm.lowValue)
          ) {
            return this.$message.error('最大值不能小于最小值')
          }

          this.$http({
            url: this.$http.adornUrl(
              `/weather/applimitrule/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              appCode: this.dataForm.appCode,
              ruleType: this.dataForm.ruleType,
              ruleName: this.dataForm.ruleName,
              lowValue: this.dataForm.lowValue,
              highValue: this.dataForm.highValue,
              status: this.dataForm.status,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
