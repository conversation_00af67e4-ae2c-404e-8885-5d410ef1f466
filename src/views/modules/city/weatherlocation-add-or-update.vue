<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
    <el-form-item label="位置代码" prop="locationCode">
      <el-input v-model="dataForm.locationCode" placeholder="位置代码"></el-input>
    </el-form-item>
    <el-form-item label="位置类型：1省2市3区4镇5街道" prop="locationType">
      <el-input v-model="dataForm.locationType" placeholder="位置类型：1省2市3区4镇5街道"></el-input>
    </el-form-item>
    <el-form-item label="位置简称" prop="name">
      <el-input v-model="dataForm.name" placeholder="位置简称"></el-input>
    </el-form-item>
    <el-form-item label="位置拼音" prop="pinyin">
      <el-input v-model="dataForm.pinyin" placeholder="位置拼音"></el-input>
    </el-form-item>
    <el-form-item label="省份" prop="province">
      <el-input v-model="dataForm.province" placeholder="省份"></el-input>
    </el-form-item>
    <el-form-item label="城市" prop="city">
      <el-input v-model="dataForm.city" placeholder="城市"></el-input>
    </el-form-item>
    <el-form-item label="区镇县" prop="district">
      <el-input v-model="dataForm.district" placeholder="区镇县"></el-input>
    </el-form-item>
    <el-form-item label="街道" prop="street">
      <el-input v-model="dataForm.street" placeholder="街道"></el-input>
    </el-form-item>
    <el-form-item label="纬度" prop="latitude">
      <el-input v-model="dataForm.latitude" placeholder="纬度"></el-input>
    </el-form-item>
    <el-form-item label="经度" prop="longitude">
      <el-input v-model="dataForm.longitude" placeholder="经度"></el-input>
    </el-form-item>
    <el-form-item label="父ID" prop="parentId">
      <el-input v-model="dataForm.parentId" placeholder="父ID"></el-input>
    </el-form-item>
    <el-form-item label="创建时间" prop="createdAt">
      <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updatedAt">
      <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          locationCode: '',
          locationType: '',
          name: '',
          pinyin: '',
          province: '',
          city: '',
          district: '',
          street: '',
          latitude: '',
          longitude: '',
          parentId: '',
          createdAt: '',
          updatedAt: ''
        },
        dataRule: {
          locationCode: [
            { required: true, message: '位置代码不能为空', trigger: 'blur' }
          ],
          locationType: [
            { required: true, message: '位置类型：1省2市3区4镇5街道不能为空', trigger: 'blur' }
          ],
          name: [
            { required: true, message: '位置简称不能为空', trigger: 'blur' }
          ],
          pinyin: [
            { required: true, message: '位置拼音不能为空', trigger: 'blur' }
          ],
          province: [
            { required: true, message: '省份不能为空', trigger: 'blur' }
          ],
          city: [
            { required: true, message: '城市不能为空', trigger: 'blur' }
          ],
          district: [
            { required: true, message: '区镇县不能为空', trigger: 'blur' }
          ],
          street: [
            { required: true, message: '街道不能为空', trigger: 'blur' }
          ],
          latitude: [
            { required: true, message: '纬度不能为空', trigger: 'blur' }
          ],
          longitude: [
            { required: true, message: '经度不能为空', trigger: 'blur' }
          ],
          parentId: [
            { required: true, message: '父ID不能为空', trigger: 'blur' }
          ],
          createdAt: [
            { required: true, message: '创建时间不能为空', trigger: 'blur' }
          ],
          updatedAt: [
            { required: true, message: '更新时间不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/city/weatherlocation/info/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm.locationCode = data.weatherLocation.locationCode
                this.dataForm.locationType = data.weatherLocation.locationType
                this.dataForm.name = data.weatherLocation.name
                this.dataForm.pinyin = data.weatherLocation.pinyin
                this.dataForm.province = data.weatherLocation.province
                this.dataForm.city = data.weatherLocation.city
                this.dataForm.district = data.weatherLocation.district
                this.dataForm.street = data.weatherLocation.street
                this.dataForm.latitude = data.weatherLocation.latitude
                this.dataForm.longitude = data.weatherLocation.longitude
                this.dataForm.parentId = data.weatherLocation.parentId
                this.dataForm.createdAt = data.weatherLocation.createdAt
                this.dataForm.updatedAt = data.weatherLocation.updatedAt
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/city/weatherlocation/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData({
                'id': this.dataForm.id || undefined,
                'locationCode': this.dataForm.locationCode,
                'locationType': this.dataForm.locationType,
                'name': this.dataForm.name,
                'pinyin': this.dataForm.pinyin,
                'province': this.dataForm.province,
                'city': this.dataForm.city,
                'district': this.dataForm.district,
                'street': this.dataForm.street,
                'latitude': this.dataForm.latitude,
                'longitude': this.dataForm.longitude,
                'parentId': this.dataForm.parentId,
                'createdAt': this.dataForm.createdAt,
                'updatedAt': this.dataForm.updatedAt
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
