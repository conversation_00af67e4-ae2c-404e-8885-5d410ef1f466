<template>
  <el-dialog
    class="adaptive_dialog"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="950px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="_dataFormSubmit()"
      label-width="120px"
    >
      <el-form-item label="策略名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="策略名称" />
      </el-form-item>
      <el-form-item label="分群策略" prop="userFilterId">
        <el-input
          v-model="dataForm.userFilterId"
          placeholder="分群策略（用户）"
        />
      </el-form-item>
      <el-form-item label="屏蔽规则" prop="blockRuleId">
        <el-select v-model="dataForm.blockRuleId">
          <el-option
            v-for="{ id, name } in rules"
            :key="id"
            :value="id"
            :label="name"
          />
        </el-select>
        <div class="rule-detail">
          <el-collapse v-model="activeName" accordion>
            <el-collapse-item title="规则详情" name="1">
              <rule-table v-bind="jsonRule" disabled hide-header hide-operate />
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-form-item>
      <el-form-item label="排序" prop="sortValue">
        <el-input v-model="dataForm.sortValue" placeholder="排序" />
      </el-form-item>
      <el-form-item label="关联广告位ID" prop="apId">
        <el-input v-model="dataForm.apId" placeholder="关联广告位ID" disabled />
      </el-form-item>
      <el-form-item label-width="0">
        <connect-code-position
          v-if="dataForm.apId && dataForm.id"
          ref="connectCodePosition"
          type="strategy"
          :ap-id="dataForm.apId"
          :as-id="dataForm.id"
          @bind-handle-sort="_handleSort"
          has-sort
          un-bind-has-sort
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="_dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import connectCodePosition from '@/components/connect-code-position'
import RuleTable from '@/components/rule-table'

export default {
  components: {
    connectCodePosition,
    RuleTable,
  },
  props: {
    rules: {
      type: Array,
    },
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        userFilterId: '',
        blockRuleId: '',
        sortValue: 0,
        apId: null,
        appId: null,
      },
      dataRule: {
        name: [
          {
            required: true,
            message: '策略名称不能为空',
            trigger: 'blur',
          },
        ],
        userFilterId: [
          {
            required: false,
            message: '分群策略（用户）不能为空',
            trigger: 'blur',
          },
        ],
        blockRuleId: [
          {
            required: false,
            message: '屏蔽规则不能为空',
            trigger: 'blur',
          },
        ],
        // sortValue: [
        //   {
        //     required: true,
        //     message: '排序不能为空',
        //     trigger: 'blur',
        //   },
        // ],
        apId: [
          {
            required: true,
            message: '关联广告位ID不能为空',
            trigger: 'blur',
          },
        ],
        appId: [
          {
            required: true,
            message: '应用ID不能为空',
            trigger: 'blur',
          },
        ],
      },
      activeName: '1',
      jsonRule: {
        ruleList: [],
        appVersionList: new Map([]),
        channelList: new Map([]),
        appId: '',
      },
    }
  },
  watch: {
    'dataForm.blockRuleId': {
      immediate: true,
      handler(ruleId) {
        if (this.rules) {
          const res = this.rules.find(it => ruleId === it.id)
          const rules = res && res.rules ? res.rules : null

          if (rules) {
            try {
              const { appId, list } = JSON.parse(rules)
              this.jsonRule.ruleList = list
              this.jsonRule.appId = appId
              this._getAppVersionList(appId)
              this._getChannelList(appId)
            } catch (e) {
              console.log(e)
            }
          }
        }
      },
    },
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/ad/adstrategy/info/${this.dataForm.id}`),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.name = data.adStrategy.name
              this.dataForm.userFilterId = data.adStrategy.userFilterId
              this.dataForm.blockRuleId = data.adStrategy.blockRuleId
              this.dataForm.sortValue = data.adStrategy.sortValue
              this.dataForm.apId = data.adStrategy.apId
              this.dataForm.appId = data.adStrategy.appId

              setTimeout(() => this.$refs.connectCodePosition.init())
            }
          })
        } else {
          this.dataForm.appId = this.$store.state.ad.appId
          setTimeout(() => this.$refs.connectCodePosition.init())
        }
      })
    },
    // 表单提交
    _dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/ad/adstrategy/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              name: this.dataForm.name,
              userFilterId: this.dataForm.userFilterId,
              blockRuleId: this.dataForm.blockRuleId,
              sortValue: this.dataForm.sortValue,
              apId: this.dataForm.apId,
              appId: this.dataForm.appId,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.success('操作成功')
              this.visible = false
              this.$emit('refreshDataList')
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    _getAppVersionList(app_id) {
      const params = {
        page: 1,
        limit: 100,
        app_id,
      }

      this.$store
        .dispatch('api/app/getVersionList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.jsonRule.appVersionList = new Map(
              // data.page.list.map(it => [it.id, it.versionName])
              data.page.list.map(it => [it.versionCode, it.versionName])
            )
          } else {
            this.jsonRule.appVersionList = new Map([])
          }
        })
    },
    _getChannelList(app_id) {
      this.$store
        .dispatch('api/app/getChannelList', {
          page: 1,
          limit: 100,
          app_id,
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.jsonRule.channelList = new Map(
              // data.page.list.map(it => [it.id, it.channelName])
              data.page.list.map(it => [it.channelCode, it.channelName])
            )
          } else {
            this.jsonRule.channelList = new Map([])
          }
        })
    },
    _handleSort(data) {
      this.$http({
        url: this.$http.adornUrl(`/ad/adstrategyslot/update`),
        method: 'post',
        data: this.$http.adornData({
          id: data.strategySlotId, // '策略下代码位管理'模块的自增ID
          // strategyId: this.dataForm.id, // 策略ID
          // slotId: data.asId, // 广告代码位ID
          sortValue: data.sortValue, // 排序值
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message.success('操作成功')
        } else {
          this.$message.error(data.msg)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.rule-detail {
  margin-top: 20px;
  ::v-deep {
    .el-collapse-item__wrap {
      border-bottom: none;
    }
  }
}
</style>
