<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <el-form-item label="应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          v-if="isAuth('ad:adslot:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('ad:adslot:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      class="adapter-height"
      :max-height="tableHeight"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      />
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="自增ID"
      />
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="代码位名称"
      />
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="代码位类型"
      >
        <template slot-scope="{ row }">
          <span>{{ codeType.get(row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="adType"
        header-align="center"
        align="center"
        label="广告类型"
      >
        <template slot-scope="{ row }">
          <span>{{ adType.get(row.adType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unionType"
        header-align="center"
        align="center"
        label="联盟类型"
      >
        <template slot-scope="{ row }">
          <span>{{ allianceType.get(row.unionType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unionCode"
        header-align="center"
        align="center"
        label="代码位ID"
      />
      <el-table-column
        prop="ecpm"
        header-align="center"
        align="center"
        label="eCPM(¥)"
      />
      <el-table-column
        prop="exclusive"
        header-align="center"
        align="center"
        label="是否排它"
      >
        <template slot-scope="{ row }">
          <span>{{ isExclusion.get(row.exclusive) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="代码位状态"
      >
        <template slot-scope="{ row }">
          <span>{{ codeSwitchStatus.get(row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="apId"
        header-align="center"
        align="center"
        label="关联广告位ID"
      />
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './adslot-add-or-update'
import { mixinElTableAdapterHeight } from '@/mixins'

import {
  codeType,
  isExclusion,
  codeSwitchStatus,
  adSwitchStatus,
  allianceType,
  adType,
} from '@/map/common'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      dataForm: {
        key: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      codeType,
      isExclusion,
      codeSwitchStatus,
      adSwitchStatus,
      allianceType,
      adType,
    }
  },
  watch: {
    '$store.state.ad.appId'() {
      this.getDataList()
    },
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        key: this.dataForm.key,
      }
      this.$store.dispatch('api/ad/getAdSlotList', params).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      if (!this.$store.state.ad.appId && !id)
        return this.$message.error('请选选择应用')

      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$store.dispatch('api/ad/deleteAdSlot', ids).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('操作成功')
            this.getDataList()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
  },
}
</script>
