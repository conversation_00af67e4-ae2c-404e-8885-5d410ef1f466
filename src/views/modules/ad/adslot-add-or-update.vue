<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item label="联盟类型" prop="unionType">
        <el-select v-model="dataForm.unionType" placeholder="联盟类型">
          <el-option
            v-for="[key, label] in allianceType"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="广告类型" prop="adType">
        <el-select v-model="dataForm.adType" placeholder="广告类型">
          <el-option
            v-for="[key, label] in adType"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代码位名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="代码位名称"></el-input>
      </el-form-item>
      <el-form-item label="代码位ID" prop="unionCode">
        <el-input
          v-model="dataForm.unionCode"
          placeholder="代码位ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="eCPM(¥)" prop="ecpm">
        <el-input-number v-model="dataForm.ecpm" label="eCPM(¥)" />
      </el-form-item>
      <el-form-item label="代码位类型" prop="type">
        <el-select v-model="dataForm.type" disabled placeholder="代码位类型">
          <el-option
            v-for="[key, label] in codeType"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否排它" prop="exclusive">
        <el-select v-model="dataForm.exclusive" disabled placeholder="是否排它">
          <el-option
            v-for="[key, label] in isExclusion"
            :key="key"
            :label="label"
            :value="key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="代码位状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio
            v-for="[key, label] in codeSwitchStatus"
            :key="key"
            :label="key"
          >
            {{ label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  allianceType,
  codeType,
  adType,
  isExclusion,
  codeSwitchStatus,
} from '@/map/common'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        type: 1,
        adType: '',
        unionType: '',
        unionCode: '',
        ecpm: 0.01,
        exclusive: 1,
        status: 0,
        appId: null,
      },
      dataRule: {
        name: [
          {
            required: true,
            message: '代码位名称不能为空',
            trigger: 'blur',
          },
        ],
        type: [
          {
            required: true,
            message: '代码位类型：1普通 2竞价不能为空',
            trigger: 'blur',
          },
        ],
        adType: [
          {
            required: true,
            message:
              '广告类型：0自渲染/1信息流/2Banner/3开屏/4插屏广告/5激励视频/6全屏视频广告/7draw信息流不能为空',
            trigger: 'blur',
          },
        ],
        unionType: [
          {
            required: true,
            message: '联盟类型不能为空',
            trigger: 'blur',
          },
        ],
        unionCode: [
          {
            required: true,
            message: '代码位ID不能为空',
            trigger: 'blur',
          },
        ],
        ecpm: [
          {
            required: true,
            message: 'eCPM(¥)不能为空',
            trigger: 'blur',
          },
        ],
        exclusive: [
          {
            required: true,
            message: '是否排它：1排 2共享不能为空',
            trigger: 'blur',
          },
        ],
        appId: [
          {
            required: true,
            message: '应用ID不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '代码位状态：1开 0关不能为空',
            trigger: 'blur',
          },
        ],
      },
      allianceType,
      codeType,
      adType,
      isExclusion,
      codeSwitchStatus,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$store
            .dispatch('api/ad/getAdSlotInfo', this.dataForm.id)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.name = data.adSlot.name
                this.dataForm.type = data.adSlot.type
                this.dataForm.adType = data.adSlot.adType
                this.dataForm.unionType = data.adSlot.unionType
                this.dataForm.unionCode = data.adSlot.unionCode
                this.dataForm.ecpm = data.adSlot.ecpm
                this.dataForm.exclusive = data.adSlot.exclusive
                this.dataForm.status = data.adSlot.status
                this.dataForm.appId = data.adSlot.appId
              }
            })
        } else {
          this.dataForm.appId = this.$store.state.ad.appId
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = {
            id: this.dataForm.id || undefined,
            name: this.dataForm.name,
            type: this.dataForm.type,
            adType: this.dataForm.adType,
            unionType: this.dataForm.unionType,
            unionCode: this.dataForm.unionCode,
            ecpm: this.dataForm.ecpm,
            exclusive: this.dataForm.exclusive,
            status: this.dataForm.status,
            appId: this.dataForm.appId,
          }
          this.$store
            .dispatch('api/ad/addOrUpdateAdSlot', data)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message.success('操作成功')
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            })
        }
      })
    },
  },
}
</script>
