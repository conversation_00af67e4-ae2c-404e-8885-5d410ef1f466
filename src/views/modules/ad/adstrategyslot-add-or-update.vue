<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-form-item label="广告策略ID" prop="strategyId">
        <el-input v-model="dataForm.strategyId" placeholder="广告策略ID" />
      </el-form-item>
      <el-form-item label="广告代码位ID" prop="slotId">
        <el-input v-model="dataForm.slotId" placeholder="广告代码位ID" />
      </el-form-item>
      <el-form-item label="排序值" prop="sortValue">
        <el-input v-model="dataForm.sortValue" placeholder="排序值" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        strategyId: '',
        slotId: '',
        sortValue: 0,
      },
      dataRule: {
        strategyId: [
          { required: true, message: '广告策略ID不能为空', trigger: 'blur' },
        ],
        slotId: [
          { required: true, message: '广告代码位ID不能为空', trigger: 'blur' },
        ],
        sortValue: [
          { required: true, message: '排序值不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(
              `/ad/adstrategyslot/info/${this.dataForm.id}`
            ),
            method: 'get',
            params: this.$http.adornParams(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm.strategyId = data.adStrategySlot.strategyId
              this.dataForm.slotId = data.adStrategySlot.slotId
              this.dataForm.sortValue = data.adStrategySlot.sortValue
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(
              `/ad/adstrategyslot/${!this.dataForm.id ? 'save' : 'update'}`
            ),
            method: 'post',
            data: this.$http.adornData({
              id: this.dataForm.id || undefined,
              strategyId: this.dataForm.strategyId,
              slotId: this.dataForm.slotId,
              sortValue: this.dataForm.sortValue,
            }),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.success('操作成功')
              this.visible = false
              this.$emit('refreshDataList')
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
  },
}
</script>
