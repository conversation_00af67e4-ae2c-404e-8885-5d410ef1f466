<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!--<el-form-item>-->
      <!--  <el-input-->
      <!--    v-model="dataForm.key"-->
      <!--    placeholder="参数名"-->
      <!--    clearable-->
      <!--  ></el-input>-->
      <!--</el-form-item>-->
      <el-form-item label="应用">
        <app-select
          @change="currentChangeHandle(1)"
          @init-app-id="currentChangeHandle(1)"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          v-if="isAuth('ad:adposition:save')"
          type="primary"
          @click="addOrUpdateHandle()"
        >
          新增
        </el-button>
        <el-button
          v-if="isAuth('ad:adposition:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      />
      <el-table-column
        prop="id"
        header-align="center"
        align="center"
        label="自增ID"
      />
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="广告位名称"
      />
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="广告位标识"
      />
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="广告类型"
      >
        <template slot-scope="{ row }">
          <el-tag type="success">{{ adType.get(row.type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="kind"
        header-align="center"
        align="center"
        label="广告种类"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.kind || row.kind === 0" type="success">
            {{ adKind.get(row.kind) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="maxTimeout"
        header-align="center"
        align="center"
        label="广告位超时"
      />
      <el-table-column
        prop="itemTimeout"
        header-align="center"
        align="center"
        label="广告源超时"
      />
      <el-table-column
        prop="appId"
        header-align="center"
        align="center"
        label="应用ID"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="appList && appList.length">
            {{ row.appId | getListLabel(appList, 'id', 'name') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="广告状态"
      >
        <template slot-scope="{ row }">
          <el-tag>{{ adSwitchStatus.get(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createdAt"
        header-align="center"
        align="center"
        label="创建时间"
      />
      <el-table-column
        prop="updatedAt"
        header-align="center"
        align="center"
        label="更新时间"
      />
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <router-link :to="'/ad-adstrategy?ap_id=' + scope.row.id">
            策略管理
          </router-link>
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            修改
          </el-button>
          <!--<el-button-->
          <!--  type="text"-->
          <!--  size="small"-->
          <!--  @click="deleteHandle(scope.row.id)"-->
          <!--&gt;-->
          <!--  删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100, 1000]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    />
  </div>
</template>

<script>
import AppSelect from '@/components/app-select'
import AddOrUpdate from './adposition-add-or-update'
import { adSwitchStatus, adType, adKind } from '@/map/common'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        kind: '',
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 100,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      adSwitchStatus,
      adType,
      adKind,
      appVersionList: [],
      appList: [],
    }
  },
  watch: {
    '$store.state.ad.appId'() {
      this.getDataList()
    },
  },
  components: {
    AddOrUpdate,
    AppSelect,
  },
  activated() {
    // this.getDataList()
    this.getAppList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        key: this.dataForm.key,
        kind: this.dataForm.kind,
      }
      this.$store
        .dispatch('api/ad/getAdPositionList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
            this.$message.error(data.msg || '服务错误')
          }
        })
        .finally(() => {
          this.dataListLoading = false
        })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      if (!this.$store.state.ad.appId && !id)
        return this.$message.error('请选选择应用')

      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id
        ? [id]
        : this.dataListSelections.map(item => {
            return item.id
          })
      this.$confirm(
        `【危险提示】确定对[id=${ids.join(',')}]进行[${
          id ? '删除' : '批量删除'
        }]操作吗? 此操作不可恢复！`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        this.$store
          .dispatch('api/ad/deleteAdPosition', ids)
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                },
              })
            } else {
              this.$message.error(data.msg)
            }
          })
      })
    },
    getAppList() {
      this.$store
        .dispatch('api/app/getAppList', {
          page: 1,
          limit: 10000,
        })
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.appList = data.page.list
          }
        })
    },
  },
}
</script>
