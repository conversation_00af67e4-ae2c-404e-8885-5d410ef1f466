<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    :class="[{ adaptive_dialog: !!dataForm.id }]"
    width="950px"
    append-to-body
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item label="广告位名称" prop="name">
        <el-input
          v-model.trim="dataForm.name"
          placeholder="广告位名称"
          :disabled="!!dataForm.id"
        />
      </el-form-item>
      <el-form-item label="广告位标识" prop="code">
        <el-input
          v-model.trim="dataForm.code"
          placeholder="广告位标识"
          :disabled="!!dataForm.id"
        />
      </el-form-item>
      <el-form-item label="广告类型" prop="type">
        <el-select
          v-model="dataForm.type"
          placeholder="请选择"
          :disabled="!!dataForm.id"
        >
          <el-option
            v-for="[key, value] in adType"
            :key="key"
            :value="key"
            :label="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="广告种类" prop="kind">
        <el-select
          v-model="dataForm.kind"
          placeholder="请选择"
          :disabled="!!dataForm.id"
        >
          <el-option
            v-for="[key, value] in adKind"
            :key="key"
            :value="key"
            :label="value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="广告状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio
            v-for="[key, label] in adSwitchStatus"
            :key="key"
            :label="key"
          >
            {{ label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="广告位超时(秒)" prop="maxTimeout">
        <el-input v-model.trim="dataForm.maxTimeout" placeholder="广告位超时" />
      </el-form-item>
      <el-form-item label="广告源超时(秒)" prop="itemTimeout">
        <el-input
          v-model.trim="dataForm.itemTimeout"
          placeholder="广告源超时"
        />
      </el-form-item>
      <el-form-item v-if="dataForm.id" label-width="0">
        <connect-code-position
          :ap-id="dataForm.id"
          :ad-type="dataForm.type"
          ref="connectCodePosition"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { adType, adKind, adSwitchStatus } from '@/map/common'
import connectCodePosition from '@/components/connect-code-position'

export default {
  components: {
    connectCodePosition,
  },
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        code: '',
        type: '',
        kind: 0,
        maxTimeout: 10,
        itemTimeout: 3,
        appId: null,
        status: 0,
      },
      dataRule: {
        name: [
          {
            required: true,
            message: '广告位名称不能为空',
            trigger: 'blur',
          },
        ],
        // code: [
        //   { required: true, message: '广告位标识不能为空', trigger: 'blur' },
        // ],
        type: [
          {
            required: true,
            message: '广告类型不能为空',
            trigger: 'blur',
          },
        ],
        kind: [
          {
            required: true,
            message: '广告种类不能为空',
            trigger: 'blur',
          },
        ],
        maxTimeout: [
          {
            required: true,
            message: '广告位超时不能为空',
            trigger: 'blur',
          },
        ],
        itemTimeout: [
          {
            required: true,
            message: '广告源超时不能为空',
            trigger: 'blur',
          },
        ],
        appId: [
          {
            required: true,
            message: '应用ID不能为空',
            trigger: 'blur',
          },
        ],
        status: [
          {
            required: true,
            message: '广告状态：1开 0关不能为空',
            trigger: 'blur',
          },
        ],
      },
      adType,
      adKind,
      adSwitchStatus,
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs.connectCodePosition && this.$refs.connectCodePosition.init()

        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$store
            .dispatch('api/ad/getAdPositionInfo', this.dataForm.id)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.name = data.adPosition.name
                this.dataForm.code = data.adPosition.code
                this.dataForm.type = data.adPosition.type
                this.dataForm.kind = data.adPosition.kind
                this.dataForm.maxTimeout = data.adPosition.maxTimeout
                this.dataForm.itemTimeout = data.adPosition.itemTimeout
                this.dataForm.appId = data.adPosition.appId
                this.dataForm.status = data.adPosition.status
              }
            })
        } else {
          this.dataForm.appId = this.$store.state.ad.appId
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const data = {
            id: this.dataForm.id || undefined,
            name: this.dataForm.name,
            code: this.dataForm.code,
            type: this.dataForm.type,
            kind: this.dataForm.kind,
            maxTimeout: this.dataForm.maxTimeout,
            itemTimeout: this.dataForm.itemTimeout,
            appId: this.dataForm.appId,
            status: this.dataForm.status,
          }
          this.$store
            .dispatch('api/ad/addOrUpdateAdPosition', data)
            .then(({ data }) => {
              if (data && data.code === 0) {
                this.$message('操作成功')
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            })
        }
      })
    },
  },
}
</script>
