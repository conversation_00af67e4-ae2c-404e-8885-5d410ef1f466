<template>
  <div>
    <h1>echarts</h1>
    <el-button @click="setTitle">设置标题</el-button>
    <v-chart class="chart" :option="option" :init-options="initOptions" />
  </div>
</template>

<script>
import { use } from 'echarts/core'
import VChart, { THEME_KEY } from 'vue-echarts'
// import chalk from '@/assets/json/echarts-chalk.json'
import walden from '@/assets/json/walden.json'

import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent,
} from 'echarts/components'
import { LineChart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  Mark<PERSON>ineComponent,
  Mark<PERSON>ointComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON>enderer,
  UniversalTransition,
])

export default {
  name: 'HelloWorld',
  components: {
    VChart,
  },
  provide: {
    // 主题，可以使用json，或者 内置的主题，或者注册好主题【但是目前插件注册方式可能不支持】
    // 自定义主题推荐使用json
    // [THEME_KEY]: 'dark',
    [THEME_KEY]: walden,
  },
  data() {
    return {
      initOptions: {},
      option: {
        title: {
          text: 'Temperature Change in the Coming Week',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {},
        toolbox: {
          show: true,
          feature: {
            dataZoom: {
              yAxisIndex: 'none',
            },
            dataView: { readOnly: false },
            magicType: { type: ['line', 'bar'] },
            restore: {},
            saveAsImage: {},
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} °C',
          },
        },
        series: [
          {
            name: 'Highest',
            type: 'line',
            data: [10, 11, 13, 11, 12, 12, 9],
            markPoint: {
              data: [
                { type: 'max', name: 'Max' },
                { type: 'min', name: 'Min' },
              ],
            },
            markLine: {
              data: [{ type: 'average', name: 'Avg' }],
            },
          },
          {
            name: 'Lowest',
            type: 'line',
            data: [1, -2, 2, 5, 3, 2, 0],
            markPoint: {
              data: [{ name: '周最低', value: -2, xAxis: 1, yAxis: -1.5 }],
            },
            markLine: {
              data: [
                { type: 'average', name: 'Avg' },
                [
                  {
                    symbol: 'none',
                    x: '90%',
                    yAxis: 'max',
                  },
                  {
                    symbol: 'circle',
                    label: {
                      position: 'start',
                      formatter: 'Max',
                    },
                    type: 'max',
                    name: '最高点',
                  },
                ],
              ],
            },
          },
        ],
      },
    }
  },
  methods: {
    setTitle() {
      // this.option.title.text = 'change'
    },
  },
}
</script>

<style scoped>
.chart {
  height: 400px;
}
</style>
