<template>
  <el-dialog title="提示" :visible.sync="show" width="30%">
    <span slot="footer" class="dialog-footer">
      <el-button @click="show = false">取 消</el-button>
      <el-button type="primary" @click="show = false">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mixinElDialogVisible } from '@/mixins'

export default {
  name: 'MDialog',
  mixins: [mixinElDialogVisible],
  data() {
    return {
      activeNames: ['1'],
      formInline: {
        user: '',
        region: '',
      },
    }
  },
  created() {
    console.log('created')
  },
  methods: {
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(() => {
          done()
        })
        .catch(() => {})
    },
    handleChange(val) {
      console.log(val)
    },
    onSubmit() {
      console.log('submit!')
    },
  },
}
</script>

<style scoped></style>
