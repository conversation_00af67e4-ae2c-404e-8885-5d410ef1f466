<template>
  <vxe-grid
    ref="vxe-grid"
    v-bind="gridOptions"
    @form-reset="handleReset"
    @form-submit-invalid="handleFormSubmitInvalid"
  >
    <template #form>
      <vxe-form
        title-colon
        ref="xForm"
        title-align="right"
        title-width="100"
        :data="formData2"
        :rules="formRules2"
        :loading="loading2"
        @submit="submitEvent2"
        @reset="resetEvent"
      >
        <vxe-form-gather span="12">
          <vxe-form-item title="名称" field="name" span="24"></vxe-form-item>
          <vxe-form-item title="昵称" span="24">
            <template #title>
              <span style="color: red;">自定义标题</span>
            </template>
            <template #default="{ data }">
              <span>自定义 {{ data.nickname }}</span>
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="标题"
            field="sex"
            span="24"
            :item-render="{}"
            title-overflow
          >
            <template #default="params">
              <vxe-select
                v-model="params.data.sex"
                placeholder="请选择性别"
                clearable
                @change="$refs.xForm.updateStatus(params)"
              >
                <vxe-option value="1" label="女"></vxe-option>
                <vxe-option value="2" label="男"></vxe-option>
              </vxe-select>
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="标题貌似有点长呢"
            field="age"
            span="24"
            :item-render="{}"
            title-overflow="title"
          >
            <template #default="params">
              <vxe-input
                v-model="params.data.age"
                type="integer"
                placeholder="请输入年龄"
                clearable
                @input="$refs.xForm.updateStatus(params)"
              ></vxe-input>
            </template>
          </vxe-form-item>
          <vxe-form-item
            title="标题貌似有点长呢"
            field="date"
            span="24"
            :item-render="{}"
            title-overflow="ellipsis"
          >
            <template #default="params">
              <vxe-input
                v-model="params.data.date"
                type="date"
                placeholder="请选择日期"
                clearable
                @change="$refs.xForm.updateStatus(params)"
              ></vxe-input>
            </template>
          </vxe-form-item>
        </vxe-form-gather>
        <vxe-form-gather span="12">
          <vxe-form-item
            title="标题貌似有点长呢标题貌似有点长呢"
            field="address"
            span="24"
            :item-render="{}"
          >
            <template #default="params">
              <vxe-textarea
                v-model="params.data.address"
                placeholder="请输入地址"
                :autosize="{ minRows: 6, maxRows: 10 }"
                clearable
                @input="$refs.xForm.updateStatus(params)"
              ></vxe-textarea>
            </template>
          </vxe-form-item>
        </vxe-form-gather>
        <vxe-form-item align="center" span="24" :item-render="{}">
          <template #default>
            <vxe-button type="submit" status="primary">基本表单</vxe-button>
            <vxe-button type="reset">重置</vxe-button>
          </template>
        </vxe-form-item>
      </vxe-form>
    </template>
  </vxe-grid>
</template>

<script>
import VXETable from 'vxe-table'

export default {
  data() {
    return {
      gridOptions: {
        resizable: true,
        border: true,
        showOverflow: true,
        height: 400,
        exportConfig: {},
        pagerConfig: {
          pageSize: 10,
        },
        toolbarConfig: {
          export: true,
          custom: true,
        },
        proxyConfig: {
          autoLoad: true,
          ajax: {
            // 接收 Promise API
            query: ({ page, form }) => {
              return new Promise(resolve => {
                setTimeout(() => {
                  const list = [
                    {
                      id: 10001,
                      name: 'Test1' + form.name,
                      nickname: 'T1',
                      role: 'Develop',
                      sex: '1',
                      age: 28,
                      address: 'Shenzhen',
                    },
                    {
                      id: 10002,
                      name: 'Test2' + form.name,
                      nickname: 'T2',
                      role: 'Test',
                      sex: '0',
                      age: 22,
                      address: 'Guangzhou',
                    },
                    {
                      id: 10003,
                      name: 'Test3' + form.name,
                      nickname: 'T3',
                      role: 'PM',
                      sex: '1',
                      age: 32,
                      address: 'Shanghai',
                    },
                    {
                      id: 10004,
                      name: 'Test4' + form.name,
                      nickname: 'T4',
                      role: 'Designer',
                      sex: '0',
                      age: 23,
                      address: 'Shenzhen',
                    },
                    {
                      id: 10005,
                      name: 'Test5' + form.name,
                      nickname: 'T5',
                      role: 'Develop',
                      sex: '0',
                      age: 30,
                      address: 'Shanghai',
                    },
                    {
                      id: 10006,
                      name: 'Test6' + form.name,
                      nickname: 'T6',
                      role: 'Develop',
                      sex: '0',
                      age: 27,
                      address: 'Shanghai',
                    },
                    {
                      id: 10007,
                      name: 'Test7' + form.name,
                      nickname: 'T7',
                      role: 'Develop',
                      sex: '1',
                      age: 29,
                      address: 'Shenzhen',
                    },
                    {
                      id: 10008,
                      name: 'Test8' + form.name,
                      nickname: 'T8',
                      role: 'Develop',
                      sex: '0',
                      age: 32,
                      address: 'Shanghai',
                    },
                    {
                      id: 10009,
                      name: 'Test9' + form.name,
                      nickname: 'T9',
                      role: 'Develop',
                      sex: '1',
                      age: 30,
                      address: 'Shenzhen',
                    },
                    {
                      id: 10010,
                      name: 'Test10' + form.name,
                      nickname: 'T10',
                      role: 'Develop',
                      sex: '0',
                      age: 34,
                      address: 'Shanghai',
                    },
                  ]
                  resolve({
                    result: list,
                    page: {
                      total: page.pageSize * 20,
                    },
                  })
                }, 500)
              })
            },
          },
        },
        columns: [
          { type: 'seq', width: 60 },
          { type: 'checkbox', width: 60 },
          { field: 'name', title: 'Name' },
          { field: 'nickname', title: 'Nickname' },
          { field: 'age', title: 'Age' },
          { field: 'sex', title: 'Sex' },
          { field: 'describe', title: 'Describe', showOverflow: true },
        ],
      },
      sexList1: [
        { value: '1', label: '男' },
        { value: '0', label: '女' },
      ],
      formRules2: {
        name: [
          { required: true, message: '请输入名称' },
          { min: 3, max: 5, message: '长度在 3 到 5 个字符' },
        ],
        nickname: [{ required: true, message: '请输入昵称' }],
        sex: [{ required: true, message: '请选择性别' }],
        age: [
          { required: true, message: '请输入年龄' },
          {
            validator({ itemValue }) {
              // 自定义校验
              if (Number(itemValue) > 35 || Number(itemValue) < 18) {
                return new Error('年龄在 18 ~ 35 之间')
              }
            },
          },
        ],
        date: [{ required: true, message: '必填校验' }],
        address: [{ required: true, message: '必填校验' }],
      },
      loading2: false,
      formData2: {
        name: 'test1',
        nickname: 'Testing',
        sex: '',
        age: 26,
        date: null,
        address: '左右布局',
      },
    }
  },
  methods: {
    test() {
      console.log('测试')
    },
    handleReset() {
      console.log('重置...')
    },
    handleFormSubmitInvalid() {
      console.log('未通过校验')
    },
    searchEvent() {
      VXETable.modal.message({ message: '查询事件', status: 'info' })
    },
    resetEvent() {
      VXETable.modal.message({ message: '重置事件', status: 'info' })
    },
    submitEvent2() {
      this.loading2 = true
      setTimeout(() => {
        this.loading2 = false
        VXETable.modal.message({ message: '保存成功', status: 'success' })
      }, 1000)
    },
  },
}
</script>

<style scoped></style>
