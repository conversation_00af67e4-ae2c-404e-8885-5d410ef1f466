<template>
  <nav>
    <div>
      <el-menu
        class="site-sidebar__menu"
        mode="horizontal"
      >
        <el-submenu index="2">
          <template slot="title">
            <div class="title">
              <img
                src="~@/assets/img/avatar.png"
                :alt="userName"
                style="margin-right: 10px; height: 40px"
              />
              {{ userName }}
            </div>
          </template>
          <el-menu-item index="2-1" @click.native="updatePasswordHandle">
            <i class="el-icon-edit" />
            修改密码
          </el-menu-item>
          <el-menu-item index="2-2" @click.native="logoutHandle">
            <i class="el-icon-document-delete" />
            退出
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePassowrdVisible" ref="updatePassowrd" />
  </nav>
</template>

<script>
import UpdatePassword from './main-navbar-update-password'
import { clearLoginInfo } from '@/utils'
export default {
  data() {
    return {
      updatePassowrdVisible: false,
    }
  },
  components: {
    UpdatePassword,
  },
  computed: {
    navbarLayoutType: {
      get() {
        return this.$store.state.common.navbarLayoutType
      },
    },
    sidebarFold: {
      get() {
        return this.$store.state.common.sidebarFold
      },
      set(val) {
        this.$store.commit('common/updateSidebarFold', val)
      },
    },
    mainTabs: {
      get() {
        return this.$store.state.common.mainTabs
      },
      set(val) {
        this.$store.commit('common/updateMainTabs', val)
      },
    },
    userName: {
      get() {
        return this.$store.state.user.name
      },
    },
  },
  methods: {
    // 修改密码
    updatePasswordHandle() {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init()
      })
    },
    // 退出
    logoutHandle() {
      this.$confirm(`确定进行[退出]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$http({
            url: this.$http.adornUrl('/sys/logout'),
            method: 'post',
            data: this.$http.adornData(),
          }).then(({ data }) => {
            if (data && data.code === 0) {
              clearLoginInfo()
              this.$router.push({ name: 'login' })
              // this.$router.push('/login')
            }
          })
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-menu.el-menu--horizontal {
    border-bottom: none;
  }
}
.title {
  display: flex;
  align-items: center;
  padding-right: 20px;
  color: white;

  &:hover {
    color: rgba(255, 255, 255, 0.8);
  }
}
</style>
