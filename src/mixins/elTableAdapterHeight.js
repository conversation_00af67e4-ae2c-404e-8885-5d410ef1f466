// el-table组件自适应高度
// 需要给组件加上 :max-height="tableHeight" 属性 切需要加上 class='adapter-height'

import { getTop } from '@/utils/doms'

export default {
  data() {
    return {
      tableHeight: null,
    }
  },
  watch: {
    dataList() {
      this.__changeTableHeight()
    },
  },
  mounted() {
    window.addEventListener('resize', () => {
      this.__changeTableHeight()
    })
  },
  methods: {
    __changeTableHeight() {
      this.$nextTick(() => {
        if (!document.querySelector('.adapter-height')) {
          throw new Error('需要给el-table组件加上 adapter-height class')
        }
        if (!this.dataList.length) {
          this.tableHeight = 112
          return
        }

        const clientHeight = document.documentElement.clientHeight
        const elTable = document.querySelector('.adapter-height.el-table')
        const elTableBody = document.querySelector(
          '.adapter-height .el-table__body'
        )
        const headerHeight = document.querySelector(
          '.adapter-height .el-table__header'
        ).clientHeight
        const elPagination = document.querySelector('.el-pagination')

        const maxHeight =
          clientHeight -
          getTop(elTable) -
          40 - // 40 为padding的值
          (elPagination ? elPagination.clientHeight + 15 : 0) // 15为margin值

        const contentHeight = elTableBody.clientHeight + headerHeight

        if (contentHeight > maxHeight) {
          this.tableHeight = maxHeight
        } else {
          this.tableHeight = elTableBody.offsetHeight + headerHeight + 15
        }
      })
    },
  },
}
