/**
 在组件el-dialog中使用，目的是解决封装dialog的时候书写组件不方便

 |=================> demo start <=================|

 # 封装的dialog
 <el-dialog title="提示" :visible.sync="show" width="30%">
  <!-- 这里一般会写很多表单之类的 -->
  <span slot="footer" class="dialog-footer">
    <el-button @click="show = false">取 消</el-button>
    <el-button type="primary" @click="show = false">确 定</el-button>
  </span>
 </el-dialog>

 <script>
   import { mixinElDialogVisible } from '@/mixins'
   export default {
    mixins: [mixinElDialogVisible],
    created() {
      // 可以在这里请求数据，不要每次都写init函数了
    }
   }
 </script>

 # 使用封装的dialog
 # 这样书写组件就方便一些，v-if如果加上的话组件每次都会销毁重建
 # 那么如果dialog内有表单的话，每次打开dialog的话，表单就都会重置极其方便
 # 且一些请求的数据都可以放入dialog中（销毁重建后生命周期钩子每次都会走）
 <McDialog v-if="visible" :visible.sync="visible" />

 |=================> demo end <=================|
 */
export default {
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    show: {
      get() {
        return this.visible
      },
      set(show) {
        this.$emit('update:visible', show)
      },
    },
  },
}
