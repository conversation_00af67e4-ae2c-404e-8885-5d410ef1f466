// ==========================================================================
// SCSS Variables - Design System
// ==========================================================================

// ==========================================================================
// Colors - Primary Palette
// ==========================================================================
// 站点主色 - 现代化渐变蓝色系
// Tips: 要达到整站主题修改效果，请确保 $--color-primary 与 element-ui-theme 中的主题色一致
$--color-primary: #32a77e;
$--color-primary-light: #76D6B4;
$--color-primary-dark: #30A179;
$--color-primary-gradient: linear-gradient(135deg, #32a77e 0%, #76D6B4 100%);

// 辅助色彩
$--color-secondary: #f093fb;
$--color-accent: #4facfe;
$--color-accent-light: #00f2fe;

// ==========================================================================
// Colors - Semantic Colors
// ==========================================================================
$--color-success: #67c23a;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #909399;

// ==========================================================================
// Colors - Neutral Colors
// ==========================================================================
$--color-text-primary: #303133;
$--color-text-regular: #606266;
$--color-text-secondary: #909399;
$--color-text-placeholder: #c0c4cc;

$--color-white: #ffffff;
$--color-black: #000000;

$--border-color-base: #dcdfe6;
$--border-color-light: #e4e7ed;
$--border-color-lighter: #ebeef5;
$--border-color-extra-light: #f2f6fc;

$--background-color-base: #f5f7fa;

// ==========================================================================
// Layout - 统一的现代化配色方案
// ==========================================================================

// 主色调：深蓝到紫色的现代渐变
$--unified-gradient: linear-gradient(135deg, #32a77e 0%, #76D6B4 100%);
$--unified-gradient-dark: linear-gradient(135deg, #30A179 0%, #32a77e 100%);

// ==========================================================================
// Layout - Navbar
// ==========================================================================
$navbar--background: $--color-primary;
$navbar--background-color: $--color-primary;
$navbar--height: 60px;
$navbar--text-color: $--color-white;
$navbar--shadow: 0 2px 12px 0 rgba(102, 126, 234, 0.15);

// ==========================================================================
// Layout - Sidebar
// ==========================================================================
$sidebar--background: $--color-white;
$sidebar--background-color-dark: #30A179;
$sidebar--color-text-dark: $--color-text-primary;
$sidebar--color-text-active: $--color-primary;
$sidebar--width: 260px;
$sidebar--width-collapsed: 70px;
$sidebar--item-hover-bg: rgba(255, 255, 255, 0.12);
$sidebar--item-active-bg: rgba(255, 255, 255, 0.2);

// ==========================================================================
// Layout - Content
// ==========================================================================
$content--background-color: #f8fafc;
$content--background-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
$content--card-background: $--color-white;
$content--card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$content--card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

// ==========================================================================
// Typography
// ==========================================================================
$--font-family-primary: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
$--font-size-base: 14px;
$--font-size-small: 12px;
$--font-size-large: 16px;
$--font-size-extra-large: 18px;

$--line-height-base: 1.15;

// ==========================================================================
// Spacing
// ==========================================================================
$--spacing-xs: 4px;
$--spacing-sm: 8px;
$--spacing-md: 12px;
$--spacing-lg: 16px;
$--spacing-xl: 20px;
$--spacing-xxl: 24px;

// ==========================================================================
// Border Radius
// ==========================================================================
$--border-radius-base: 4px;
$--border-radius-small: 2px;
$--border-radius-large: 6px;

// ==========================================================================
// Shadows - 现代化阴影系统
// ==========================================================================
$--box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$--box-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$--box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$--box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$--box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$--box-shadow-light: $--box-shadow-sm;

// 特殊阴影
$--box-shadow-colored: 0 4px 14px 0 rgba(102, 126, 234, 0.15);
$--box-shadow-inset: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

// ==========================================================================
// Transitions - 流畅动画系统
// ==========================================================================
$--transition-fast: all 0.15s ease-out;
$--transition-base: all 0.25s ease-out;
$--transition-slow: all 0.35s ease-out;
$--transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

// 特定动画
$--transition-transform: transform 0.25s ease-out;
$--transition-opacity: opacity 0.2s ease-out;
$--transition-colors: background-color 0.2s ease-out, border-color 0.2s ease-out, color 0.2s ease-out;
