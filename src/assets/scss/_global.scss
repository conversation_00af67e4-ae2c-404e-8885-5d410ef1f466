// ==========================================================================
// Global Styles - Component Overrides & Utilities
// ==========================================================================

// ==========================================================================
// Element UI - Dialog Adaptive (临时禁用防闪烁优化)
// ==========================================================================
// 解决 Element UI Dialog 内容过长的问题
// 暂时移除可能导致闪烁的优化属性
.adaptive_dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;

    // 临时移除这些可能导致闪烁的属性
    // will-change: transform;
    // transform: translateZ(0);

    .el-dialog__body {
      max-height: calc(100% - 54px - 66px);
      box-sizing: border-box;
      overflow: hidden auto;
    }
  }
}

// ==========================================================================
// Element UI - Table Customization (基础样式)
// ==========================================================================
.el-table th.el-table__cell {
  background-color: #fafafa;
  padding: 12px 0;
  font-size: 13px;
  font-weight: 500;
}

// ==========================================================================
// Vue Router - Transition Animations
// ==========================================================================

// Fade transition
.fade-enter-active,
.fade-leave-active {
  transition: opacity $--transition-slow;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

// Slide fade transition
.slide-fade-enter-active {
  transition: $--transition-base;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

// ==========================================================================
// Utility Classes
// ==========================================================================

// Page table height utility
.page-table-height {
  height: calc(100vh - 135px);
}
