// ==========================================================================
// Sidebar Enhancements - 侧边栏美观度增强
// ==========================================================================

// ==========================================================================
// 主侧边栏样式增强
// ==========================================================================
.site-sidebar {
  // Logo区域美化
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $--spacing-lg $--spacing-xl;
    //background: rgba(255, 255, 255, 0.05);
    //border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;

    h2 {
      color: $--color-white;
      font-size: $--font-size-large + 2px;
      font-weight: 600;
      margin: 0;
      //text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      //background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 0.8) 100%);
      //-webkit-background-clip: text;
      //-webkit-text-fill-color: transparent;
      //background-clip: text;
    }

    .env-text {
      position: absolute;
      top: 5px;
      right: 10px;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      color: $--color-white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

      span {
        text-shadow: none;
      }
    }
  }
}

// ==========================================================================
// 垂直菜单样式增强
// ==========================================================================
.sidebar-container {
  background: $sidebar--background !important;
  border: none !important;

  // 折叠按钮美化
  .toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    background: rgba(255, 255, 255, 0.05);
    color: $sidebar--color-text-dark;
    cursor: pointer;
    transition: $--transition-colors;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-family: monospace;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: $sidebar--color-text-active;
    }

    &:active {
      background: rgba(255, 255, 255, 0.15);
    }
  }

  // 菜单项样式增强
  .el-menu-item,
  .el-submenu__title {
    height: 50px !important;
    line-height: 50px !important;
    color: $sidebar--color-text-dark !important;
    background: transparent !important;
    border-radius: $--border-radius-base;
    margin: 4px 12px;
    transition: $--transition-colors, $--transition-transform !important;
    position: relative;
    overflow: hidden;

    // 左侧激活指示器
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 0;
      background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
      border-radius: 2px;
      transition: $--transition-base;
    }

    // 图标样式
    .site-sidebar__menu-icon {
      margin-right: $--spacing-md !important;
      font-size: $--font-size-large !important;
      transition: $--transition-transform;
      width: 20px;
      text-align: center;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    // Hover效果
    &:hover {
      background: $sidebar--item-hover-bg !important;
      color: $sidebar--color-text-active !important;
      transform: translateX(4px);

      .site-sidebar__menu-icon {
        transform: scale(1.1);
      }
    }

    // 激活状态
    &.is-active {
      background: $sidebar--item-active-bg !important;
      color: $sidebar--color-text-active !important;
      font-weight: 500;

      &::before {
        height: 24px;
      }

      .site-sidebar__menu-icon {
        color: $--color-white !important;
      }
    }

    // 焦点状态
    &:focus {
      background: $sidebar--item-hover-bg !important;
      color: $sidebar--color-text-active !important;
    }
  }

  // 子菜单样式
  .el-submenu {
    .el-submenu__title {
      padding-left: 15px !important;
      &:hover {
        background: $sidebar--item-hover-bg !important;
        color: $sidebar--color-text-active !important;
      }
    }

    .el-menu {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: $--border-radius-base;
      margin: 4px 12px;
      padding: 8px 0;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .el-menu-item {
        margin: 2px 8px;
        padding-left: 15px !important;
        height: 40px !important;
        line-height: 40px !important;
        font-size: $--font-size-small;
        border-radius: $--border-radius-small;
        transition: $--transition-colors, $--transition-transform;
        position: relative;
        overflow: hidden;

        &::before {
          left: 8px;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.15) !important;
          transform: translateX(2px);
          color: $sidebar--color-text-active !important;
        }

        &.is-active {
          background: rgba(255, 255, 255, 0.2) !important;
          color: $sidebar--color-text-active !important;
          font-weight: 500;
          margin: 2px 6px; // 稍微减少边距，防止溢出

          &::before {
            height: 16px;
          }
        }
      }
    }

    &.is-opened {
      > .el-submenu__title {
        background: $sidebar--item-active-bg !important;
        color: $sidebar--color-text-active !important;

        &::before {
          height: 3px;
        }
      }
    }
  }

  // 折叠状态样式
  &.el-menu--collapse {
    .el-menu-item,
    .el-submenu__title {
      margin: 4px 8px;
      text-align: center;
      display: flex !important;
      align-items: center;
      justify-content: center;
      padding: 0 !important;

      .site-sidebar__menu-icon {
        margin-right: 0 !important;
        margin-left: 0 !important;
        width: auto;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      span {
        display: none;
      }

      // 将选中指示器移到底部，避免与图标重叠
      &::before {
        left: 50%;
        top: auto;
        bottom: 4px;
        transform: translateX(-50%);
        width: 20px;
        height: 0;
        background: #32a77e;
        border-radius: 2px 2px 0 0;
        transition: $--transition-base;
      }

      // 激活状态下的指示器
      &.is-active::before {
        height: 3px;
      }

      // 折叠状态下的hover效果，避免横向移动
      &:hover {
        transform: none !important;

        .site-sidebar__menu-icon {
          transform: scale(1.15);
        }
      }
    }

    .toggle-button {
      font-size: 16px;
      letter-spacing: 0;
    }
  }
}

// ==========================================================================
// 收起状态下弹出菜单样式
// ==========================================================================
.site-sidebar--dark-popper {
  background: $sidebar--background !important;
  border: none !important;
  border-radius: $--border-radius-base !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  min-width: 180px !important;
  overflow: hidden !important;
  padding: 4px 0 !important;

  .el-menu.el-menu--popup.el-menu--popup-right-start {
    background: transparent !important;
    border: none !important;
    border-radius: $--border-radius-base !important;
    box-shadow: none !important;
    max-height: 400px !important;
    overflow-y: auto !important;

    .el-menu-item {
      background: transparent !important;
      position: relative;
      overflow: hidden;
      color: $sidebar--color-text-dark !important;
      height: 40px !important;
      line-height: 40px !important;
      margin: 2px 8px !important;
      border-radius: $--border-radius-small !important;
      transition: $--transition-colors, $--transition-transform !important;

      &::before {
        content: '';
        position: absolute;
        left: 4px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 0;
        background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%) !important;
        border-radius: 2px;
        transition: $--transition-base !important;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.15) !important;
        color: $sidebar--color-text-active !important;
        transform: translateX(2px);

        &::before {
          height: 20px;
        }
      }

      &.is-active {
        background: rgba(255, 255, 255, 0.2) !important;
        color: $sidebar--color-text-active !important;
        font-weight: 500;

        &::before {
          height: 20px;
        }
      }

      .site-sidebar__menu-icon {
        margin-right: $--spacing-md !important;
        font-size: $--font-size-base !important;
        color: inherit !important;
        width: 16px;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
    }

    .el-submenu {
      .el-submenu__title {
        background: transparent !important;
        color: $sidebar--color-text-dark !important;
        height: 40px !important;
        line-height: 40px !important;
        margin: 2px 8px !important;
        border-radius: $--border-radius-small !important;
        transition: $--transition-colors, $--transition-transform !important;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          left: 4px;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 0;
          background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%) !important;
          border-radius: 2px;
          transition: $--transition-base !important;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.15) !important;
          color: $sidebar--color-text-active !important;
          transform: translateX(2px);

          &::before {
            height: 20px;
          }
        }

        .site-sidebar__menu-icon {
          margin-right: $--spacing-md !important;
          font-size: $--font-size-base !important;
          color: inherit !important;
          width: 16px;
          text-align: center;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }

        .el-submenu__icon-arrow {
          color: inherit !important;
        }
      }

      &.is-opened > .el-submenu__title {
        background: rgba(255, 255, 255, 0.15) !important;
        color: $sidebar--color-text-active !important;

        &::before {
          height: 20px;
        }
      }
    }
  }
}


// 弹出菜单滚动样式
.site-sidebar--scroll {
  max-height: 400px;
  overflow-y: auto;
  -ms-overflow-style: none; // IE and Edge
  scrollbar-width: none; // Firefox

  &::-webkit-scrollbar {
    display: none; // Chrome, Safari, Opera
  }
}

// ==========================================================================
// 水平导航栏样式增强
// ==========================================================================
.site-navbar {
  .site-sidebar__menu {
    background: transparent !important;
    border: none !important;

    .el-submenu {
      .el-submenu__title {
        color: $navbar--text-color !important;
        background: transparent !important;
        border-radius: $--border-radius-base;
        margin: 0 8px;
        transition: $--transition-colors;

        &:hover {
          background: rgba(255, 255, 255, 0.1) !important;
        }

        // 用户头像区域
        > div {
          display: flex;
          align-items: center;
          padding: 8px 16px;

          img {
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: $--transition-base;

            &:hover {
              border-color: rgba(255, 255, 255, 0.6);
              transform: scale(1.05);
            }
          }
        }
      }

      .el-menu {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: $--border-radius-base;
        box-shadow: $--box-shadow-lg;
        border: 1px solid rgba(255, 255, 255, 0.2);

        .el-menu-item {
          color: $--color-text-primary !important;
          transition: $--transition-colors;

          &:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            color: $--color-primary !important;
          }

          i {
            color: $--color-text-secondary;
            margin-right: 8px;
          }
        }
      }
    }
  }
}

// ==========================================================================
// 弹出菜单边框优化
// ==========================================================================
// 确保弹出菜单的 z-index 正确并移除边框
.el-popper[x-placement^="right"] {
  z-index: 2000 !important;

  // 移除 Element UI 默认的边框
  .el-menu {
    border: none !important;
    box-shadow: none !important;
  }
}

// 全局移除侧边栏弹出菜单的边框
.el-menu--popup {
  border: none !important;
  box-shadow: none !important;
}

// 特别针对侧边栏的弹出菜单
.el-submenu__popup {
  border: none !important;
  box-shadow: none !important;

  .el-menu {
    border: none !important;
    box-shadow: none !important;
  }
}

// 折叠状态下的弹出菜单定位优化
.el-menu--collapse {
  .el-submenu .el-menu {
    position: absolute !important;
    left: 100% !important;
    top: 0 !important;
    min-width: 180px !important;
    border: none !important;
    box-shadow: none !important;
  }
}

// ==========================================================================
// 响应式适配
// ==========================================================================
@media (max-width: 768px) {
  .sidebar-container {
    .el-menu-item,
    .el-submenu__title {
      margin: 2px 8px;
      font-size: $--font-size-small;
    }
  }
}
