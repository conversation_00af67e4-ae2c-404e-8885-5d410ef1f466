// ==========================================================================
// Element UI Table Fixes - 最小化干预的表格样式修复
// ==========================================================================

.el-table {
  // 只保留最基本的样式增强，不干预Element UI内部结构

  // 表头样式 - 仅美化，不改变布局
  th.el-table__cell {
    background-color: #fafafa !important;
    font-weight: 500;
    color: #606266;
    font-size: 13px;

    // 保持排序功能正常
    &.is-sortable {
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa !important;
      }
    }
  }

  // 表格行悬停效果 - 轻微增强
  .el-table__body tr:hover > td.el-table__cell {
    background-color: #f5f7fa;
  }

  // 选中行样式
  .el-table__body tr.current-row > td.el-table__cell {
    background-color: #ecf5ff;
  }

  // 展开图标样式优化
  &__expand-icon {
    transition: transform 0.2s ease-in-out;

    &--expanded {
      transform: rotate(90deg);
    }
  }
}

// ==========================================================================
// 表格分页样式
// ==========================================================================
.el-table + .el-pagination {
  margin-top: 16px;
  text-align: right;
}
