// ==========================================================================
// Base Styles - Foundation & Reset
// ==========================================================================

// ==========================================================================
// Universal Box Sizing
// ==========================================================================
*,
*::before,
*::after {
  box-sizing: border-box;
}

// ==========================================================================
// Base Typography & Body
// ==========================================================================
body {
  font-family: $--font-family-primary;
  font-size: $--font-size-base;
  line-height: $--line-height-base;
  color: $--color-text-primary;
  background-color: $--color-white;
  margin: 0;
  padding: 0;
}

// ==========================================================================
// Links
// ==========================================================================
a {
  color: mix($--color-white, $--color-primary, 20%);
  text-decoration: none;
  transition: $--transition-fast;

  &:focus,
  &:hover {
    color: $--color-primary;
    text-decoration: underline;
  }
}

// ==========================================================================
// Images
// ==========================================================================
img {
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

// ==========================================================================
// Utility Classes
// ==========================================================================

// Clearfix utility
.clearfix {
  &::before,
  &::after {
    content: '';
    display: table;
  }

  &::after {
    clear: both;
  }
}

// ==========================================================================
// Element UI Overrides
// ==========================================================================
.site-wrapper {
  .el-pagination {
    margin-top: $--spacing-lg;
    text-align: right;
  }
}

// ==========================================================================
// Layout - Site Wrapper
// ==========================================================================
.site-wrapper {
  position: relative;
  min-width: 1180px;
}

// ==========================================================================
// Layout - Sidebar Fold State
// ==========================================================================
.site-sidebar--fold {
  // Collapsed width elements
  .site-navbar__header,
  .site-navbar__brand,
  .site-sidebar,
  .site-sidebar__inner,
  .el-menu.site-sidebar__menu {
    width: $sidebar--width-collapsed;
  }

  // Adjust content margins
  .site-navbar__body,
  .site-content__wrapper {
    margin-left: $sidebar--width-collapsed;
  }

  // Brand visibility toggle
  .site-navbar__brand {
    &-lg {
      display: none;
    }

    &-mini {
      display: inline-block;
    }
  }

  // Sidebar overflow handling
  .site-sidebar,
  .site-sidebar__inner {
    overflow: initial;
  }

  // Menu icon adjustments
  .site-sidebar__menu-icon {
    margin-right: 0;
    font-size: 20px;
  }

  // Tabs header positioning
  .site-content--tabs > .el-tabs > .el-tabs__header {
    left: $sidebar--width-collapsed;
  }
}

// ==========================================================================
// Layout - Transition Animations
// ==========================================================================
.site-navbar__header,
.site-navbar__brand,
.site-navbar__body,
.site-sidebar,
.site-sidebar__inner,
.site-sidebar__menu.el-menu,
.site-sidebar__menu-icon,
.site-content__wrapper,
.site-content--tabs > .el-tabs .el-tabs__header {
  transition: display $--transition-base, left $--transition-base,
              width $--transition-base, margin-left $--transition-base,
              font-size $--transition-base;
}

// ==========================================================================
// Layout - Navbar
// ==========================================================================
.site-navbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
  height: $navbar--height;
  background: $navbar--background;
  box-shadow: $navbar--shadow;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  // Inverse navbar theme
  &--inverse {
    .el-menu {
      background-color: $navbar--background-color;

      > .el-menu-item,
      > .el-submenu > .el-submenu__title {
        color: $navbar--text-color;

        &:focus,
        &:hover {
          color: $navbar--text-color;
          background-color: mix($--color-black, $navbar--background-color, 15%);
        }
      }

      > .el-menu-item.is-active,
      > .el-submenu.is-active > .el-submenu__title {
        border-bottom-color: mix($--color-white, $navbar--background-color, 85%);
      }

      .el-menu-item i,
      .el-submenu__title i,
      .el-dropdown {
        color: $navbar--text-color;
      }
    }

    .el-menu--popup-bottom-start {
      background-color: $navbar--background-color;
    }
  }

  // Navbar header section
  &__header {
    position: relative;
    float: left;
    width: $sidebar--width;
    height: $navbar--height;
    overflow: hidden;
  }

  // Brand/logo section
  &__brand {
    display: table-cell;
    vertical-align: middle;
    width: $sidebar--width;
    height: $navbar--height;
    margin: 0;
    line-height: $navbar--height;
    font-size: $--font-size-extra-large + 2px;
    text-align: center;
    text-transform: uppercase;
    white-space: nowrap;
    color: $navbar--text-color;

    &-lg,
    &-mini {
      margin: 0 $--spacing-xs + 1px;
      color: $navbar--text-color;

      &:focus,
      &:hover {
        color: $navbar--text-color;
        text-decoration: none;
      }
    }

    &-mini {
      display: none;
    }
  }

  // Sidebar toggle switch
  &__switch {
    font-size: $--font-size-large + 2px;
    border-bottom: none !important;
  }

  // User avatar dropdown
  &__avatar {
    border-bottom: none !important;

    * {
      vertical-align: inherit;
    }

    .el-dropdown-link {
      > img {
        width: 36px;
        height: auto;
        margin-right: $--spacing-xs + 1px;
        border-radius: 50%;
        vertical-align: middle;
      }
    }
  }

  // Navbar body content
  &__body {
    position: relative;
    margin-left: $sidebar--width;
    padding-right: $--spacing-lg - 1px;
    background-color: $--color-white;
  }
  // Navbar menu
  &__menu {
    float: left;
    background-color: transparent;
    border-bottom: 0;

    &--right {
      float: right;
    }

    a {
      &:focus,
      &:hover {
        text-decoration: none;
      }
    }

    .el-menu-item,
    .el-submenu > .el-submenu__title {
      height: $navbar--height;
      line-height: $navbar--height;
    }

    .el-submenu > .el-menu {
      top: $navbar--height + 5px;
    }

    .el-badge {
      display: inline;
      z-index: 2;

      &__content {
        line-height: $--spacing-lg;
      }
    }
  }
}

// ==========================================================================
// Layout - Sidebar
// ==========================================================================
.site-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1020;
  width: 100%;
  height: $navbar--height;
  background: $sidebar--background;
  overflow: hidden;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  // Scrollable sidebar variant
  &--scroll {
    max-height: 650px;
    overflow-y: auto;
    -ms-overflow-style: none; // IE and Edge
    scrollbar-width: none; // Firefox

    &::-webkit-scrollbar {
      display: none; // Chrome, Safari, Opera
    }
  }

  // Dark theme sidebar
  &--dark,
  &--dark-popper {
    background: $sidebar--background;

    .site-sidebar__menu.el-menu,
    > .el-menu--popup {
      background: transparent;

      .el-menu-item,
      .el-submenu > .el-submenu__title {
        color: $sidebar--color-text-dark;
        background: transparent;
        transition: $--transition-colors;
        border-radius: $--border-radius-base;
        margin: 2px 8px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 0;
          background: $--color-primary;
          border-radius: 2px;
          transition: $--transition-base;
        }

        &:focus,
        &:hover {
          color: $sidebar--color-text-active;
          background: $sidebar--item-hover-bg;
          transform: translateX(4px);
        }
      }

      .el-menu,
      .el-submenu.is-opened {
        background: rgba(0, 0, 0, 0.1);
        border-radius: $--border-radius-base;
        margin: 4px 8px;
      }

      .el-menu-item.is-active,
      .el-submenu.is-active > .el-submenu__title {
        color: $sidebar--color-text-active;
        background: $sidebar--item-active-bg;
        font-weight: 500;

        &::before {
          height: 20px;
        }
      }
    }
  }
  // Sidebar inner container
  &__inner {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 60px;
  }

  // Sidebar menu
  &__menu.el-menu {
    border-right: 0;
  }

  // Menu icons
  &__menu-icon {
    margin-right: $--spacing-xs + 1px;
    text-align: center;
    font-size: $--font-size-large;
    color: inherit !important;
  }
}

// ==========================================================================
// Layout - Content Area
// ==========================================================================
.site-content {
  position: relative;

  // Content wrapper
  &__wrapper {
    position: relative;
    min-height: 100vh;
    background: $content--background-gradient;
    padding: 0;

    // 登录页面特殊处理 - 不设置背景
    .site-page--login & {
      background: transparent;
      padding: 0;
    }
  }

  // Tabs content variant
  &--tabs {
    // Additional styles for tabbed content can be added here
  }

  // Tab navigation styling
  > .el-tabs {

    > .el-tabs__header {
      left: $--spacing-lg - 1px;
      width: calc(100% - #{$--spacing-lg - 1px});
      z-index: 930;
      padding: 0 55px 0 0;
      box-shadow: $--box-shadow-light;
      background-color: $--color-white;

      > .el-tabs__nav-wrap {
        margin-bottom: 0;

        &::after {
          display: none;
        }

        .el-tabs__nav {
          border: none;
          border-radius: 0;

          .el-tabs__item + .el-tabs__item {
            margin-left: $--spacing-sm + 2px;
          }

          .el-tabs__item {
            font-size: $--font-size-small;
            padding: 0 $--spacing-sm + 2px;
            line-height: 30px;
            height: 30px;
            border: 1px solid $--border-color-light;
            border-bottom: none;
            border-radius: $--border-radius-small $--border-radius-small 0 0;
            transition: $--transition-fast;

            &:hover {
              color: $--color-primary;
            }

            &.is-active {
              color: $--color-primary;
              border-color: $--color-primary;
            }
          }
        }
      }
    }

    > .el-tabs__content {
      padding: 0 $--spacing-lg - 1px $--spacing-lg - 1px;
    }
  }

  > .site-tabs__tools {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 931;
    height: 30px;
    padding: 0 $--spacing-md;
    font-size: $--font-size-large;
    line-height: 30px;
    background-color: $content--background-color;
    cursor: pointer;
    transition: $--transition-fast;

    &:hover {
      background-color: darken($content--background-color, 5%);
    }

    .el-icon--right {
      margin-left: 0;
    }
  }
}

// ==========================================================================
// Element UI - Table Expand Icon
// ==========================================================================
.el-table__expand-icon {
  display: inline-block;
  width: 14px;
  vertical-align: middle;
  margin-right: $--spacing-xs + 1px;
}
