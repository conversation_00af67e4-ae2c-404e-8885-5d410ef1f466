<template>
  <el-dialog
    :visible.sync="showModel"
    :title="title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    v-bind="modelConfig"
    @closed="closedEvent"
    @open="openEvent"
  >
    <!-- element-ui 的 form 表单更好用一些 -->
    <el-form
      :model="formData"
      :rules="formRule"
      ref="form-box"
      v-bind="formConfig"
    >
      <slot />
    </el-form>
    <span slot="footer">
      <el-button @click="showModel = false">取消</el-button>
      <el-button type="primary" @click="submit" :loading="loading">
        提交
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { merge } from 'lodash'

export default {
  name: 'model-in-table',
  props: {
    /**
     * 获取数据的函数
     * 例子：taskConfigRequest.selectItem
     */
    selectItemRequest: {
      type: Function,
      default: () => id => Promise.resolve(id),
    },
    /**
     * 更新或者新增的函数
     * 例子：taskConfigRequest.insertOrUpdate
     */
    insertOrUpdateRequest: {
      type: Function,
      default: () => (id, data) => Promise.resolve(id, data),
    },
    visible: {
      type: Boolean,
      default: false,
    },
    // 某项的数据id
    dataId: {
      type: [Number, String],
    },
    // 表单数据
    formData: {
      type: Object,
      default: () => ({}),
    },
    formRule: {
      type: Object,
    },
    modelConfig: {
      type: Object,
    },
    // 除了 data items rules 意外的表单配置项目
    // formConfig.beforeSubmit 表单提交之前执行
    formConfig: {
      type: Object,
    },
    // 是否自动更新数据
    autoUpdate: {
      type: Boolean,
      default: true,
    },
    // 是否自动提交表单
    autoCommit: {
      type: Boolean,
      default: true,
    },
    // 更新的时候是否跳过空数据
    isUpdateEmpty: {
      type: Boolean,
      default: false,
    },
    // 表单提交之前适配数据,需要返回一个 formData 对象
    adapterFormData: {
      type: Function,
    },
    // 返回数据的时候，适配器
    adapterResData: {
      type: Function,
    },
  },
  data() {
    return {
      // 缓存数据，有待考虑...
      catchFormData: {},
      loading: false,
    }
  },
  computed: {
    showModel: {
      set(v) {
        this.$emit('update:visible', v)
      },
      get() {
        return this.visible
      },
    },
    title() {
      return this.dataId ? '编辑' : '新增'
    },
  },
  created() {
    this.catchFormData = merge({}, this.formData)
  },
  methods: {
    getData() {
      this.selectItemRequest(this.dataId).then(res => {
        // 在 genCRUDRequest 中 多生成了一个统一的字段
        const resData = this.adapterResData
          ? this.adapterResData(res.__data__)
          : res.__data__

        this.$emit('init-data', {
          data: res,
          update: (updateData = resData) => {
            this.update(updateData)
          },
        })

        // 更新数据：后端请求到的数据，会同步到 formData 中
        // 但是有可能参数会不一样，如果不一样，就需要把autoUpdate设置为false，
        // 在@init-data事件中，手动 update(传入参数)
        this.autoUpdate && this.update(resData)
      })
    },
    async submit() {
      try {
        const [beforeSubmitRes] = await Promise.all([
          // 表单提交之前
          this.formConfig.beforeSubmit ? this.formConfig.beforeSubmit() : true,
          this.$refs['form-box'].validate(),
        ])

        if (!beforeSubmitRes) return

        let formData = { ...this.formData }
        if (this.adapterFormData) {
          formData = await this.adapterFormData(this.formData)
        }

        this.$emit('submit', {
          commit: (requestData = formData) => this.commit(requestData),
        })
        // 提交表单：把 formData 中的数据作为参数给后端
        // 但是有可能参数会不一样，如果不一样，就需要把autoCommit设置为false，
        // 在@submit事件中，手动commit(传入参数)
        // 也可以使用 adapterFormData 适配一下数据
        this.autoCommit && this.commit(formData)
      } catch (e) {
        console.log('err', e)
      }
    },
    commit(data) {
      return new Promise((resolve, reject) => {
        this.loading = true
        this.insertOrUpdateRequest(this.dataId, data)
          .then(() => {
            this.showModel = false
            this.$vxeMessage({
              content: '更新成功',
              status: 'success',
            })
            this.$emit('refresh')
            resolve()
          })
          .catch(() => {
            reject()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    update(resData) {
      if (resData) {
        const data = {}
        for (const formDataKey in this.formData) {
          if (this.isUpdateEmpty) {
            data[formDataKey] = resData[formDataKey]
          } else {
            data[formDataKey] =
              resData[formDataKey] === null ||
              resData[formDataKey] === undefined
                ? this.catchFormData[formDataKey]
                : resData[formDataKey]
          }
        }
        console.log('update data', data)
        this.autoUpdate && this.$emit('update:formData', merge({}, data))
      }
    },
    closedEvent() {
      this.$emit('update:formData', this.catchFormData)
      this.$emit('closed')
    },
    openEvent() {
      this.dataId && this.getData()
    },
  },
}
</script>
