<template>
  <div>
    <div v-for="(item, index) in dataForm" :key="index" class="row">
      <!--ecpm-->
      <div v-if="isShowEcpm" class="col ecpm">
        <el-input-number v-model="item.ecpmLow" :min="0" />
        <span style="padding: 0 5px">&le; ecpm &lt;</span>
        <el-input-number v-model="item.ecpmHigh" :min="0" />
        <div v-if="ecpmErrors[index]" class="error-message">
          {{ ecpmErrors[index] }}
        </div>
      </div>
      <!--激励视频次数-->
      <div v-if="isShowInspireVideo" class="col rewarded-video">
        要求
        <el-input-number v-model="item.inspireVideoTimes" :min="0" />
        次激励视频
        <div v-if="inspireVideo[index]" class="error-message">
          {{ inspireVideo[index] }}
        </div>
      </div>
      <!--全屏视频次数-->
      <div v-if="isShowFullScreen" class="col full-video">
        {{ connectChar }}
        <el-input-number v-model="item.fullScreenTimes" :min="0" />
        次全屏视频
        <div v-if="fullScreenErrors[index]" class="error-message">
          {{ fullScreenErrors[index] }}
        </div>
      </div>
      <div v-if="!isSingle" class="col">
        <el-popconfirm
          v-if="dataForm.length > 1"
          title="确定删除吗？"
          @confirm="handleDel(index, item)"
          style="margin-right: 10px;"
        >
          <i
            slot="reference"
            style="color: #F56C6C; cursor: pointer"
            type="danger"
            class="el-icon-remove-outline"
          />
        </el-popconfirm>
        <!--dataForm.length - 1 === index && -->
        <i
          v-if="isCanAdd"
          style="color: #17B3A3; cursor: pointer"
          class="el-icon-circle-plus-outline"
          @click="handleAdd(index)"
        />
      </div>
    </div>
    <div></div>
    <div v-if="!isCanAdd" class="error-message">最多新增{{ maxLen }}条</div>
  </div>
</template>

<script>
import { returnGearCondition } from '@/map/common'

const getDataFormItem = () => ({
  // ecpm区间下限
  ecpmLow: '',
  // ecpm区间上限
  ecpmHigh: '',
  // 激励视频观看次数
  inspireVideoTimes: '',
  // 全屏视频观看次数
  fullScreenTimes: '',
})

export default {
  name: 'index',
  props: {
    gear: {
      type: Number,
      default:
        returnGearCondition.ecpm |
        returnGearCondition.rewardedVideo |
        returnGearCondition.fullScreen,
    },
    isSingle: {
      type: Boolean,
      default: false,
    },
    maxLen: {
      type: Number,
      default: 20,
    },
    connectChar: {
      type: String,
      default: '或',
    },
  },
  data() {
    return {
      dataForm: [getDataFormItem()],
      returnGearCondition,
      ecpmErrors: [],
      inspireVideo: [],
      fullScreenErrors: [],
    }
  },
  computed: {
    isCanAdd() {
      return this.dataForm.length < this.maxLen
    },
    isShowEcpm() {
      return this.gear & returnGearCondition.ecpm
    },
    isShowInspireVideo() {
      return this.gear & returnGearCondition.rewardedVideo
    },
    isShowFullScreen() {
      return this.gear & returnGearCondition.fullScreen
    },
  },
  watch: {
    dataForm: {
      handler(data) {
        this.$emit('change', data)
        this.ecpmErrors = Array(data.length).fill('')
        this.inspireVideo = Array(data.length).fill('')
        this.fullScreenErrors = Array(data.length).fill('')
        this.validate()
      },
      deep: true,
    },
  },
  methods: {
    handleAdd(index) {
      if (this.isCanAdd) {
        // this.dataForm.push(getDataFormItem())
        this.dataForm.splice(index + 1, 0, getDataFormItem())
      }
    },
    handleDel(index, item) {
      this.$emit('del', {
        data: item,
        index,
        callback: () => {
          this.dataForm.splice(index, 1)
        },
      })
    },
    setData(dataFn) {
      if (!dataFn) {
        this.dataForm = [getDataFormItem()]
        return
      }
      if (typeof dataFn !== 'function') {
        throw new Error('参数必须为函数')
      }
      this.dataForm = dataFn() || [getDataFormItem()]
    },
    getData() {
      return this.dataForm
    },
    clearData() {
      this.dataForm = this.setData(() => null)
    },
    validate() {
      const v1 = this.isShowEcpm ? this.handleValidateEcpm() : true
      const v2 = this.isShowInspireVideo
        ? this.handleValidateInspireVideo()
        : true
      const v3 = this.isShowFullScreen ? this.handleValidateFullScreen() : true

      return v1 && v2 && v3
    },
    handleValidateEcpm() {
      let ret = true
      for (let i = 0; i < this.dataForm.length; i++) {
        const { validate, message } = this.ecpmValidator(
          this.dataForm[i].ecpmLow,
          this.dataForm[i].ecpmHigh
        )
        if (!validate) {
          ret = false
          this.$set(this.ecpmErrors, i, message)
        }
      }
      return ret
    },
    handleValidateInspireVideo() {
      let ret = true
      for (let i = 0; i < this.dataForm.length; i++) {
        const { validate, message } = this.requiredValidator(
          this.dataForm[i].inspireVideoTimes,
          '激励视频次数不能为0'
        )
        if (!validate) {
          ret = false
          this.$set(this.inspireVideo, i, message)
        }
      }
      return ret
    },
    handleValidateFullScreen() {
      let ret = true
      for (let i = 0; i < this.dataForm.length; i++) {
        const { validate, message } = this.requiredValidator(
          this.dataForm[i].fullScreenTimes,
          '全屏视频次数不能为0'
        )
        if (!validate) {
          ret = false
          this.$set(this.fullScreenErrors, i, message)
        }
      }
      return ret
    },
    ecpmValidator(ecpmLow, ecpmHigh) {
      let validate = true
      let message = ''

      if (ecpmLow >= ecpmHigh) {
        message = '前值必须小于后值'
        validate = false
      } else if (!Number.isInteger(ecpmLow) || !Number.isInteger(ecpmHigh)) {
        message = '必须为整数'
        validate = false
      }

      return { validate, message }
    },
    requiredValidator(prop, msg = '必填') {
      let validate = true
      let message = ''
      if (!prop) {
        message = msg
        validate = false
      } else if (!Number.isInteger(prop)) {
        message = '必须为整数'
        validate = false
      }
      return { validate, message }
    },
  },
}
</script>

<style scoped lang="scss">
.row {
  display: flex;
  margin-bottom: 10px;

  & > .col + .col {
    margin-left: 30px;
  }
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
}
</style>
