<template>
  <el-form :model="dataForm" ref="dataForm" :disabled="disabled">
    <div v-if="!hideHeader" class="t-header">
      <h3>屏蔽规则</h3>
      <div>
        <el-button
          @click="addRules"
          plain
          type="primary"
          size="small"
          icon="el-icon-plus"
        >
          添加规则
        </el-button>
      </div>
    </div>
    <table class="rule-table">
      <thead>
        <tr>
          <th>屏蔽规则</th>
          <th>对比条件</th>
          <th>规则详情</th>
          <th v-if="!hideOperate">操作</th>
        </tr>
      </thead>
      <tbody>
        <template v-if="dataForm.ruleList && dataForm.ruleList.length">
          <tr v-for="(rule, index) in dataForm.ruleList" :key="rule.key">
            <td>
              <el-form-item
                label-width="0"
                :prop="'ruleList.' + index + '.rule'"
                :rules="{
                  required: true,
                  message: '屏蔽规则不能为空',
                }"
              >
                <el-select
                  v-model="rule.rule"
                  @change="_changeRule(index, rule.rule)"
                  style="width: 100%"
                >
                  <el-option
                    v-for="[key, value] in ruleType"
                    :key="key"
                    :label="value"
                    :value="key"
                    :disabled="_isRuleDisabled(key)"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item
                label-width="0"
                :prop="'ruleList.' + index + '.condition'"
                :rules="{
                  required: true,
                  message: '对比条件不能为空',
                }"
              >
                <el-select
                  v-model="rule.condition"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="[key, value] in _getConditionList(rule.rule)"
                    :key="key"
                    :label="value"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item
                label-width="0"
                :prop="'ruleList.' + index + '.ruleDetail'"
                :rules="{
                  required: true,
                  message: '规则详情不能为空',
                }"
              >
                <template
                  v-if="
                    rule.rule === 'city' ||
                      rule.rule === 'appVersion' ||
                      rule.rule === 'brand' ||
                      rule.rule === 'channel'
                  "
                >
                  <el-select
                    v-model="rule.ruleDetail"
                    filterable
                    multiple
                    clearable
                    style="width: 100%"
                    collapse-tags
                  >
                    <el-option
                      v-for="[key, value] in _getRuleDetail(rule.rule)"
                      :key="key"
                      :label="value"
                      :value="key"
                    />
                  </el-select>
                </template>
                <template v-else-if="rule.rule === 'activateTime'">
                  <el-input
                    v-model.trim="rule.ruleDetail"
                    type="number"
                    placeholder="激活时间(分钟)"
                    style="width: 100%"
                  />
                </template>
                <template v-else>
                  <el-input />
                </template>
              </el-form-item>
            </td>
            <td v-if="!hideOperate">
              <el-popconfirm
                title="确定删除规则吗？"
                @confirm="removeRule(rule)"
              >
                <el-button slot="reference" type="text" icon="el-icon-delete" />
              </el-popconfirm>
            </td>
          </tr>
        </template>
        <tr v-else>
          <td colspan="4">
            <el-empty description="暂无数据" />
          </td>
        </tr>
      </tbody>
    </table>
  </el-form>
</template>

<script>
import { ruleType, getConditionList, phoneBrand } from '@/map/common'
import city from '@/assets/js/city'

export default {
  name: 'RuleTable',
  props: {
    ruleList: {
      type: Array,
      // 必须是以下的格式
      default: () => [
        // {
        //   rule: 'city',
        //   condition: '',
        //   ruleDetail: '',
        // },
      ],
    },
    appVersionList: {
      type: Map,
    },
    channelList: {
      type: Map,
    },
    appId: {
      type: [Number, String],
    },
    hideHeader: {
      type: Boolean,
      default: false,
    },
    hideOperate: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleType,
      dataForm: {
        ruleList: this.ruleList,
      },
      // channelList: [],
      // appVersionList: [],
    }
  },
  watch: {
    ruleList: {
      immediate: true,
      handler(list) {
        // 这里直接引用了ruleList，
        // 所以直接操作dataForm.ruleList就会改变props中的ruleList的值
        // 由于这里的ruleList是Array，即使值改变了，引用地址也不会改变，
        this.dataForm.ruleList = list
      },
    },
  },
  mounted() {
    setTimeout(() => this.$refs.dataForm.clearValidate())
  },
  methods: {
    _getConditionList: getConditionList,
    _getRuleDetail(rule) {
      switch (rule) {
        case 'brand':
          return phoneBrand
        case 'city':
          return new Map(city.map(it => [it, it]))
        case 'channel':
          return this.channelList
        case 'appVersion':
          return this.appVersionList
        default:
          return []
      }
    },
    _changeRule(index, rule) {
      this.dataForm.ruleList[index].condition = ''
      this.dataForm.ruleList[index].ruleDetail = ''

      if (rule === 'appVersion') {
        this._getAppVersionList()
      } else if (rule === 'channel') {
        this._getChannelList()
      }
    },
    _getAppVersionList() {
      if (!this.appId) {
        return this.$emit('app-id-error')
      }
    },
    _getChannelList() {
      if (!this.appId) {
        return this.$emit('app-id-error')
      }
    },
    _isRuleDisabled(key) {
      return !!this.dataForm.ruleList.find(it => it.rule === key)
    },
    addRules() {
      this.dataForm.ruleList.push({
        rule: '',
        condition: '',
        ruleDetail: '',
        key: Date.now(),
      })
    },
    removeRule(item) {
      const index = this.dataForm.ruleList.indexOf(item)
      if (index !== -1) {
        this.dataForm.ruleList.splice(index, 1)
      }
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.dataForm.validate(valid =>
          valid ? resolve(this.dataForm.ruleList) : reject()
        )
      })
    },
    resetFields() {
      this.$refs.dataForm.resetFields()
    },
  },
}
</script>

<style scoped lang="scss">
.t-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-table {
  width: 100%;
  border-collapse: collapse;

  &::v-deep {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  thead {
    height: 40px;
    font-size: 14px;
    background-color: #eee;
  }

  th {
    color: #909399;
    &:before {
      content: '* ';
      color: red;
    }

    &:last-child:before {
      content: '';
    }
  }

  tr {
    border-bottom: 1px #eee solid;
  }

  td {
    padding: 15px 5px;
    text-align: center;
    vertical-align: middle;

    &:nth-of-type(4n) {
      width: 80px;
    }
  }
}
</style>
