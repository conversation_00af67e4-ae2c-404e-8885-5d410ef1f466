<template>
  <el-form ref="form" :model="formData" v-bind="$attrs">
    <el-form-item
      v-for="(item, index) in formData.list"
      :key="index"
      :prop="'list.' + index"
      :rules="[createValidator(item)]"
    >
      <div style="display: flex; margin-bottom: 10px;">
        <el-input
          v-for="(num, i) in columns"
          :key="i"
          v-model="formData.list[index][i]"
          clearable
          style="margin-right: 5px;"
          :placeholder="placeholderList[i]"
        />
        <el-button icon="el-icon-plus" @click="addContent(index)">
          新增
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          @click="removeContent(index)"
          :disabled="
            !isAllowDeleteAll && formData.list && formData.list.length <= 1
          "
        >
          删除
        </el-button>
      </div>
    </el-form-item>
    <el-form-item style="margin-bottom: 0">
      <el-button
        v-if="!formData.list || !formData.list.length"
        style="width: 100%; margin-bottom: 10px;"
        icon="el-icon-plus"
        @click="addContent(0)"
      >
        新增
      </el-button>
      <el-upload
        class="txt-uploader"
        action=""
        :auto-upload="false"
        :on-change="handleChange"
        accept=".txt"
        :show-file-list="false"
      >
        <div style="display: flex; align-items: center">
          <el-button icon="el-icon-upload" style="flex: 1; margin-right: 10px;">
            上传，只能上传txt，换行分割
          </el-button>
          <a
            @click.stop
            :href="require('@/assets/file/yuDanContentPlus.txt')"
            download="语弹内容模板.txt"
            class="el-icon-download"
          >
            下载模板
          </a>
        </div>
      </el-upload>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'index',
  props: {
    value: {
      type: Array,
      // default: () => [
      //   ['1', '12'],
      //   ['2', '12'],
      // ],
    },
    placeholderList: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Number,
      default: 1,
    },
    isAllowDeleteAll: {
      type: Boolean,
      default: true,
    },
    separator: {
      type: String,
      default: '-',
    },
  },
  computed: {
    formData() {
      return {
        list: this.value,
      }
    },
  },
  watch: {
    formData: {
      handler(data) {
        this.$emit('input', data.list)
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    addContent(index) {
      const arr = new Array(this.columns).fill('')
      this.formData.list.splice(index + 1, 0, arr)
    },
    removeContent(index) {
      this.formData.list.splice(index, 1)
    },
    handleChange(file) {
      const reader = new FileReader()
      reader.onload = () => {
        if (reader.result) {
          const list = reader.result.split('\n')
          const defaultArr = new Array(this.columns).fill('')
          const arr = list.map(it => it.split(this.separator))

          if (list && list.length) {
            this.formData.list.push(...Object.assign([], defaultArr, arr))
          }
        }
      }
      reader.readAsText(file.raw)
    },
    validate() {
      return this.$refs.form.validate()
    },
    createValidator(item) {
      return {
        validator: (rule, value, callback) => {
          if (item.some(it => !it)) {
            callback(new Error('不能为空'))
          } else {
            callback()
          }
        },
      }
    },
  },
}
</script>

<style scoped lang="scss">
.txt-uploader {
}
::v-deep {
  .el-upload {
    width: 100%;
  }
}
</style>
