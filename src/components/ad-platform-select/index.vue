<template>
  <map-select
    v-model="select"
    :list="adPlatformList"
    v-bind="$attrs"
    v-on="$listeners"
    clearable
  />
</template>

<script>
import { adPlatformList, adPlatformList2 } from '@/map/sat'

export default {
  props: {
    value: {
      type: [Number, String],
      // default: adPlatformList[0],
    },
    version: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      adPlatformList: this.version === '1' ? adPlatformList : adPlatformList2,
    }
  },
  computed: {
    select: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
}
</script>
