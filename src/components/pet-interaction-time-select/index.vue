<template>
  <el-form ref="form" :model="formData">
    <div v-for="(item, index) in formData.list" :key="index" class="t-box">
      <el-form-item>
        <vxe-button icon="el-icon-plus" @click="addConfig(index)">
          继续添加时间段
        </vxe-button>
        <vxe-button
          icon="el-icon-delete"
          @click="removeConfig(index)"
          :disabled="formData.list.length <= 1"
        >
          删除时间段
        </vxe-button>
      </el-form-item>
      <el-form-item
        v-if="showDateRange"
        :prop="'list.' + index + '.effectiveDate'"
        :rules="{
          required: true,
          message: '不能为空',
          trigger: 'blur',
        }"
      >
        <!--<el-date-picker-->
        <!--  v-model="item.effectiveDate"-->
        <!--  type="daterange"-->
        <!--  range-separator="至"-->
        <!--  start-placeholder="开始日期"-->
        <!--  end-placeholder="结束日期"-->
        <!--  style="width: 100%"-->
        <!--  value-format="yyyy-MM-dd"-->
        <!--  format="yyyy-MM-dd"-->
        <!--/>-->
        <el-select
          v-model="item.effectiveDate"
          style="width: 100%"
          placeholder="选择特殊节日"
        >
          <el-option
            v-for="(date, index) in specialDateList"
            :key="index"
            :value="date.code"
            :label="date.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :prop="'list.' + index + '.timeConfig'"
        :rules="createArrayRequiredValidate(index, 'timeConfig')"
      >
        <div class="list-box">
          <el-checkbox-group v-model="item.timeConfig">
            <div class="checkbox-item" v-for="(item, i) in 24" :key="i">
              <el-checkbox
                style="display: block"
                :label="timeRepairZero(i) + '-' + timeRepairZero(i + 1)"
                :disabled="
                  handleDisabled(index).includes(
                    timeRepairZero(i) + '-' + timeRepairZero(i + 1)
                  )
                "
              >
                {{ timeRepairZero(i) }} -
                {{ timeRepairZero(i + 1) }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item
        :prop="'list.' + index + '.times'"
        :rules="{
          required: true,
          message: '不能为空',
        }"
      >
        <el-input type="number" v-model.number="item.times" :min="0">
          <template #prepend>时间段</template>
          <template #append>次</template>
        </el-input>
      </el-form-item>
      <el-form-item :prop="'list.' + index + '.defaultContent'">
        <el-input
          v-model="item.defaultContent"
          placeholder="默认内容"
          clearable
        />
      </el-form-item>
      <el-form-item
        :prop="'list.' + index + '.contentList'"
        style="margin-bottom: 0;"
      >
        <add-input-plus
          ref="add-input"
          v-model="item.contentList"
          placeholder="请输入语句内容"
          :isAllowDeleteAll="false"
          :columns="columns"
          :placeholder-list="['键盘语句', 'app语句']"
        />
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
import AddInputPlus from '@/components/add-input-plus'
import { specialDateRequest } from '@/api/petkeyboard'

const columns = 2
const defaultContentList = new Array(columns).fill('')

export default {
  name: 'index',
  components: {
    AddInputPlus,
  },
  props: {
    showDateRange: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Array,
      default: () => [
        {
          effectiveDate: '',
          timeConfig: [],
          times: 0,
          defaultContent: '',
          contentList: [defaultContentList],
        },
      ],
    },
  },
  data() {
    return {
      columns: columns,
      specialDateList: [],
    }
  },
  created() {
    specialDateRequest
      .selectAll({
        currentPage: 1,
        pageSize: 10000,
      })
      .then(res => {
        if (res.page && res.page.list) {
          this.specialDateList = res.page.list
        } else {
          this.specialDateList = []
        }
      })
      .catch(() => {
        this.specialDateList = []
      })
  },
  computed: {
    formData: {
      get() {
        return {
          list: this.value,
        }
      },
    },
  },
  watch: {
    value: {
      handler(list) {
        this.$emit('input', list)
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    timeRepairZero(num) {
      return num < 10 ? `0${num}:00` : `${num}:00`
    },
    addConfig(index) {
      this.formData.list.splice(index + 1, 0, {
        effectiveDate: '',
        timeConfig: [],
        times: 0,
        contentList: [defaultContentList],
      })
    },
    removeConfig(index) {
      if (!this.formData.list || !this.formData.list.length) {
        return
      }
      this.formData.list.splice(index, 1)
    },
    createArrayRequiredValidate(index, attr) {
      return [
        {
          validator: (rule, value, callback) => {
            if (
              !this.formData.list[index][attr] ||
              !this.formData.list[index][attr].length
            ) {
              callback(new Error('不能为空'))
            } else {
              callback()
            }
          },
        },
      ]
    },
    validate() {
      return Promise.all([
        this.$refs.form.validate(),
        ...this.$refs['add-input'].map(it => it.validate()),
      ])
    },
    handleDisabled(index) {
      let arr = []

      this.formData.list.forEach((it, i) => {
        if (index !== i) {
          arr = arr.concat(it.timeConfig)
        }
      })

      return arr
    },
  },
}
</script>

<style lang="scss" scoped>
.list-box {
  height: 200px;
  padding: 10px 0;
  overflow: auto;
  border: 1px #eee solid;
}

.checkbox-item {
  display: inline-block;
  //display: block;
  padding: 5px 20px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #eee;
  }
}

.t-box {
  border: 1px #eee dashed;
  padding: 20px;
  margin-top: 10px;

  &.error {
    border-color: red;
  }
}
</style>
