<template>
  <el-select
    v-bind="$attrs"
    v-on="$listeners"
    v-model="selectData"
    :collapse-tags="collapseTags"
  >
    <div v-if="isShowTools" style="overflow: hidden; padding: 5px 20px;">
      <span style="float: left">
        <el-checkbox v-model="isSelectAll" @change="selectAll">
          全选
        </el-checkbox>
      </span>
      <span style="float: right;">
        <el-checkbox v-model="isSelectReverse" @change="selectReverse">
          反选
        </el-checkbox>
      </span>
    </div>
    <slot />
  </el-select>
</template>

<script>
import { difference } from 'lodash'

export default {
  props: {
    value: {
      type: [Array, String, Number],
    },
    // 源数据，就是下拉列表，目前只兼容数组
    dataList: {
      type: [Array],
    },
    // el-option 对应的value，如果不传，就默认为是 dataList 中的项目
    selectKey: {
      type: String,
    },
    isShowTools: {
      type: <PERSON>olean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isSelectReverse: false,
    }
  },
  computed: {
    selectData: {
      get() {
        return this.value
      },
      set(data) {
        this.emitData(data)
      },
    },
    allSelectData() {
      return this.selectKey
        ? this.dataList.map(it => it[this.selectKey])
        : [...this.dataList]
    },
    isSelectAll: {
      get() {
        return this.selectData.length === this.dataList.length
      },
      set() {},
    },
  },
  methods: {
    selectAll() {
      const list =
        this.selectData.length !== this.dataList.length
          ? this.allSelectData
          : []

      this.emitData(list)
      // 这里必须要手动触发一下 change 事件
      this.$emit('change', list)
    },
    selectReverse() {
      const list = difference(this.allSelectData, this.selectData)
      this.emitData(list)
      // 这里必须要手动触发一下 change 事件
      this.$emit('change', list)
    },
    emitData(data) {
      this.$emit('input', data)
    },
  },
}
</script>
