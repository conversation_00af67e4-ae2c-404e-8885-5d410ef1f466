<template>
  <el-select-extend
    v-model="cities"
    :data-list="list"
    clearable
    collapse-tags
    multiple
  >
    <el-option
      v-for="(item, index) in list"
      :key="index"
      :value="item"
      :label="item"
    />
  </el-select-extend>
</template>

<script>
import ElSelectExtend from '@/components/el-select-extend'
import mainCityList from '@/assets/js/mainCity'
import cityList from '@/assets/js/city'

export default {
  name: 'city-select',
  components: { ElSelectExtend },
  model: {
    prop: 'cityValue',
    event: 'change-city',
  },
  props: {
    isMain: {
      type: Boolean,
      default: true,
    },
    cityValue: {
      type: [Array, String],
    },
  },
  computed: {
    list() {
      return this.isMain ? mainCityList : cityList
    },
    cities: {
      set(cities) {
        this.$emit('change-city', cities)
      },
      get() {
        return this.cityValue
      },
    },
  },
}
</script>
