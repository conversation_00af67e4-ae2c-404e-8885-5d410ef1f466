<template>
  <div>
    <el-select
      v-model="appId"
      placeholder="请选择当前应用"
      v-bind="$attrs"
      v-on="$listeners"
      filterable
      :filter-method="filterMethod"
    >
      <template v-for="item in filteredDataList">
        <el-option
          v-if="!(!hasAll && !item.id)"
          :key="item.id"
          :value="item.id"
          :label="item.name"
        >
          <span style="float: left">{{ item.name }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">
            {{ item.code }}
          </span>
        </el-option>
      </template>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'AppSelect',
  props: {
    hasAll: {
      type: <PERSON>olean,
      default() {
        return this.$store.state.user.groupIdList.includes(0)
      },
    },
    autoInit: {
      type: Boolean,
      default: true,
    },
    withRole: {
      type: Boolean,
      default: true,
    },
    // 只有数据仓库需要传的参数
    isStore: {
      type: Boolean,
      default: false,
    },
    // app类型 : 1:工具,2:网赚,-1:不分类
    category: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      dataList: [],
      filteredDataList: [],
      filterText: '',
    }
  },
  computed: {
    showAll() {
      // 这两个账号不能显示全部
      const userNameList = this.$store.state.global.outUserNameList
      const userName = this.$store.state.user.name
      let showAll = this.hasAll

      if (userNameList.includes(userName)) {
        showAll = false
      }

      return showAll
    },
    appId: {
      get() {
        return this.$store.state.ad.appId
      },
      set(appId) {
        this.$store.commit('ad/updateAppId', appId)
      },
    },
  },
  watch: {
    appId(appId) {
      window.localStorage.setItem('store.ad.appId', appId)
    },
    dataList(newVal) {
      this.filteredDataList = [...newVal]
      this.filterMethod(this.filterText)
    },
  },
  created() {
    this.autoInit && this._getAppList()
  },
  methods: {
    _getAppList() {
      const params = {
        page: 1,
        limit: 100,
      }
      const action = this.isStore
        ? 'api/app/getAppListWarehouse'
        : 'api/app/getAppListWithRole'

      this.$store
        .dispatch(action, params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            if (this.category !== -1) {
              this.dataList = data.apps.filter(
                it => it.category === this.category
              )
            } else {
              this.dataList = data.apps
            }

            this.dataList.unshift({
              id: null,
              name: '全部应用',
              code: 0,
            })
            this.$store.commit('ad/setAppList', this.dataList)
          }
        })
        .finally(() => this._initAppId())
    },
    _initAppId() {
      let appId = window.localStorage.getItem('store.ad.appId')

      if (appId) {
        let id = 0
        const isInAppList = this.dataList.find(it => it.id == appId)
        const isEmpty =
          appId === 'undefined' || appId === 'null' || appId === 'NaN'
        const isNull = isEmpty || !isInAppList

        if (!this.showAll) {
          id = isNull ? this.dataList[1].id : Number(appId)
        } else {
          id = isNull ? null : Number(appId)
        }

        this.$store.commit('ad/updateAppId', id)
      } else {
        this.$store.commit(
          'ad/updateAppId',
          this.dataList[this.showAll ? 0 : 1].id
        )
      }

      // 加个异步，不知道为啥刷新页面的时候偶尔会丢失数据
      setTimeout(() => this.$emit('init-app-id'))
    },
    init() {
      this._getAppList()
    },
    changeAppList(list) {
      this.dataList = list
      this.filteredDataList = [...list]
      if (list && list.length) {
        if (this.showAll) {
          this.appId = list[0].id
        } else {
          this.appId = list[0].id === null ? list[1].id : list[0].id
        }

        this.$emit('change', this.appId)
      } else {
        this.appId = null
      }
      console.log('过滤后的数据', this.dataList)
    },
    // 过滤方法
    filterMethod(query) {
      console.log('query', query)
      this.filterText = query
      if (query) {
        // let lowerQuery = query.toLowerCase()
        this.filteredDataList = this.dataList.filter(item => {
          return (
            (item.name && item.name.includes(query)) ||
            (item.code && item.code.toString().includes(query))
          )
        })
      } else {
        this.filteredDataList = [...this.dataList]
      }
    },
  },
}
</script>
