<template>
  <div>
    <el-form-item label="是否添加条件">
      <el-select v-model="hasLimit" @change="emitData" style="width: 100%">
        <el-option label="无限制" :value="1" />
        <el-option label="有限制" :value="2" />
      </el-select>
    </el-form-item>
    <div v-if="hasLimit !== 1" class="item-box">
      <div v-for="(item, index) in limitList" :key="index">
        <el-form-item :label="item.label">
          <div>
            <!--style="display: inline-block; width: 140px;"-->
            <el-input
              v-show="!item.hideLimitNum"
              type="number"
              :min="0"
              :max="item.max"
              v-model.number="item.limitNum"
              :placeholder="item.placeholder"
              :disabled="item.limitNumDisabled"
              clearable
              @blur="handleBlur(item)"
            />
            <!--style="display: inline-block; width: 140px; margin-left: 10px;"-->
            <el-input
              v-if="item.needWID"
              type="number"
              :min="0"
              v-model.number="item.withdrawalId"
              placeholder="提现配置ID"
              clearable
            />
          </div>
        </el-form-item>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'withdraw-limit',
  props: {
    // [{limitType: 3, limitNum: 100, withdrawalId: null}]
    limitData: {
      type: Array,
    },
  },
  data() {
    return {
      hasLimit: 2,
      limitList: [
        {
          limitType: 2,
          limitNum: null,
          withdrawalId: null,
          label: '任务完成要求',
          placeholder: '次数',
          max: 1,
        },
        {
          limitType: 3,
          limitNum: null,
          withdrawalId: null,
          label: '视频观看次数要求',
          placeholder: '次数',
        },
        {
          limitType: 4,
          limitNum: null,
          withdrawalId: null,
          label: '累计打卡限制',
          placeholder: '次数',
        },
        {
          limitType: 6,
          limitNum: 1,
          withdrawalId: null,
          label: '提现一次x元元宝',
          placeholder: '元',
          needWID: true, // 是否需要填写 withdrawalId
          limitNumDisabled: true, // 是否禁止填写 limitNum
          hideLimitNum: true, // 是否隐藏 limitNum 的框
        },
      ],
    }
  },
  watch: {
    limitList: {
      handler() {
        this.emitData()
      },
      deep: true,
      // immediate: true
    },
    limitData: {
      handler(data) {
        if (data && data.length) {
          this.setLimitList(data)
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    emitData() {
      const data = this.buildData()
      this.$emit('change', data)
    },
    setLimitList(list) {
      if (!list || !list.length) {
        this.hasLimit = 1
        return
      }

      if (list.find(it => it.limitType === 1)) {
        this.hasLimit = 1
        return
      }

      list.forEach(it => {
        const limitType = it.limitType
        const limitNum = it.limitNum
        const withdrawalId = it.withdrawalId

        const index = this.limitList.findIndex(it => it.limitType === limitType)
        if (index !== -1) {
          this.limitList[index]['limitNum'] = limitNum
          if (this.limitList[index]['needWID']) {
            this.limitList[index]['withdrawalId'] = withdrawalId
          }
        }
      })
    },
    buildData() {
      if (this.hasLimit === 1) {
        return [{ limitType: 1, limitNum: 0 }]
      }
      // 目标数据结构 [{limitType: 3, limitNum: 100, withdrawalId: null}]
      return this.limitList
        .filter(it => {
          if (it.needWID) {
            return it.withdrawalId
          }
          return it.limitNum
        })
        .map(it => {
          const item = {
            limitType: it.limitType,
            limitNum: it.limitNum,
          }

          if (it.needWID) {
            item.withdrawalId = it.withdrawalId
          }

          return item
        })
    },
    handleBlur(item) {
      if (item.max) {
        item.limitNum = Math.min(item.limitNum, item.max)
      }
    },
  },
}
</script>

<style scoped></style>
