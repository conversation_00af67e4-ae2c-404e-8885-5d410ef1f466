<template>
  <Wrapper title="分时累计留存率">
    <Charts
      :request-params="requestParams"
      @request-finish="$emit('request-finish')"
    />
  </Wrapper>
</template>

<script>
import Charts from './charts'
import { wrapperMixin } from '../mixins'

export default {
  mixins: [wrapperMixin],
  props: {
    // 请求参数
    requestParams: {
      type: Object,
      default: () => ({
        appCode: 0,
        groupId: 0,
        mediaType: '', // 媒体类型：0APK 1穿山甲/2优量汇/3快手/4百度
        brand: '', // 设备品牌：
        advertiseId: '', // 广告组ID
        marketCode: '', //  市场代码
        channel: '', // 海外渠道
      }),
    },
  },
  components: {
    Charts,
  },
}
</script>
