<template>
  <m-charts :option="option" :loading="loading" />
</template>

<script>
import { chartsMixin } from '../mixins'

export default {
  mixins: [chartsMixin],
  methods: {
    getData() {
      this.loading = true
      this.$store
        .dispatch('api/charts/getBriefingInTimeKeepList', {
          params: {
            // appCode: this.appCode,
            // groupId: this.groupId,
            ...this.requestParams,
          },
        })
        .then(({ data }) => {
          if (data.code !== 0) {
            this.option.series = null
            return this.$message.error(data.msg || '服务器错误')
          }

          if (data.data) {
            this.option = this.handleHourLine(data)
          } else {
            this.option.series = null
          }
        })
        .catch(() => {
          this.option.series = null
        })
        .finally(() => {
          this.loading = false
          this.$emit('request-finish')
        })
    },
  },
}
</script>
