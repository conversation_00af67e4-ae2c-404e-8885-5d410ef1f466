export function handleHourLine(
  data,
  seriesConfig = [
    { name: '昨日', type: 'line', attr: 'yesTodayPer' },
    { name: '今日', type: 'line', attr: 'todayPer' },
  ],
  yAxisName = '单位(%)'
) {
  const result = data.data
  const getData = attr => result.map(it => it[attr])

  const series = seriesConfig.map(it => ({
    name: it.name,
    type: it.type,
    smooth: true,
    showSymbol: false,
    data: getData(it.attr).map(it =>
      Number.isInteger(it) ? it : it.toFixed(2)
    ),
  }))

  return {
    series,
    xAxis: {
      type: 'category',
      data: data.data.map(it => it.hour).concat(24),
    },
    yAxis: {
      name: yAxisName,
    },
    legend: {
      data: series.map(it => it.name),
    },
  }
}
