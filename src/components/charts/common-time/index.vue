<template>
  <Wrapper>
    <template v-slot:header>
      <div class="header-box">
        <span>{{ title }}</span>
        <div class="operator">
          <span @click="chartType = 1" :class="[{ active: chartType === 1 }]">
            实时累计
          </span>
          <span @click="chartType = 2" :class="[{ active: chartType === 2 }]">
            实时小时
          </span>
        </div>
      </div>
    </template>
    <m-charts :option="option" :loading="loading" />
  </Wrapper>
</template>

<script>
import MCharts from '@/components/m-charts'
import { wrapperMixin } from '../mixins'
import { handleHourLine } from '@/components/charts/tools'
import { chartTypeList } from '@/map/store'

export default {
  mixins: [wrapperMixin],
  components: {
    MCharts,
  },
  props: {
    title: {
      type: String,
    },
    type: {
      type: String,
      validator: function(value) {
        // 这个值必须匹配下列字符串中的一个
        return ['ARPU', 'ECPM', 'AIPU', 'ImpRate'].indexOf(value) !== -1
      },
    },
  },
  data() {
    return {
      option: {},
      loading: false,
      chartTypeList,
      chartType: 1,
      config: {
        ARPU: {
          dispatchType: 'api/charts/getARPUList',
          seriesConfig: [
            { name: '昨日', type: 'line', attr: 'yesterdayArpu' },
            { name: '今日', type: 'line', attr: 'todayArpu' },
          ],
          yAxisName: '',
        },
        ECPM: {
          dispatchType: 'api/charts/getECPMList',
          seriesConfig: [
            { name: '昨日', type: 'line', attr: 'yesterdayEcpm' },
            { name: '今日', type: 'line', attr: 'todayEcpm' },
          ],
          yAxisName: '',
        },
        AIPU: {
          dispatchType: 'api/charts/getAIPUList',
          seriesConfig: [
            { name: '昨日', type: 'line', attr: 'yesterdayAipu' },
            { name: '今日', type: 'line', attr: 'todayAipu' },
          ],
          yAxisName: '',
        },
        ImpRate: {
          dispatchType: 'api/charts/getImpRateList',
          seriesConfig: [
            { name: '昨日', type: 'line', attr: 'yesterdayImpRate' },
            { name: '今日', type: 'line', attr: 'todayImpRate' },
          ],
          yAxisName: '',
        },
      },
    }
  },
  computed: {
    finalRequestParams() {
      return {
        ...this.requestParams,
        chartType: this.chartType, //1实时累计2实时小时
      }
    },
    seriesConfig() {
      return this.config[this.type].seriesConfig
    },
    yAxisName() {
      return this.config[this.type].yAxisName
    },
    dispatchType() {
      return this.config[this.type].dispatchType
    },
  },
  watch: {
    finalRequestParams: {
      immediate: true,
      deep: true,
      handler() {
        if (!this.loading) {
          this.getData()
        }
      },
    },
    loading: {
      handler(v) {
        this.$emit('change-loading', v)
      },
      immediate: true,
    },
  },
  methods: {
    getData() {
      this.loading = true
      this.$store
        .dispatch(this.dispatchType, {
          params: {
            ...this.finalRequestParams,
          },
        })
        .then(({ data }) => {
          if (data.code !== 0) {
            this.option.series = null
            return this.$message.error(data.msg || '服务器错误')
          }

          if (data.data) {
            this.option = handleHourLine(
              data,
              this.seriesConfig,
              this.yAxisName
            )
          } else {
            this.option.series = null
          }
        })
        .catch(() => {
          this.option.series = null
        })
        .finally(() => {
          this.loading = false
          this.$emit('request-finish')
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.header-box {
  display: flex;
  justify-content: space-between;
}

.operator {
  display: flex;

  span + span {
    margin-left: 10px;
  }

  span {
    cursor: pointer;
    color: #9a9a9a;

    &:hover {
      color: #75d7e8;
    }

    &.active {
      color: #138093;
    }
  }
}
</style>
