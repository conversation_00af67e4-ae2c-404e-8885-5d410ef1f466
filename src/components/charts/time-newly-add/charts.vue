<template>
  <m-charts :option="option" :loading="loading" />
</template>

<script>
import { chartsMixin } from '../mixins'

export default {
  mixins: [chartsMixin],
  data() {
    return {
      cancelList: {},
    }
  },
  methods: {
    getData() {
      this.loading = true
      this.$store
        .dispatch('api/charts/getBriefingInTimeNewlyAddList', {
          params: {
            ...this.requestParams,
          },
        })
        .then(({ data }) => {
          if (data.code !== 0) {
            this.option.series = null
            return this.$message.error(data.msg || '服务器错误')
          }

          if (data.data) {
            this.option = this.handleHourLine(
              data,
              [
                { name: '昨日', type: 'line', attr: 'yesTodayNum' },
                { name: '今日', type: 'line', attr: 'todayNum' },
              ],
              ''
            )
            // 海外的不需要重置为0
            if (
              this.option.series[1] &&
              this.option.series[1].data &&
              this.requestParams.groupId !== 12
            ) {
              this.option.series[1].data[new Date().getHours()] = 0
            }
          } else {
            this.option.series = null
          }
        })
        .catch(() => {
          this.option.series = null
        })
        .finally(() => {
          this.loading = false
          this.$emit('request-finish')
        })
    },
  },
}
</script>
