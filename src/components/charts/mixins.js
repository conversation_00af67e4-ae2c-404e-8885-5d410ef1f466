import MCharts from '@/components/m-charts'
import { handleHourLine } from '@/components/charts/tools'
import Wrapper from './wrapper'

const requestParamsDefaultValue = {
  appCode: 0,
  groupId: 0,
  mediaType: '', // 媒体类型：0APK 1穿山甲/2优量汇/3快手/4百度
  brand: '', // 设备品牌：
  advertiseId: '', // 广告组ID
  marketCode: '', //  市场代码
  channel: '', // 海外渠道
}

const requestParamsProps = {
  type: Object,
  default: () => ({
    ...requestParamsDefaultValue,
  }),
}

export const chartsMixin = {
  components: {
    MCharts,
  },
  props: {
    appCode: {
      type: [String, Number],
      default: null,
    },
    groupId: {
      type: [String, Number],
      default: null,
    },
    // 请求参数
    requestParams: requestParamsProps,
  },
  data() {
    return {
      option: {},
      loading: false,
    }
  },
  watch: {
    appCode: {
      handler(v) {
        if (!this.loading && v !== null && v !== '') {
          // this.getData()
        }
      },
      immediate: true,
    },
    groupId: {
      handler(v) {
        if (!this.loading && v !== null && v !== '') {
          // this.getData()
        }
      },
      immediate: true,
    },
    requestParams: {
      immediate: true,
      handler(v) {
        if (!this.loading && v) {
          this.getData()
        }
      },
      deep: true,
    },
    loading: {
      handler(v) {
        this.$emit('change-loading', v)
      },
      immediate: true,
    },
  },
  methods: {
    getData() {
      throw new Error('组件内自行实现')
    },
    handleHourLine() {
      return handleHourLine.apply(null, arguments)
    },
  },
}

export const wrapperMixin = {
  components: {
    Wrapper,
  },
  props: {
    appCode: {
      type: [String, Number],
    },
    groupId: {
      type: [String, Number],
    },
    // 请求参数
    requestParams: requestParamsProps,
  },
  methods: {
    changeLoading(v) {
      this.$emit('change-loading', v)
    },
  },
}

// 选择条件
export const selectMixin = {
  props: {
    // 媒体类型：0APK 1穿山甲/2优量汇/3快手/4百度
    mediaType: {
      type: [String, Number],
    },
    // 设备品牌：
    brand: {
      type: [String, Number],
    },
    // 广告组ID
    advertiseId: {
      type: [String, Number],
    },
    //  市场代码
    marketCode: {
      type: [String, Number],
    },
  },
}
