<!--
  原本是海外成本表
-->
<template>
  <div>
    <page-table
      :grid-config="gridOptions"
      :request-collection="request"
      :model-config="modelConfig"
      :features="features"
      operate-width="120"
      :before-select="beforeSelect"
      :show-operate="showOperate"
    >
      <template #extend_submit_item>
        <vxe-button
          content="上传"
          status="primary"
          icon="el-icon-upload"
          @click="visibleUploadDialog = true"
        />
      </template>
      <template #form_appCode>
        <app-select-component
          v-model="selectFormData.appCode"
          :is-show-all="false"
          clearable
          multiple
          collapse-tags
          :app-filter="appFilter"
          :width="300"
        />
      </template>
      <template #form_day>
        <el-date-picker
          v-model="selectFormData.day"
          type="date"
          placeholder="选择日期"
          value-format="yyyyMMdd"
        />
      </template>
      <template #form_country>
        <country-select v-model="selectFormData.country" />
      </template>
      <template #form_adPlatform>
        <ad-platform-select v-model="selectFormData.adPlatform" />
      </template>
      <template #table_item_appCode="{row}">
        <color-tag :id="row.appCode">{{ row.appCode | getAppName }}</color-tag>
      </template>
      <template #table_item_appCode_text="{row}">
        {{ row.appCode | getAppName }}
      </template>
      <template #table_item_day="{row}">
        <span>{{ row.day | formatDate }}</span>
      </template>
      <template #table_item_icon="{row}">
        <img width="80" :src="row.icon" alt="" />
      </template>
      <template #model>
        <el-form-item label="应用" prop="appCode">
          <app-select-component
            v-model="modelConfig.formData.appCode"
            clearable
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="日期" prop="day">
          <el-date-picker
            v-model="modelConfig.formData.day"
            type="date"
            placeholder="选择日期"
            value-format="yyyyMMdd"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="广告主ID" prop="advertiserId">
          <el-input
            v-model="modelConfig.formData.advertiserId"
            placeholder="广告主ID"
          />
        </el-form-item>
        <el-form-item label="广告组ID" prop="adgroupId">
          <el-input
            v-model="modelConfig.formData.adgroupId"
            placeholder="广告组ID"
          />
        </el-form-item>
        <el-form-item label="国家" prop="country">
          <country-select
            v-model="modelConfig.formData.country"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="成本" prop="cost">
          <el-input
            type="number"
            v-model="modelConfig.formData.cost"
            placeholder="成本"
          />
        </el-form-item>
        <el-form-item label="广告展现次数" prop="impressions">
          <el-input
            type="number"
            v-model="modelConfig.formData.impressions"
            placeholder="广告展现次数"
          />
        </el-form-item>
        <el-form-item label="转化" prop="conversion">
          <el-input
            type="number"
            v-model="modelConfig.formData.conversion"
            placeholder="转化"
          />
        </el-form-item>
        <el-form-item label="投放平台" prop="adPlatform">
          <ad-platform-select
            v-model="modelConfig.formData.adPlatform"
            style="width: 100%;"
          />
        </el-form-item>
      </template>
    </page-table>
    <el-dialog
      :visible.sync="visibleUploadDialog"
      width="400px"
      @closed="resetForm"
    >
      <el-form ref="upload-form" :model="uploadForm" :rules="rules">
        <el-form-item label="类型" prop="type">
          <arr-select
            :list="typeList"
            v-model="uploadForm.type"
            label-key="label"
            value-key="value"
            clearable
          />
        </el-form-item>
        <el-form-item label="文件" prop="file">
          <el-upload
            action=""
            :on-change="changeFile"
            :auto-upload="false"
            :multiple="false"
            :before-remove="beforeRemoveFile"
            :file-list="fileList"
          >
            <el-button type="primary" icon="el-icon-upload">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="visibleUploadDialog = false">取消</el-button>
        <el-button @click="submitUpload" type="primary">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { hwCostDailyRequest as request } from '@/api/stat'
import CountrySelect from '@/components/country-select'
import AdPlatformSelect from '@/components/ad-platform-select'

export default {
  props: {
    isRoas: {
      type: Boolean,
      default: false,
    },
    showAdPlatform: {
      type: Boolean,
      default: true,
    },
    adPlatform: {
      type: String,
    },
    features: {
      default: () => ['select', 'insert', 'update'],
    },
    showOperate: {
      type: Boolean,
      default: true,
    },
    columns: {
      type: Array,
      default: () => [
        // { type: 'checkbox', width: 35 },
        { type: 'seq', title: '序号', minWidth: 60 },
        {
          field: 'appCode',
          title: '应用',
          minWidth: 140,
          slots: { default: 'table_item_appCode' },
        },
        {
          field: 'day',
          title: '日期',
          minWidth: 100,
          slots: { default: 'table_item_day' },
        },
        { field: 'advertiserId', title: '广告主ID', minWidth: 60 },
        { field: 'adgroupId', title: '广告组ID', minWidth: 60 },
        { field: 'country', title: '国家', minWidth: 60 },
        { field: 'cost', title: '成本', minWidth: 60 },
        { field: 'impressions', title: '广告展现次数', minWidth: 80 },
        { field: 'conversion', title: '转化', minWidth: 60 },
        { field: 'adPlatform', title: '投放平台', minWidth: 60 },
        { field: 'createdAt', title: '创建时间', minWidth: 150 },
        { field: 'updatedAt', title: '更新时间', minWidth: 150 },
      ],
    },
    rowStyle: {
      type: Function,
      default: () => {
        return () => {}
      },
    },
    appFilter: {
      type: Function,
      default: () => () => true,
    },
  },
  components: {
    AdPlatformSelect,
    CountrySelect,
  },
  data() {
    return {
      fileList: [],
      uploadForm: {
        type: null,
        file: null,
      },
      typeList: [
        { label: '账号数据', value: 1 },
        { label: '分国家数据', value: 2 },
      ],
      visibleUploadDialog: false,
      fishList: [],
      gridOptions: {
        columns: this.columns,
        formConfig: {
          items: [
            { title: '应用', slots: { default: 'form_appCode' } },
            { title: '日期', slots: { default: 'form_day' } },
            { title: '国家', slots: { default: 'form_country' } },
          ].concat(
            this.showAdPlatform
              ? [{ title: '投放平台', slots: { default: 'form_adPlatform' } }]
              : []
          ),
        },
        rowStyle: this.rowStyle,
      },
      modelConfig: {
        modelConfig: { width: '500px' },
        formConfig: { labelWidth: '110px' },
        formData: {
          id: null,
          appCode: '',
          day: '',
          advertiserId: '',
          adgroupId: '',
          country: '',
          cost: '',
          impressions: '',
          conversion: '',
          adPlatform: '',
        },
        adapterResData: data => {
          return {
            ...data,
            day: data.day.toString(),
          }
        },
        adapterFormData: formData => {
          return {
            ...formData,
            day: Number(formData.day),
          }
        },
        formRule: {
          appCode: [{ required: true, message: '必传' }],
          day: [{ required: true, message: '必传' }],
          advertiserId: [{ required: true, message: '必传' }],
          adgroupId: [{ required: true, message: '必传' }],
          country: [{ required: true, message: '必传' }],
          cost: [{ required: true, message: '必传' }],
          impressions: [{ required: true, message: '必传' }],
          conversion: [{ required: true, message: '必传' }],
          adPlatform: [{ required: true, message: '必传' }],
        },
      },
      selectFormData: {
        appCode: null,
        day: null,
        country: null,
        adPlatform: this.adPlatform,
      },
      beforeSelect: () => {
        const appCode =
          this.selectFormData.appCode instanceof Array
            ? this.selectFormData.appCode.join()
            : this.selectFormData.appCode

        return {
          ...this.selectFormData,
          appCode,
        }
      },
      rules: {
        type: [{ required: true, message: '必传' }],
        file: [{ required: true, message: '必传' }],
      },
    }
  },
  computed: {
    request() {
      return this.isRoas
        ? {
            ...request,
            selectAll: request.selectRoasList,
          }
        : request
    },
  },
  methods: {
    changeFile(file) {
      this.fileList = [file]
      this.uploadForm.file = this.fileList[0]
      this.$refs['upload-form'].validateField('file')
    },
    submitUpload() {
      this.$refs['upload-form'].validate().then(() => {
        const data = new FormData()
        data.append('type', this.uploadForm.type)
        data.append('file', this.fileList[0].raw)
        request
          .uploadGoogleCost(data)
          .then(res => {
            if (res.code === 0) {
              this.$message.success('上传成功')
              this.visibleUploadDialog = false
            } else {
              this.$message.success('上传失败')
            }
          })
          .catch(e => {
            this.$message.success(e.message || '上传失败')
          })
      })
    },
    beforeRemoveFile() {
      this.fileList = []
      this.uploadForm.file = null
      this.$refs['upload-form'].validateField('file')
      return true
    },
    resetForm() {
      this.$refs['upload-form'].resetFields()
      this.uploadForm.type = null
      this.uploadForm.file = null
      this.fileList = []
    },
  },
}
</script>
