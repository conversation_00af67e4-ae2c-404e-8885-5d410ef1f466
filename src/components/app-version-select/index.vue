<template>
  <el-select-extend
    v-model="versions"
    :multiple="multiple"
    :collapse-tags="collapseTags"
    :is-show-tools="isShowTools"
    :data-list="list"
    :select-key="selectKey"
    clearable
  >
    <el-option
      v-for="(item, index) in list"
      :key="index"
      :value="item[selectKey]"
      :label="item.versionName"
    />
  </el-select-extend>
</template>

<script>
import ElSelectExtend from '@/components/el-select-extend'
import { getAllAppVersion } from '@/repository/app'
export default {
  name: 'app-version-select',
  components: { ElSelectExtend },
  model: {
    prop: 'versionsValue',
    event: 'change-versions',
  },
  props: {
    appId: {
      type: [Number, String],
    },
    versionsValue: {
      type: [Array, Number, String],
    },
    isShowTools: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
    selectKey: {
      type: String,
      default: 'versionCode',
    },
  },
  data() {
    return {
      list: [],
      // selectKey: 'versionCode',
    }
  },
  computed: {
    versions: {
      set(versions) {
        this.$emit('change-versions', versions)
      },
      get() {
        return this.versionsValue
      },
    },
  },
  watch: {
    appId: {
      handler(appId) {
        getAllAppVersion(appId).then(list => (this.list = list))
      },
      immediate: true,
    },
  },
  methods: {
    getVersionList() {
      return this.list
    },
  },
}
</script>

<style scoped></style>
