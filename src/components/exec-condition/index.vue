<!--
  优化：provide/inject 会更方便一些
-->
<template>
  <div>
    <component
      v-for="(item, index) in renderComponentWithCondition"
      :ref="`${item}-${index}`"
      :is="item"
      :key="index"
      style="margin-bottom: 10px; display: flex"
    />
  </div>
</template>

<script>
import Con1 from './con1'
import Con2 from './con2'
import Con3 from './con3'
import Key3 from './key3'
import Condition from './Condition'
import { con1List, con2List } from '@/map/common'

export default {
  name: 'index',
  components: {
    Con1,
    Con2,
    Con3,
    Key3,
    Condition,
  },
  props: {
    renderComponents: {
      type: Array,
      default: () => ['Con1', 'Con2', 'Con3'],
      validator(arr) {
        return arr.every(it => ['Con1', 'Con2', 'Con3', 'Key3'].includes(it))
      },
    },
    extra: {
      type: Array,
    },
  },
  computed: {
    renderComponentWithCondition() {
      const arr = []
      const mid = 'Condition'
      this.renderComponents.forEach((it, index) => {
        if (index === this.renderComponents.length - 1) {
          arr.push(it)
        } else {
          arr.push(it, mid)
        }
      })
      return arr
    },
  },
  methods: {
    // 生成描述：$到首页 >= $1 $且 $广告曝光次数 >= $2 $或 $广告曝光ECPM >= $3
    buildDescribe() {
      return this.buildInfo().describe
    },
    // 附加信息: ["page_show", 0, "AND", "advertise_exposure", 3, "OR", "exposure_ecpm", 3]
    buildExtraInfo() {
      return this.buildInfo().extraInfo
    },
    // 生成条件: page_show >= $1 AND advertise_exposure >= $2 OR exposure_ecpm >= $3
    buildCondition() {
      return this.buildInfo().conditionStr
    },
    buildInfo() {
      const allCondition = new Map([...con1List, ...con2List])
      const extraInfo = []
      let conditionStr = ''
      let describe = ''

      this.renderComponentWithCondition.forEach((it, index) => {
        const dataForm = this.$refs[`${it}-${index}`][0].dataForm
        const condition = this.$refs[`${it}-${index}`][0].condition
        if (dataForm) {
          const { key, value } = dataForm
          extraInfo.push(key, value)
          conditionStr += `${key} >= ${value} `
          // 关键指标得特殊处理
          if (it === 'Key3') {
            describe += `${key.replace(
              'key_behavior',
              '关键指标'
            )} >= ${value} `
          } else {
            const label = allCondition.get(key)
            const unit =
              label && label.toUpperCase().includes('ECPM') ? '' : '次'
            describe += `${label} >= ${value}${unit} `
          }
        } else {
          extraInfo.push(condition)
          conditionStr += ` ${condition} `
          describe += ` ${
            this.$refs[`${it}-${index}`][0].conditionList[condition]
          } `
        }
      })

      return {
        describe,
        extraInfo,
        conditionStr,
      }
    },
    resetValue() {
      this.renderComponentWithCondition.forEach((it, index) => {
        if (this.$refs[`${it}-${index}`][0].dataForm) {
          this.$refs[`${it}-${index}`][0].dataForm.key = ''
          this.$refs[`${it}-${index}`][0].dataForm.value = ''
        } else {
          this.$refs[`${it}-${index}`][0].condition = ''
        }
      })
    },
    // 源数据格式：["device_ping", "1", "AND", "key_avg_ecpm", "222", "AND", "key_ecpm", "33"]
    // 转为：[["device_ping", "1"],["AND"],["key_avg_ecpm", "222"],["AND"],["key_ecpm", "33"]]
    setValue(extra) {
      const list = []
      for (let index = 0; index < extra.length; index++) {
        const item = extra[index]
        if (item === 'AND' || item === 'OR') {
          list.push([item])
        } else {
          list.push([extra[index], extra[index + 1]])
          index += 1
        }
      }
      this.renderComponentWithCondition.forEach((it, index) => {
        if (this.$refs[`${it}-${index}`][0].dataForm) {
          this.$refs[`${it}-${index}`][0].dataForm.key = list[index][0]
          this.$refs[`${it}-${index}`][0].dataForm.value = list[index][1]
        } else {
          this.$refs[`${it}-${index}`][0].condition = list[index][0]
        }
      })
    },
  },
}
</script>
