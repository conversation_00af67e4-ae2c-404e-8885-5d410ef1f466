<template>
  <div>
    <el-select v-model="condition">
      <el-option
        v-for="(label, key) in conditionList"
        :key="key"
        :value="key"
        :label="label"
      />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'Condition',
  data() {
    return {
      conditionList: {
        OR: '或',
        AND: '且',
      },
      condition: '',
    }
  },
}
</script>

<style scoped></style>
