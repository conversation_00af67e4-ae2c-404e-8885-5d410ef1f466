<template>
  <div>
    <!--条件3-->
    <el-select v-model="dataForm.key" placeholder="条件3">
      <el-option
        v-for="[key, label] in con2List"
        :key="key"
        :label="label"
        :value="key"
      />
    </el-select>
    <span style="padding: 0  5px;  font-size: 12px">大于等于</span>
    <el-input
      v-model.trim="dataForm.value"
      :min="0"
      type="number"
      style="width: 170px"
    />
  </div>
</template>

<script>
import { con2List } from '@/map/common'

export default {
  name: 'con2',
  data() {
    return {
      dataForm: {
        key: '',
        value: '',
      },
      con2List,
    }
  },
}
</script>

<style scoped></style>
