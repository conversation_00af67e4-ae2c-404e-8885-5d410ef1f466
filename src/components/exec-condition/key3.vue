<template>
  <div>
    <!--关键指标-->
    <el-select v-model="dataForm.key" placeholder="关键指标">
      <el-option
        v-for="item in keyBehaviorList"
        :key="item.key"
        :label="item.label"
        :value="item.key"
      />
    </el-select>
    <span style="padding: 0  5px;font-size: 12px">大于等于</span>
    <el-input
      v-model.trim="dataForm.value"
      :min="0"
      type="number"
      style="width: 170px"
    />
  </div>
</template>

<script>
export default {
  name: 'con2',
  data() {
    return {
      dataForm: {
        key: '',
        value: '',
      },
    }
  },
  computed: {
    keyBehaviorList() {
      const arr = []
      for (let i = 1; i <= 5; i++) {
        arr.push({
          key: `key_behavior_${i}`,
          label: i,
        })
      }
      return arr
    },
  },
}
</script>

<style scoped></style>
