<!--
  自动化增删改查表格
-->
<template>
  <div :class="[{ 'page-table-height': adaptiveHeight }]">
    <!--
      v-if="isMounted" 不写的话，总是会报插槽未找到
    -->
    <vxe-grid
      v-if="isMounted"
      v-bind="gridOptions"
      ref="vxe-grid"
      show-header-overflow
    >
      <!--默认插槽写在上面，不然无法覆盖-->
      <template #submit_item>
        <vxe-button
          v-if="features.includes('select')"
          type="submit"
          status="primary"
          icon="el-icon-search"
          :content="btnTxt.select"
        />
        <slot name="extend_submit_item" />
      </template>

      <template #table_item_operate="scope">
        <vxe-button
          v-if="features.includes('update')"
          icon="el-icon-edit"
          @click="edit(scope.row)"
          :content="btnTxt.update"
        />
        <vxe-button
          v-if="features.includes('delete')"
          icon="el-icon-delete"
          status="danger"
          @click="remove([scope.row.id])"
          :content="btnTxt.delete"
        />
        <vxe-button
          v-if="features.includes('online')"
          icon="el-icon-upload2"
          @click="online([scope.row.id])"
          :content="btnTxt.online"
        />
        <vxe-button
          v-if="features.includes('offline')"
          icon="el-icon-download"
          status="danger"
          @click="offline([scope.row.id])"
          :content="btnTxt.offline"
        />
        <vxe-button
          v-if="features.includes('copy')"
          icon="el-icon-plus"
          status="primary"
          @click="handleCopy(scope.row)"
          :content="btnTxt.copy"
        />
        <!--自定义操作-->
        <slot name="custom_table_operate" v-bind="scope" />
      </template>
      <template #toolbar_buttons>
        <vxe-button
          v-if="features.includes('insert')"
          icon="el-icon-plus"
          @click="add"
          :content="btnTxt.insert"
        />
        <vxe-button
          v-if="features.includes('batch_delete')"
          icon="el-icon-delete"
          status="danger"
          @click="batchRemove"
          :content="btnTxt.batch_delete"
        />
        <vxe-button
          v-if="features.includes('batch_online')"
          icon="el-icon-upload2"
          :content="btnTxt.batch_online"
          @click="batchOnline"
        />
        <vxe-button
          v-if="features.includes('batch_offline')"
          icon="el-icon-download"
          status="danger"
          :content="btnTxt.batch_offline"
          @click="batchOffline"
        />
        <!--自定义的按钮列表-->
        <slot name="custom_toolbar" />
      </template>
      <!--默认插槽写在上面，不然无法覆盖-->

      <!-- ===================分割线=================== -->

      <template v-for="item in slotsList" v-slot:[item]="slotProps">
        <slot :name="item" v-bind="slotProps" />
      </template>
    </vxe-grid>

    <model-in-table
      :visible.sync="showModel"
      :select-item-request="requestCollection['selectItem']"
      :insert-or-update-request="requestCollection['insertOrUpdate']"
      :data-id="dataId"
      :model-config="modelConfig.modelConfig"
      :form-rule="modelConfig.formRule"
      :form-config="modelConfig.formConfig"
      :form-data.sync="modelConfig.formData"
      :auto-commit="modelConfig.autoCommit"
      :auto-update="modelConfig.autoUpdate"
      :adapter-form-data="modelConfig.adapterFormData"
      :adapter-res-data="modelConfig.adapterResData"
      @refresh="refreshEvent"
      @init-data="modelInitData"
      @submit="modelSubmit"
      @closed="isCopy = false"
    >
      <slot name="model">
        <template v-for="(value, key, index) in modelConfig.formData">
          <el-form-item
            v-if="key !== 'id'"
            :key="index"
            :prop="key"
            :label="
              modelConfig.formItemMap && modelConfig.formItemMap[key]
                ? modelConfig.formItemMap[key].title
                : key
            "
          >
            <component
              :is="
                modelConfig.formItemMap &&
                modelConfig.formItemMap[key] &&
                modelConfig.formItemMap[key].itemRender &&
                modelConfig.formItemMap[key].itemRender.name
                  ? modelConfig.formItemMap[key].itemRender.name
                  : 'el-input'
              "
              v-model="modelConfig.formData[key]"
              v-bind="
                modelConfig.formItemMap &&
                modelConfig.formItemMap[key] &&
                modelConfig.formItemMap[key].itemRender
                  ? modelConfig.formItemMap[key].itemRender.attrs
                  : {}
              "
            />
          </el-form-item>
        </template>
      </slot>
    </model-in-table>
  </div>
</template>

<script>
import { isArray, mergeWith } from 'lodash'
import ModelInTable from '@/components/model-in-table'
import ConfigJsonEditor from '@/components/config-json-editor'

// 批量上下线还未集成
export const features = [
  'insert',
  'delete',
  'batch_delete',
  'update',
  'select',
  // 'offline',
  'batch_offline',
  // 'online',
  'batch_online',
  // 'see',
  'copy',
]

export default {
  components: {
    ModelInTable,
    ConfigJsonEditor,
  },
  props: {
    // 自适应高度
    adaptiveHeight: {
      type: Boolean,
      default: true,
    },
    features: {
      types: Array,
      default: () => [...features],
    },
    buttonTxt: {
      type: Object,
    },
    gridConfig: {
      type: Object,
    },
    // 请求集合
    requestCollection: {
      type: Object,
    },
    modelConfig: {
      type: Object,
      default: () => ({
        autoCommit: true,
        autoUpdate: true,
        adapterFormData: null,
        adapterResData: null,
        modelConfig: {},
        formRule: {},
        // 表单配置
        formConfig: {},
        // 表单数据
        formData: {},
        // 表单简单配置
        formItemMap: {
          // categoryId: {
          //   title: '分类ID',
          //   itemRender: {
          //     name: 'input',
          //     attrs: {
          //       type: 'number',
          //     },
          //   },
          // },
        },
      }),
    },
    operateWidth: {
      type: [Number, String],
      default: 185,
    },
    showOperate: {
      type: Boolean,
      default: true,
    },
    beforeSelect: {
      type: Function,
      default: data => data,
    },
  },
  data() {
    return {
      slotsList: [],
      isMounted: false,
      showModel: false,
      dataId: '',
      isCopy: false,
    }
  },
  computed: {
    gridOptions() {
      return mergeWith(
        {
          maxHeight: this.adaptiveHeight ? 'auto' : '',
          exportConfig: {},
          pagerConfig: {
            pageSizes: [10, 15, 20, 50, 100, 500, 1000, 2000, 10000],
          },
          toolbarConfig: {
            refresh: true,
            slots: {
              buttons: 'toolbar_buttons',
            },
          },
          proxyConfig: {
            form: true, // 启用表单代理
            ajax: {
              query: async ({ page, form }) => {
                try {
                  const params = await this.beforeSelect(form)
                  const querySelect = {
                    currentPage: page.currentPage,
                    pageSize: page.pageSize,
                  }
                  if (params && typeof params === 'object') {
                    Object.assign(querySelect, params)
                  }
                  const result = await this.requestCollection['selectAll'](
                    querySelect
                  )
                  this.$emit('query-success', result)
                  return result
                } catch (e) {
                  this.$emit('query-fail', e)
                  return []
                }
              },
            },
          },
        },
        this.gridConfig,
        // 必须写在后面，不然，查询按钮就会在前面
        {
          formConfig: {
            items: [{ slots: { default: 'submit_item' } }],
          },
          columns: [
            {
              title: '操作',
              width: this.operateWidth,
              visible: this.showOperate,
              fixed: 'right',
              slots: { default: 'table_item_operate' },
            },
          ],
        },
        // 这玩意不写的话，formConfig.items会被直接覆盖,也就是说，查询按钮会没有
        // 如果传递了features。这里组件会报错
        // .[Vue warn]: Error in render: "TypeError: Cannot read properties of undefined (reading 'concat')"
        (objValue, srcValue) => {
          if (objValue && isArray(objValue)) {
            return objValue.concat(srcValue)
          }
        }
      )
    },
    btnTxt() {
      return Object.assign(
        {
          insert: '新增',
          delete: '删除',
          batch_delete: '批量删除',
          select: '查询',
          update: '编辑',
          offline: '下线',
          batch_offline: '批量下线',
          online: '上线',
          batch_online: '批量上线',
          copy: '复制',
        },
        this.buttonTxt
      )
    },
  },
  mounted() {
    // this.$scopedSlots 只有在mounted中才有值
    this.slotsList = Object.keys(this.$scopedSlots)
    this.isMounted = true
  },
  methods: {
    modelInitData(evt) {
      // 弹窗初始化获取数据
      this.$emit('model-init-data', evt)

      // model-init-data 执行时机会比 update:formData 早，所以需要延后一些
      setTimeout(() => {
        if (this.isCopy) {
          this.dataId = null
          delete this.modelConfig.formData.id
        }
      }, 30)
    },
    modelSubmit(evt) {
      // 弹窗提交数据
      this.$emit('model-submit', evt)
    },
    refreshEvent() {
      this.reloadQuery()
    },
    reloadQuery() {
      this.$refs['vxe-grid'].commitProxy('query')
    },
    getCheckboxRecords() {
      return this.$refs['vxe-grid'].getCheckboxRecords()
    },
    add() {
      this.$emit('operate-add')
      this.dataId = null
      this.showModel = true
    },
    edit(row) {
      this.$emit('operate-edit', {
        data: row,
      })
      this.dataId = row.id
      this.showModel = true
    },
    getIds() {
      const checkboxRecords = this.getCheckboxRecords()
      if (!checkboxRecords || !checkboxRecords.length) {
        this.$vxeMessage({
          status: 'warning',
          message: '请选选择',
        })
        return null
      }
      return checkboxRecords.map(it => it.id)
    },
    batchRemove() {
      const ids = this.getIds()
      if (ids && ids.length) {
        this.remove(ids)
      }
    },
    batchOnline() {
      const ids = this.getIds()
      if (ids && ids.length) {
        this.online(ids)
      }
    },
    batchOffline() {
      const ids = this.getIds()
      if (ids && ids.length) {
        this.offline(ids)
      }
    },
    online(ids) {
      this.handleRemoveOnlineOffline('batch_online', ids)
    },
    offline(ids) {
      this.handleRemoveOnlineOffline('batch_offline', ids)
    },
    remove(ids) {
      this.handleRemoveOnlineOffline('remove', ids)
    },
    handleRemoveOnlineOffline(type, ids) {
      let typMsg = this.btnTxt[type]
      let fnType

      switch (type) {
        case 'remove':
          typMsg = '删除'
          fnType = 'remove'
          break
        case 'batch_offline':
          typMsg = '下线'
          fnType = 'batchOffline'
          break
        case 'batch_online':
          typMsg = '上线'
          fnType = 'batchOnline'
          break
      }

      this.$confirm(`确定${typMsg}id为[${ids}]的数据吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.requestCollection[fnType](ids).then(() => {
          this.handleSuccess(typMsg)
        })
      })
    },
    handleSuccess(message) {
      this.$vxeMessage({
        status: 'success',
        message,
      })
      this.reloadQuery()
    },
    handleCopy(row) {
      this.$emit('operate-copy')
      this.edit(row)
      this.isCopy = true
    },
  },
}
</script>
