<template>
  <vue-json-editor
    v-model="config"
    :expandedOnStart="true"
    :height="height"
    mode="code"
  />
</template>

<script>
import vueJsonEditor from 'vue-json-editor'

export default {
  components: { vueJsonEditor },
  props: {
    value: {
      type: String,
    },
    height: {
      type: Number,
      default: 400,
    },
  },
  computed: {
    config: {
      set(config) {
        this.$emit('input', JSON.stringify(config))
      },
      get() {
        const config = this.value
        if (config) {
          try {
            return JSON.parse(config)
          } catch (e) {
            return {}
          }
        }
        return {}
      },
    },
  },
}
</script>
