<template>
  <el-select v-model="select" v-bind="$attrs" v-on="$listeners" collapse-tags>
    <el-option
      v-for="(item, index) in list"
      :key="index"
      :value="valueKey ? item[valueKey] : item"
      :label="labelKey ? item[labelKey] : item"
    />
  </el-select>
</template>

<script>
export default {
  name: 'arr-select',
  props: {
    list: {
      type: Array,
    },
    value: {
      type: [Number, String, Array],
    },
    labelKey: {
      type: String,
    },
    valueKey: {
      type: String,
    },
  },
  computed: {
    select: {
      get() {
        if (this.$attrs.multiple && this.value && !Array.isArray(this.value)) {
          return [this.value]
        }
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
}
</script>
