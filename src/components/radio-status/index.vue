<template>
  <map-radio v-model="status" :list="statusList" />
  <!--<el-switch-->
  <!--  v-model="status"-->
  <!--  :active-value="1"-->
  <!--  :inactive-value="0"-->
  <!--  active-text="上架"-->
  <!--  inactive-text="下架"-->
  <!--/>-->
</template>

<script>
export default {
  name: 'radio-status',
  props: {
    value: {
      type: Number,
      default: 0,
    },
    activeText: {
      type: String,
      default: '上架',
    },
    inactiveText: {
      type: String,
      default: '下架',
    },
  },
  data() {
    return {
      statusList: new Map([
        [0, this.inactiveText],
        [1, this.activeText],
      ]),
    }
  },
  computed: {
    status: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      },
    },
  },
}
</script>
