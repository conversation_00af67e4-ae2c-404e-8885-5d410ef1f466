<template>
  <el-select-extend
    v-model="brands"
    :multiple="multiple"
    :collapse-tags="collapseTags"
    :is-show-tools="isShowTools"
    :data-list="list"
    clearable
  >
    <el-option v-for="(item, index) in list" :key="index" :value="item" />
  </el-select-extend>
</template>

<script>
import ElSelectExtend from '@/components/el-select-extend'
import { getBrandList } from '@/api/app'

// 这些品牌必须写最前面
const firstBrandList = ['huawei', 'honor', 'vivo', 'oppo', 'xiaomi', 'redmi']

export default {
  name: 'brands-select',
  components: { ElSelectExtend },
  model: {
    prop: 'brandsValue',
    event: 'change-brands',
  },
  props: {
    appCode: {
      type: [Number, String],
    },
    brandsValue: {
      type: [Array, Number, String],
    },
    isShowTools: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: [],
    }
  },
  computed: {
    brands: {
      set(brands) {
        this.$emit('change-brands', brands)
      },
      get() {
        return this.brandsValue
      },
    },
  },
  watch: {
    appCode: {
      handler(appCode) {
        getBrandList({
          appCode,
        }).then(res => {
          if (res && res.data) {
            this.list = Array.from(
              new Set([...firstBrandList, ...res.data.filter(it => it)])
            )
          }
        })
      },
      immediate: true,
    },
  },
}
</script>
