<!--
  风控规则
-->
<template>
  <div>
    <template v-if="type === 1">
      <el-form-item label="固定值" prop="highValue">
        <el-input type="number" v-model="highValue" placeholder="固定值" />
      </el-form-item>
    </template>
    <template v-if="type === 2">
      <el-form-item label="数值上限" prop="highValue">
        <el-input type="number" v-model="highValue" placeholder="数值上限" />
      </el-form-item>
      <el-form-item label="数值下限" prop="lowValue">
        <el-input type="number" v-model="lowValue" placeholder="数值下限" />
      </el-form-item>
    </template>
    <template v-if="type === 3">
      <el-form-item label-width="0" prop="extraConfig">
        <vue-json-editor v-model="extraValue" mode="code" />
      </el-form-item>
    </template>
  </div>
</template>

<script>
import vueJsonEditor from 'vue-json-editor'

export default {
  name: 'limit-rule-input',
  components: {
    vueJsonEditor,
  },
  props: {
    dataId: {
      type: Number,
    },
    low: {
      type: [Number, String],
    },
    high: {
      type: [Number, String],
    },
    extra: {
      type: String,
    },
    // 类型:1:固定，2：区间 3 json
    type: {
      type: Number,
      validator: function(value) {
        // 这个值必须匹配下列字符串中的一个
        return [1, 2, 3].indexOf(value) !== -1
      },
    },
  },
  computed: {
    extraValue: {
      get() {
        return this.extra
      },
      set(v) {
        this.$emit('update:extra', v)
        // 必须要设置null,不然无法提交表单
        this.$emit('update:low', null)
        this.$emit('update:high', null)
      },
    },
    lowValue: {
      get() {
        return this.low
      },
      set(v) {
        this.$emit('update:low', v)
        // 必须要设置null,不然无法提交表单
        this.$emit('update:extra', null)
      },
    },
    highValue: {
      get() {
        return this.high
      },
      set(v) {
        if (this.type === 1) {
          this.$emit('update:low', v)
          this.$emit('update:high', v)
        } else if (this.type === 2) {
          this.$emit('update:high', v)
        }
        // 必须要设置null,不然无法提交表单
        this.$emit('update:extra', null)
      },
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .jsoneditor-vue {
    min-height: 500px;
  }
}
</style>
