<template>
  <prism-editor
    class="my-editor"
    v-model="code"
    v-bind="$attrs"
    v-on="$listeners"
    :highlight="highlighter"
  />
</template>

<script>
// 文档
// https://github.com/koca/vue-prism-editor
// https://codechina.csdn.net/mirrors/koca/vue-prism-editor

// import Prism Editor
import { PrismEditor } from 'vue-prism-editor'
import 'vue-prism-editor/dist/prismeditor.min.css' // import the styles somewhere

// import highlighting library (you can use any library you want just return html string)
import { highlight, languages } from 'prismjs/components/prism-core'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-javascript'
import 'prismjs/themes/prism-tomorrow.css' // import syntax highlighting styles

export default {
  props: {
    value: {
      type: String,
    },
  },
  components: {
    PrismEditor,
  },
  computed: {
    code: {
      get() {
        return this.value
      },
      set(code) {
        this.$emit('input', code)
      },
    },
  },
  methods: {
    highlighter(code) {
      return highlight(code, languages.javascript, 'javascript')
    },
  },
}
</script>

<style lang="scss" scoped>
/* required class */
.my-editor {
  /* we dont use `language-` classes anymore so thats why we need to add background and text color manually */
  background: #2d2d2d;
  color: #ccc;
  /* you must provide font-family font-size line-height. Example: */
  font-family: Fira code, Fira Mono, Consolas, Menlo, Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 5px;
  &::v-deep {
    /* optional class for removing the outline */
    .prism-editor__textarea:focus {
      outline: none;
    }
  }
}

</style>
