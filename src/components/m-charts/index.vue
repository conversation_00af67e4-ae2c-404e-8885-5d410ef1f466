<template>
  <div v-loading="loading" style="position: relative">
    <div
      ref="echarts"
      :style="{
        visibility:
          echartsOption.series && echartsOption.series.length
            ? 'visible'
            : 'hidden',
      }"
    />
    <div class="no-data" v-show="!loading && !echartsOption.series">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { merge } from 'lodash'
import { removeZero } from '@/utils/tools'

export default {
  name: 'index',
  props: {
    option: {
      type: Object,
      default: () => ({}),
    },
    eChartsInitOpts: {
      type: Object,
      default: () => ({
        height: 400,
      }),
    },
    loading: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      initProp: {
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false, // 如果是bar的话，这个一定要设置为true；虽然默认就是ture
          axisLabel: {
            showMaxLabel: true,
          },
        },
        yAxis: {
          type: 'value',
        },
        legend: {
          icon: 'rect',
          position: 'left',
        },
        // series: [],
      },
      myChart: null,
    }
  },
  computed: {
    echartsOption() {
      // 其他配置merge
      const option = merge(this.initProp, this.option)
      // 数据需要覆盖
      option.series = this.option.series

      if (option.series) {
        option.series = option.series.map(it => ({
          ...it,
          data: removeZero(it.data),
          symbolSize: 4, //设定实心点的大小
          lineStyle: {
            width: 2, // 0.1的线条是非常细的了
          },
        }))
      }

      return option
    },
  },
  watch: {
    echartsOption() {
      // 绘制图表
      this.myChart.setOption(this.echartsOption, true)
    },
  },
  mounted() {
    // 基于准备好的dom，初始化echarts实例
    this.myChart = echarts.init(this.$refs.echarts, null, this.eChartsInitOpts)
    // 绘制图表
    this.myChart.setOption(this.echartsOption, true)
    window.addEventListener('resize', this.myChart.resize)
  },
  methods: {},
}
</script>

<style scoped>
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
</style>
