<template>
  <div>
    <div
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <h3>代码位关联</h3>
      <div>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="_handleAddCode"
        >
          添加代码位
        </el-button>
      </div>
    </div>
    <!--已绑定的列表-->
    <code-table
      :table-data="adCodeBindList"
      :hasSort="hasSort"
      @handle-sort="_bindHandleSort"
    >
      <template slot-scope="{ row }">
        <el-popconfirm title="确定删除吗？" @confirm="_unBindAp(row)">
          <el-button slot="reference" type="text">删除</el-button>
        </el-popconfirm>
      </template>
    </code-table>
    <!--添加弹窗-->
    <add-code
      v-if="addCodeVisible"
      ref="addCode"
      :ap-id="apId"
      :as-id="asId"
      :ad-type="adType"
      :type="type"
      :has-sort="unBindHasSort"
      @closed="_getAdCodeBindList()"
      @handle-sort="_unBindHandleSort"
    />
  </div>
</template>

<script>
import AddCode from './components/add'
import CodeTable from './components/code-table'

export default {
  name: 'connectCodePosition',
  components: {
    AddCode,
    CodeTable,
  },
  props: {
    // 广告位ID
    apId: {
      type: Number,
    },
    // 广告策略ID
    asId: {
      type: Number,
    },
    // 类型：广告位、策略
    type: {
      type: String,
      default: 'adPosition', // adPosition：广告位 | strategy: 策略
    },
    // 广告类型
    adType: {
      type: [Number, String],
    },
    // 已关联代码列表是否有排序
    hasSort: {
      type: Boolean,
      default: false,
    },
    // 未关联代码列表是否有排序
    unBindHasSort: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      adCodeBindList: [],
      addCodeVisible: false,
    }
  },
  methods: {
    init() {
      this._getAdCodeBindList()
    },
    _handleAddCode() {
      this.addCodeVisible = true

      this.$nextTick(() => {
        this.$refs.addCode.init()
      })
    },
    /**
     * 查看已绑定的广告代码位列表
     * @private
     */
    _getAdCodeBindList() {
      if (this.type === 'adPosition') {
        this._getAdPositionBindList()
      } else if (this.type === 'strategy') {
        this._getStrategyBindList()
      }
    },
    /**
     * 解绑
     * @private
     */
    _unBindAp(row) {
      if (this.type === 'adPosition') {
        this._unBindAdPositionAp(row.id)
      } else if (this.type === 'strategy') {
        this._unBindStrategyAp(row.strategySlotId)
      }
    },
    /**
     * 查看某广告位已绑定的广告代码位列表
     * @private
     */
    _getAdPositionBindList() {
      if (this.apId) {
        const params = {
          apId: this.apId,
        }
        this.$store
          .dispatch('api/ad/getAdPositionBindList', params)
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.adCodeBindList = data.list
            }
          })
      }
    },
    /**
     * 查看某策略已绑定的广告代码位列表
     * @private
     */
    _getStrategyBindList() {
      if (this.asId) {
        const params = {
          asId: this.asId, // 广告策略ID
        }
        this.$store
          .dispatch('api/ad/getStrategyBindList', params)
          .then(({ data }) => {
            if (data && data.code === 0) {
              this.adCodeBindList = data.list
            }
          })
      }
    },
    /**
     * 解绑广告位的广告代码
     * @param id
     * @private
     */
    _unBindAdPositionAp(id) {
      const data = {
        id,
      }
      this.$store
        .dispatch('api/ad/unBindAdPositionAp', data)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('解绑成功')
            this._getAdCodeBindList()
          } else {
            this.$message.error(data.msg || '解绑失败')
          }
        })
        .catch(e => {
          this.$message.error(e.message || '服务器错误')
        })
    },
    /**
     * 解绑策略的广告代码
     * @param id （"策略下代码位管理模块"的自增ID）
     * @private
     */
    _unBindStrategyAp(id) {
      this.$store
        .dispatch('api/ad/unBindStrategyAp', [id])
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('解绑成功')
            this._getAdCodeBindList()
          } else {
            this.$message.error(data.msg || '解绑失败')
          }
        })
        .catch(e => {
          this.$message.error(e.message || '服务器错误')
        })
    },
    /**
     * 处理未绑定列表的sort值
     * @param data
     * @private
     */
    _unBindHandleSort(data) {
      this.$emit('un-bind-handle-sort', data)
    },
    /**
     * 处理绑定列表的sort值
     * @param data
     * @private
     */
    _bindHandleSort(data) {
      this.$emit('bind-handle-sort', data)
    },
  },
}
</script>
