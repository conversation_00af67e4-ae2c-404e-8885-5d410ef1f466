<template>
  <el-dialog
    title="添加代码位"
    :visible.sync="showDialog"
    width="930px"
    @closed="$emit('closed')"
    append-to-body
  >
    <code-table
      :table-data="tableData"
      :has-sort="hasSort"
      @handle-sort="$emit('handle-sort', $event)"
    >
      <template slot-scope="scope">
        <el-popconfirm title="确定添加？" @confirm="_bindAp(scope)">
          <el-button slot="reference" :disabled="scope.row.isAdd" type="text">
            添加
          </el-button>
        </el-popconfirm>
      </template>
    </code-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="showDialog = false">
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import CodeTable from './code-table'
export default {
  name: 'add',
  components: {
    CodeTable,
  },
  props: {
    // 广告位ID
    apId: {
      type: Number,
      required: true,
    },
    // 如果type值位：strategy 传 广告策略ID
    asId: {
      type: Number,
    },
    // 类型：广告位、策略
    type: {
      type: String,
      default: 'adPosition', // adPosition：广告位 | strategy: 策略
    },
    // 广告类型
    adType: {
      type: [Number, String],
    },
    // 是否有排序
    hasSort: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showDialog: false,
      tableData: [],
    }
  },
  methods: {
    init() {
      this.showDialog = true
      this._getAdCodeAvailableBindList()
    },
    /**
     * 代码位绑定
     * @private
     * */
    _bindAp(scope) {
      if (this.type === 'adPosition') {
        this._bindAdPositionAp(scope)
      } else if (this.type === 'strategy') {
        this._bindStrategyAp(scope)
      }
    },
    /**
     * 策略绑定代码位
     * @private
     * */
    _bindStrategyAp(scope) {
      const data = {
        strategyId: this.asId, // 广告策略ID
        slotId: scope.row.id, // 广告代码位ID
        sortValue: scope.row.sortValue,
      }
      this.$store
        .dispatch('api/ad/bindStrategyAp', data)
        .then(({ data }) => {
          if (data && data.code === 0) {
            console.log(data)
            if (data && data.code === 0) {
              this.$message.success('绑定成功')
              this.tableData[scope.$index].isAdd = true
            } else {
              this.$message.error(data.msg || '绑定失败')
            }
          }
        })
        .catch(e => {
          this.$message.error(e.message || '服务器错误')
        })
    },
    /**
     * 广告位绑定代码位
     * */
    _bindAdPositionAp(scope) {
      const data = {
        id: scope.row.id, // 广告代码位ID
        apId: this.apId, // 广告位ID
      }
      this.$store
        .dispatch('api/ad/bindAdPositionAp', data)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.$message.success('绑定成功')
            this.tableData[scope.$index].isAdd = true
          } else {
            this.$message.error(data.msg || '绑定失败')
          }
        })
        .catch(e => {
          this.$message.error(e.message || '服务器错误')
        })
    },
    /**
     * 获取列表
     * @private
     * */
    _getAdCodeAvailableBindList() {
      if (this.type === 'adPosition') {
        this._getAdPositionBindList()
      } else if (this.type === 'strategy') {
        this._getBindCodeList()
      }
    },
    /**
     * 查看广告位可绑定广告代码位列表
     * @private
     */
    _getAdPositionBindList() {
      const params = {
        adType: this.adType,
      }
      this.$store
        .dispatch('api/ad/getAdPositionAvailableBindList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.tableData = data.list.map(it => ({
              ...it,
              isAdd: false,
            }))
          }
        })
    },
    /**
     * 查看某广告位已绑定的广告代码位列表(策略下，只能选择某广告位已经绑定好的代码位，但是必须要传asId)
     * @private
     */
    _getBindCodeList() {
      const params = {
        apId: this.apId, // 广告位ID
        asId: this.asId, // 策略ID
      }
      this.$store
        .dispatch('api/ad/getAdPositionBindList', params)
        .then(({ data }) => {
          if (data && data.code === 0) {
            this.tableData = data.list.map(it => ({
              ...it,
              isAdd: false,
            }))
          }
        })
    },
  },
}
</script>
