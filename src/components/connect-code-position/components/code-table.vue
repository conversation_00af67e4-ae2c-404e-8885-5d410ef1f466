<template>
  <div>
    <el-table :data="tableData" style="width: 100%">
      <!--<el-table-column-->
      <!--  prop="id"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="自增ID"-->
      <!--/>-->
      <!--<el-table-column type="index" width="50" />-->
      <el-table-column v-if="hasSort" label="排序" width="80">
        <template slot-scope="{ row }">
          <el-input v-model="row.sortValue" @blur="_handleSort(row)" />
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="代码位名称"
        width="240px"
      />
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="代码位类型"
      >
        <template slot-scope="{ row }">
          <span>{{ codeType.get(row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="adType"
        header-align="center"
        align="center"
        label="广告类型"
      >
        <template slot-scope="{ row }">
          <span>{{ adType.get(row.adType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unionType"
        header-align="center"
        align="center"
        label="联盟类型"
      >
        <template slot-scope="{ row }">
          <span>{{ allianceType.get(row.unionType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="unionCode"
        header-align="center"
        align="center"
        label="代码位ID"
      />
      <el-table-column
        prop="ecpm"
        header-align="center"
        align="center"
        label="eCPM(¥)"
      />
      <!--<el-table-column-->
      <!--  prop="exclusive"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="是否排它"-->
      <!--&gt;-->
      <!--  <template slot-scope="{ row }">-->
      <!--    <span>{{ isExclusion.get(row.exclusive) }}</span>-->
      <!--  </template>-->
      <!--</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="status"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="代码位状态"-->
      <!--&gt;-->
      <!--  <template slot-scope="{ row }">-->
      <!--    <span>{{ codeSwitchStatus.get(row.status) }}</span>-->
      <!--  </template>-->
      <!--</el-table-column>-->
      <!--<el-table-column-->
      <!--  prop="apId"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="关联广告位ID"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="createdAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="创建时间"-->
      <!--/>-->
      <!--<el-table-column-->
      <!--  prop="updatedAt"-->
      <!--  header-align="center"-->
      <!--  align="center"-->
      <!--  label="更新时间"-->
      <!--/>-->
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <slot v-bind="scope" />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  codeType,
  isExclusion,
  codeSwitchStatus,
  adSwitchStatus,
  allianceType,
  adType,
} from '@/map/common'

export default {
  name: 'code-table',
  props: {
    tableData: {
      type: Array,
    },
    hasSort: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      codeType,
      isExclusion,
      codeSwitchStatus,
      adSwitchStatus,
      allianceType,
      adType,
    }
  },
  methods: {
    _handleSort(row) {
      this.$emit('handle-sort', row)
    },
  },
}
</script>

<style scoped></style>
