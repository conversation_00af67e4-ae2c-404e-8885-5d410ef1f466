{"name": "vue-<PERSON><PERSON>-editor", "version": "1.4.3", "description": "A json editor of vue", "main": "vue-json-editor.vue", "scripts": {"dev": "webpack-dev-server", "build": "rimraf dist && webpack", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/dirkliu/vue-json-editor.git"}, "keywords": ["vue-<PERSON><PERSON>-editor", "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "json-editor", "jsoneditor", "json", "e<PERSON><PERSON>", "vue"], "author": "liuqi41", "contributors": ["<PERSON> <<EMAIL>> (https://tuttarealstep.github.io/)", "<PERSON> (https://github.com/dflock)", "<PERSON><PERSON><PERSON> (https://github.com/jpbecotte)", "tianzhihen (https://github.com/tianzhihen)", "alexmgillis (https://github.com/alexmgillis)", "DrCoc (https://github.com/DrCoc)", "<PERSON><PERSON><PERSON> (https://github.com/ahermant/)"], "license": "ISC", "bugs": {"url": "https://github.com/dirkliu/vue-json-editor/issues"}, "homepage": "https://github.com/dirkliu/vue-json-editor#readme", "dependencies": {"vue": "^2.2.6"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/preset-env": "^7.2.3", "babel-core": "^6.26.3", "babel-loader": "^8.0.5", "babel-preset-env": "^1.4.0", "babel-preset-es2015": "^6.24.1", "babel-template": "^6.24.1", "css-loader": "^2.1.0", "file-loader": "^3.0.1", "html-webpack-plugin": "^3.2.0", "rimraf": "^2.6.1", "style-loader": "^0.23.1", "url-loader": "^1.1.2", "vue-loader": "^15.5.0", "vue-template-compiler": "^2.2.6", "webpack": "^4.28.3", "webpack-cli": "^3.2.0", "webpack-dev-server": "^3.1.14"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}