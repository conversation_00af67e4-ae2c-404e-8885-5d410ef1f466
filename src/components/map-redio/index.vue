<template>
  <vxe-radio-group v-model="select" v-bind="$attrs" v-on="$listeners">
    <vxe-radio v-for="[key, label] in list" :key="key" :label="key">
      {{ label }}
    </vxe-radio>
  </vxe-radio-group>
</template>

<script>
export default {
  name: 'map-radio',
  props: {
    list: {
      type: Map,
    },
    value: {
      type: [Number, String],
    },
  },
  computed: {
    select: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
}
</script>
