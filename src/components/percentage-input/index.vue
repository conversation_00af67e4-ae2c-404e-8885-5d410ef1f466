<template>
  <el-input
    type="number"
    v-model.number="inData"
    v-bind="$attrs"
    :max="100"
    :min="0"
  >
    <template slot="append">%</template>
  </el-input>
</template>

<script>
export default {
  name: 'percentage-input',
  model: {
    prop: 'inputData',
    event: 'change-event',
  },
  props: {
    inputData: {
      type: Number,
    },
    fixed: {
      type: Number,
      default: 2,
    },
  },
  computed: {
    inData: {
      get() {
        return parseFloat((this.inputData * 100).toFixed(this.fixed))
      },
      set(v) {
        this.$emit('change-event', v / 100)
      },
    },
  },
}
</script>
