<!--
  使用示例
  <base-form :inline="true" :model.sync="dataForm" @submit="getDataList()">
    <el-form-item label="选择应用">
        <app-select-component
          v-model="dataForm.appCode"
          @change-app="currentChangeHandle(1)"
          clearable
        />
      </el-form-item>
      ...其他表单内容
  </base-form>
-->
<template>
  <el-form v-bind="$attrs" v-on="$listeners" :model="dataForm" ref="form">
    <slot />
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="submit">
        查询
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'base-form',
  props: {
    model: {
      type: Object,
      default: () => ({}),
    },
    isCache: {
      type: Boolean,
      default: true,
    },
    urlKey: {
      type: String,
      default: 'data_form',
    },
  },
  computed: {
    dataForm: {
      get() {
        return this.model
      },
      set(data) {
        this.$emit('update:model', data)
      },
    },
  },
  watch: {
    dataForm: {
      handler() {
        this.isCache && this.cacheModel()
      },
      deep: true,
    },
  },
  created() {
    this.isCache && this.parseModel()
  },
  methods: {
    submit() {
      this.$emit('submit', this.dataForm)
    },
    cacheModel() {
      if (window.btoa) {
        const dataForm = JSON.stringify(this.dataForm)
        // base64 编码
        const urlData = window.btoa(dataForm)

        if (urlData !== this.$route.query[this.urlKey]) {
          this.$router.replace({
            url: this.$route.path,
            query: {
              ...this.$route.query,
              [this.urlKey]: urlData,
            },
          })
        }
      }
    },
    parseModel() {
      let ret = null
      if (window.atob) {
        const dataForm = this.$route.query[this.urlKey]
        if (dataForm) {
          try {
            // base64 解码
            ret = JSON.parse(window.atob(dataForm))
            this.dataForm = ret
          } catch (e) {
            console.log(e)
          }
        }
      }
      return ret
    },
    getElForm() {
      return this.$refs.form
    },
  },
}
</script>
