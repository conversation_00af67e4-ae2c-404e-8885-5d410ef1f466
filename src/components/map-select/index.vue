<template>
  <el-select
    v-model="select"
    v-bind="$attrs"
    v-on="$listeners"
    :collapse-tags="collapseTags"
  >
    <el-option
      v-for="[key, label] in list"
      :key="key"
      :value="key"
      :label="label"
    />
  </el-select>
</template>

<script>
export default {
  name: 'map-select',
  props: {
    list: {
      type: Map,
    },
    value: {
      type: [Number, Array, String],
    },
    collapseTags: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    select: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
}
</script>
