<template>
  <arr-select
    v-model="select"
    :list="countryList"
    label-key="country_name"
    value-key="country"
    v-bind="$attrs"
    v-on="$listeners"
    clearable
    :multiple="multiple"
  />
</template>

<script>
import { countryList } from '@/map/sat'

export default {
  name: 'CountrySelect',
  props: {
    value: {
      type: [Number, String, Array],
      default: countryList[0].country,
    },
    hasAll: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    filter: {
      type: Function,
    },
  },
  data() {
    return {
      countries: this.hasAll ? countryList : countryList.slice(1),
    }
  },
  computed: {
    countryList() {
      if (this.hasAll) {
        return this.countries
      }
      if (this.filter) {
        return this.countries.filter((value, index, array) => {
          if (this.filter) {
            return this.filter(value, index, array)
          }
          return true
        })
      }
      return this.countries
    },
    select: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
}
</script>
