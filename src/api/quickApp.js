import { genRequestAPI, request } from '@/utils/request'

/**
 * 分类内小说表
 */
export const novelBookCategoryItemRequest = {
  ...genRequestAPI('/quickApp/novelbookcategoryitem', {
    selectItemResCopyField: 'novelBookCategoryItem',
  }),
}

/**
 * 小说分类表
 */
export const novelBookCategoryRequest = {
  ...genRequestAPI('/quickApp/novelbookcategory', {
    selectItemResCopyField: 'novelBookCategory',
  }),
}

/**
 * 小说信息表
 */
export const novelBookInfoRequest = {
  ...genRequestAPI('/quickApp/novelbookinfo', {
    selectItemResCopyField: 'novelBookInfo',
  }),
}

/**
 * 小说推荐
 */
export const novelRecommendPositionRequest = {
  ...genRequestAPI('/quickApp/novelrecommendposition', {
    selectItemResCopyField: 'novelRecommendPosition',
  }),
}

/**
 * 小说推荐
 */
export const businessConfigVersionChannelRequest = {
  ...genRequestAPI('/quickApp/businessconfigversionchannel', {
    selectItemResCopyField: 'businessConfigVersionChannel',
  }),
}

/**
 * 漫画推荐
 */
export const comicRecommendPositionRequest = {
  ...genRequestAPI('/quickApp/comicrecommendposition', {
    selectItemResCopyField: 'comicRecommendPosition',
  }),
}
/**
 * 漫画信息
 */
export const comicBookInfoRequest = {
  ...genRequestAPI('/quickApp/comicbookinfo', {
    selectItemResCopyField: 'comicBookInfo',
  }),
}

// 业务配置表
export const businessConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/quickApp/businessconfig`, {
    selectItemResCopyField: 'businessConfig',
  }),
}

// ocr用户
export const ocrUserRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/quickApp/ocruser`, {
    selectItemResCopyField: 'ocrUser',
  }),
}

// ocr订单
export const ocrOrderRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/quickApp/ocrorder`, {
    selectItemResCopyField: 'ocrorder',
  }),
  // 退费
  tradeRefund(id) {
    return request({
      url: '/quickApp/ocrorder/tradeRefund/' + id
    })
  },
  // 撤销权益
  cancelOrder(id) {
    return request({
      url: '/quickApp/ocrorder/cancel/' + id
    })
  }
}
