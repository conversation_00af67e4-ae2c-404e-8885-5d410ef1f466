import { genRequestAPI, request } from '@/utils/request'

export const skinRequest = {
  ...genRequestAPI('/petKeyboard/skin', {
    selectItemResCopyField: 'skin',
  }),
  /**
   * 新增，【覆盖原先的方法】需要上传文件
   * @param data
   * @returns {Promise<unknown>}
   */
  insert(data) {
    return request({
      url: `/petKeyboard/skin/save`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
      },
      isHandleAdornData: false,
    })
  },
  /**
   * 更新，【覆盖原先的方法】需要上传文件
   * @param data
   * @returns {Promise<unknown>}
   */
  update(data) {
    return request({
      url: `/petKeyboard/skin/update`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
      },
      isHandleAdornData: false,
    })
  },
  insertOrUpdate(id, data) {
    return id ? skinRequest.update(data) : skinRequest.insert(data)
  },
}

export const yudanRequest = {
  ...genRequestAPI('/petKeyboard/yudan', {
    selectItemResCopyField: 'yudan',
  }),
  /**
   * 批量上传
   * @param data
   * @returns {Promise<unknown>}
   */
  batchUpload(data) {
    return request({
      url: '/petKeyboard/yudan/upload',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
      },
      isHandleAdornData: false,
    })
  },
}

// 互动
export const petInteractionRequest = {
  ...genRequestAPI('/petKeyboard/petinteraction', {
    selectItemResCopyField: 'petInteraction',
  }),
}

// 语弹类别
export const yuDanClassificationRequest = {
  ...genRequestAPI('/petKeyboard/yudanclassification', {
    selectItemResCopyField: 'yudanClassification',
  }),
}

// 特殊日期
export const specialDateRequest = {
  ...genRequestAPI('/petKeyboard/specialdate', {
    selectItemResCopyField: 'specialDate',
  }),
}

// 用户
export const userRequest = {
  ...genRequestAPI('/petKeyboard/user', {
    selectItemResCopyField: 'user',
  }),
}

// 用户
export const businessConfigRequest = {
  ...genRequestAPI('/petKeyboard/businessconfig', {
    selectItemResCopyField: 'businessConfig',
  }),
}
