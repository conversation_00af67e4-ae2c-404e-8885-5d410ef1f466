import { genRequestAPI } from '@/utils/request'

// 任务模块
export const taskConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guesstaskconfig`, {
    selectItemResCopyField: 'guess2TaskConfig',
  }),
}

// 题库模块
export const questionStoreRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guessquestionstore`, {
    selectItemResCopyField: 'guess2QuestionStore',
  }),
}

// 提现配置
export const withdrawalConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guesswithdrawalconfig`, {
    selectItemResCopyField: 'guess2WithdrawalConfig',
  }),
}

// 连队加成配置
export const rewardAdditionConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guessrewardadditionconfig`, {
    selectItemResCopyField: 'guess2RewardAdditionConfig',
  }),
}

// 用户等级
export const userLevelConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guessuserlevelconfig`, {
    selectItemResCopyField: 'guess2UserLevelConfig',
  }),
}

// 风控
export const limitRuleRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guesslimitrule`, {
    selectItemResCopyField: 'guess2LimitRule',
  }),
}

// 用户表
export const userRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa2/guessuser`, {
    selectItemResCopyField: 'guess2User',
  }),
}
