import { genRequestAPI } from '@/utils/request'

// 提现配置
export const withdrawalConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingwithdrawalconfig`, {
    selectItemResCopyField: 'fishingWithdrawalConfig',
  }),
}

// 任务
export const taskConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingtaskconfig`, {
    selectItemResCopyField: 'fishingTaskConfig',
  }),
}

// 基础配置
export const baseConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingbaseconfig`, {
    selectItemResCopyField: 'fishingBaseConfig',
  }),
}

export const fishingDrawConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingdrawconfig`, {
    selectItemResCopyField: 'fishingDrawConfig',
  }),
}

// 等级配置
export const fishingLevelConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishinglevelconfig`, {
    selectItemResCopyField: 'fishingLevelConfig',
  }),
}

// 业务配置表
export const businessConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/businessconfig`, {
    selectItemResCopyField: 'businessConfig',
  }),
}

// 图鉴配置
export const fishingCollectConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingcollectconfig`, {
    selectItemResCopyField: 'fishingCollectConfig',
  }),
}

// 用户积分
export const fishingUserCoinRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingusercoinrecord`, {
    selectItemResCopyField: 'fishingUserCoinRecord',
  }),
}

// 图鉴记录表
export const fishingCollectRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingcollectrecord`, {
    selectItemResCopyField: 'fishingCollectRecord',
  }),
}

// ab测试用户
export const fishingAbTestResult = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingabtestresult`, {
    selectItemResCopyField: 'fishingAbTestResult',
  }),
}

// ab测试用户-配置表
export const fishingAbTestConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishingabtestconfig`, {
    selectItemResCopyField: 'fishingAbTestConfig',
  }),
}

// 用户-账户表
export const fishingUserAccount = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/fishinguseraccount`, {
    selectItemResCopyField: 'fishingUserAccount',
  }),
}

// 提示词生成规范
export const cueWordPrinciple = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/cuewordprinciple`, {
    selectItemResCopyField: 'cueWordPrinciple',
  }),
}

// 自定义过滤器
export const filterWordConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/filterwordconfig`, {
    selectItemResCopyField: 'filterWordConfig',
  }),
}

// 自定义过滤器
export const gptCallRequst = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/gptcallrequst`, {
    selectItemResCopyField: 'gptCallRequst',
  }),
}

// 充值记录
export const rechargeRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/rechargerecord`, {
    selectItemResCopyField: 'rechargeRecord',
  }),
}

// 充值记录套餐
export const rechargeSetConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing3/rechargesetconfig`, {
    selectItemResCopyField: 'rechargeSetConfig',
  }),
}
