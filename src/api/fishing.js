import { genRequestAPI } from '@/utils/request'

// 提现配置
export const withdrawalConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingwithdrawalconfig`, {
    selectItemResCopyField: 'fishingWithdrawalConfig',
  }),
}

// 任务
export const taskConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingtaskconfig`, {
    selectItemResCopyField: 'fishingTaskConfig',
  }),
}

// 基础配置
export const baseConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingbaseconfig`, {
    selectItemResCopyField: 'fishingBaseConfig',
  }),
}

export const fishingDrawConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingdrawconfig`, {
    selectItemResCopyField: 'fishingDrawConfig',
  }),
}

// 等级配置
export const fishingLevelConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishinglevelconfig`, {
    selectItemResCopyField: 'fishingLevelConfig',
  }),
}

// 业务配置表
export const businessConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/businessconfig`, {
    selectItemResCopyField: 'businessConfig',
  }),
}

// 图鉴配置
export const fishingCollectConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingcollectconfig`, {
    selectItemResCopyField: 'fishingCollectConfig',
  }),
}

// 用户积分
export const fishingUserCoinRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingusercoinrecord`, {
    selectItemResCopyField: 'fishingUserCoinRecord',
  }),
}

// 图鉴记录表
export const fishingCollectRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingcollectrecord`, {
    selectItemResCopyField: 'fishingCollectRecord',
  }),
}

// ab测试用户
export const fishingAbTestResult = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingabtestresult`, {
    selectItemResCopyField: 'fishingAbTestResult',
  }),
}

// ab测试用户-配置表
export const fishingAbTestConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishingabtestconfig`, {
    selectItemResCopyField: 'fishingAbTestConfig',
  }),
}

// 用户-账户表
export const fishingUserAccount = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing/fishinguseraccount`, {
    selectItemResCopyField: 'fishingUserAccount',
  }),
}
