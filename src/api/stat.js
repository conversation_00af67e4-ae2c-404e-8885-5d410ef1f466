import { genRequestAPI, request } from '@/utils/request'

// 头条实时投放数据
export const statOceanPromotionRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/statoceanpromotion`, {
    selectItemResCopyField: 'statOceanPromotion',
  }),
  /**
   * 投放账户列表
   * @returns {Promise | Promise<unknown>}
   */
  getAdvertiserIdList(appCode) {
    return request({
      url: '/stat/statoceanpromotion/getAdvertiserIdList?appCode=' + appCode,
    })
  },
}

// 后台头条实时投放数据
export const statOceanPromotionCalRequest = {
  ...genRequestAPI('/stat/statoceanpromotioncal', {
    selectItemResCopyField: 'statOceanPromotionCal',
  }),
}

// 海外新增用户回本数据
export const hwUserReturnDailyRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/hwuserreturndaily`, {
    selectItemResCopyField: 'hwUserReturnDaily',
  }),
}

// topOn Ltv
export const topOnLtvReportDailyRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/toponltvreportdaily`, {
    selectItemResCopyField: 'topOnLtvReportDaily',
  }),
}

// 海外数据概览表
export const hwStatBusinessDailyRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/hwstatbusinessdaily`, {
    selectItemResCopyField: 'hwStatBusinessDaily',
  }),
}
// 快应用分品牌收益
export const quickAppBrandDataDailyRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/quickappbranddatadaily`, {
    selectItemResCopyField: 'quickAppBrandDataDaily',
  }),
}

// 海外投放成本
export const hwCostDailyRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/hwcostdaily`, {
    selectItemResCopyField: 'hwCostDaily',
  }),
  selectRoasList({ currentPage, pageSize, ...rest }) {
    return request({
      url: '/stat/hwcostdaily/roas_list',
      method: 'get',
      params: {
        page: currentPage,
        limit: pageSize,
        ...rest,
      },
    })
  },
  uploadGoogleCost(data) {
    return request({
      url: '/stat/hwcostdaily/uploadGoogleCost',
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data,
      isHandleAdornData: false,
    })
  },
}

export const ctrCallbackRules = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/ctrcallbackrules`, {
    selectItemResCopyField: 'ctrCallbackRules',
  }),
  upPercentSave(data) {
    return request({
      url: '/stat/ctrcallbackrules/up_percent_save',
      method: 'POST',
      data,
    })
  },
  upPercentList(appCode, brand) {
    return request({
      url: '/stat/ctrcallbackrules/up_percent_list',
      params: {
        appId: appCode,
        brand: brand,
      },
    })
  },
}

// 海外实时数据
export const hwRealtimeDataRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/hwrealtimedata`, {
    selectItemResCopyField: 'hwRealtimeData',
  }),
}

// 海外实时数据预估
export const hwRealtimePredictDataRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/hwrealtimepredictdata`, {
    selectItemResCopyField: 'hwRealtimePredictData',
  }),
}

// 海外投放账户ID配置表
export const costAdvertiseConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/costadvertiseconfig`, {
    selectItemResCopyField: 'costAdvertiseConfig',
  }),
}

export const roiRequest = {
  selectAll(params = {}) {
    return request({
      url: '/stat/hwaccincomedaily/roi',
      params: {
        appId: '',
        channel: '',
        country: '',
        ...params,
      },
    })
  },
}

/**
 * Roi 预警表
 * @type {{selectAll: function({currentPage: *, pageSize: *, [p: string]: *}): Promise<unknown>, batchOnline: function(*): Promise<unknown>, batchOffline: function(*): Promise<unknown>, selectItem: function(*): Promise<unknown>, insert: function(*): Promise<unknown>, update: function(*): Promise<unknown>, remove: function(*): Promise<unknown>, insertOrUpdate: function(*, *): Promise<unknown>}}
 */
export const roiThresh = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/roithresh`, {
    selectItemResCopyField: 'roiThresh',
  }),
}

/**
 * Roi 预警表显示表
 */
export const roiAlert = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/stat/roialert`, {
    selectItemResCopyField: 'roiAlert',
  }),
}
