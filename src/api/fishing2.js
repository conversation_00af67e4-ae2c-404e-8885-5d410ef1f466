import { genRequestAPI } from '@/utils/request'

// 提现配置
export const withdrawalConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingwithdrawalconfig`, {
    selectItemResCopyField: 'fishingWithdrawalConfig',
  }),
}

// 任务
export const taskConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingtaskconfig`, {
    selectItemResCopyField: 'fishingTaskConfig',
  }),
}

// 基础配置
export const baseConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingbaseconfig`, {
    selectItemResCopyField: 'fishingBaseConfig',
  }),
}

export const fishingDrawConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingdrawconfig`, {
    selectItemResCopyField: 'fishingDrawConfig',
  }),
}

// 等级配置
export const fishingLevelConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishinglevelconfig`, {
    selectItemResCopyField: 'fishingLevelConfig',
  }),
}

// 业务配置表
export const businessConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/businessconfig`, {
    selectItemResCopyField: 'businessConfig',
  }),
}

// 图鉴配置
export const fishingCollectConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingcollectconfig`, {
    selectItemResCopyField: 'fishingCollectConfig',
  }),
}

// 用户积分
export const fishingUserCoinRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingusercoinrecord`, {
    selectItemResCopyField: 'fishingUserCoinRecord',
  }),
}

// 图鉴记录表
export const fishingCollectRecord = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingcollectrecord`, {
    selectItemResCopyField: 'fishingCollectRecord',
  }),
}

// ab测试用户
export const fishingAbTestResult = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingabtestresult`, {
    selectItemResCopyField: 'fishingAbTestResult',
  }),
}

// ab测试用户-配置表
export const fishingAbTestConfig = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishingabtestconfig`, {
    selectItemResCopyField: 'fishingAbTestConfig',
  }),
}

// 用户-账户表
export const fishingUserAccount = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/fishing2/fishinguseraccount`, {
    selectItemResCopyField: 'fishingUserAccount',
  }),
}
