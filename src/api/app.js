import { genRequestAPI, request } from '@/utils/request'

export function getAppVersion(params) {
  return request({
    url: '/apps/appversion/list',
    method: 'get',
    params,
  })
}

// app 信息管理模块
export const appRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/apps/app`, {
    selectItemResCopyField: 'app',
  }),
}

/**
 * 获取品牌列表
 * @param appCode {number}
 * @returns {Promise | Promise<unknown>}
 */
export function getBrandList({ appCode }) {
  return request({
    url: '/stat/activekeepdaily/brand_list',
    method: 'get',
    params: {
      appCode,
    },
  })
}
