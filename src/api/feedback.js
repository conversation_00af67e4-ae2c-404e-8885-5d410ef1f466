import { request } from '@/utils/request'

// 回传百分比

/**
 * 回传百分比：新增/修改
 * @param appId {string}
 * @param type {string} 回传类型：1：激活  2:次留   3:关键行为
 * @param adList {string} 广告主列表：_分割；例子：111_222_333
 * @param percent {number} 百分比: 小数一位
 * @param ruleId
 * @returns {Promise<unknown>}
 */
export function addOrUpdateDeduction({ appId, type, adList, percent, ruleId }) {
  return request({
    url: '/activate/activaterules/add_deduction',
    method: 'POST',
    data: {
      appId,
      type,
      adList,
      percent,
      ruleId,
    },
  })
}

/**
 * 回传百分比：获取
 * @param appId {string}
 * @param type {string} 回传类型：1：激活  2:次留   3:关键行为
 * @param adList {string} 广告主列表：_分割；例子：111_222_333
 * @param ruleId
 * @returns {Promise<unknown>}
 */
export function getDeduction({ appId, type, adList, ruleId }) {
  return request({
    url: '/activate/activaterules/get_deduction',
    method: 'POST',
    data: {
      appId,
      type,
      adList,
      ruleId,
    },
  })
}

/**
 * 回传百分比：删除
 * @param appId {string}
 * @param type {string} 回传类型：1：激活  2:次留   3:关键行为
 * @param adList {string} 广告主列表：_分割；例子：111_222_333
 * @param ruleId
 * @returns {Promise<unknown>}
 */
export function delDeduction({ appId, type, adList, ruleId }) {
  return request({
    url: '/activate/activaterules/del_deduction',
    method: 'POST',
    data: {
      appId,
      type,
      adList,
      ruleId,
    },
  })
}
