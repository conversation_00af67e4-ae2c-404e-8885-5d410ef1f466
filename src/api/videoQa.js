import { genRequestAPI } from '@/utils/request'

// 任务模块
export const taskConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guesstaskconfig`, {
    selectItemResCopyField: 'guessTaskConfig',
  }),
}

// 题库模块
export const questionStoreRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guessquestionstore`, {
    selectItemResCopyField: 'guessQuestionStore',
  }),
}

// 提现配置
export const withdrawalConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guesswithdrawalconfig`, {
    selectItemResCopyField: 'guessWithdrawalConfig',
  }),
}

// 连队加成配置
export const rewardAdditionConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guessrewardadditionconfig`, {
    selectItemResCopyField: 'guessRewardAdditionConfig',
  }),
}

// 用户等级
export const userLevelConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guessuserlevelconfig`, {
    selectItemResCopyField: 'guessUserLevelConfig',
  }),
}

// 风控
export const limitRuleRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guesslimitrule`, {
    selectItemResCopyField: 'guessLimitRule',
  }),
}

// 用户表
export const userRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guessuser`, {
    selectItemResCopyField: 'guessUser',
  }),
}

// 题库分类表
export const guessQuestionCategoryRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guessquestioncategory`, {
    selectItemResCopyField: 'guessQuestionCategory',
  }),
}

// 任务提现金额配置
export const guessTaskWithdrawalConfigRequest = {
  /**
   * CRUD
   */
  ...genRequestAPI(`/videoqa/guesstaskwithdrawalconfig`, {
    selectItemResCopyField: 'guessTaskWithdrawalConfig',
  }),
}
