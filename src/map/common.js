// 分类规则
export const ruleType = new Map([
  // ['city', '城市'],
  ['appVersion', 'app版本'],
  ['brand', '品牌'],
  ['activateTime', '激活时间'],
  ['channel', '渠道'],
  // ['newUserBlockedTime', '新用户屏蔽时长'],
])

// 所有的对比条件
export const conditionList = new Map([
  ['in', '包含'],
  ['notIn', '不包含'],
  ['>', '大于'],
  ['>=', '大于等于'],
  ['<', '小于'],
  ['<=', '小于等于'],
  ['==', '等于'],
  ['!=', '不等于'],
  ['between', '区间'],
])

// 根据key值筛选对比条件
export const getConditionList = key => {
  switch (key) {
    case 'city':
    case 'appVersion':
    case 'brand':
    case 'channel':
      return new Map([
        ['in', '包含'],
        ['notIn', '不包含'],
      ])
    case 'newUserBlockedTime':
      return new Map([['<=', '小于等于']])
    case 'activateTime':
      return new Map([
        ['>', '大于'],
        ['<', '小于'],
      ])
    default:
      return conditionList
  }
}

// 手机品牌,这里的Key只能小写
export const phoneBrand = new Map([
  ['huawei', 'HUAWEI'],
  ['honor', 'HONOR'],
  ['xiaomi', 'xiaomi'],
  ['redmi', 'Redmi'],
  ['vivo', 'vivo'],
  ['oppo', 'oppo'],
  ['lenovo', 'Lenovo'],
  ['360', '360'],
  ['hisense', 'Hisense'],
  ['gionee', 'GIONEE'],
  ['smartisan', 'SMARTISAN'],
  ['oneplus', 'OnePlus'],
  ['samsung', 'samsung'],
  ['meizu', 'Meizu'],
  ['realme', 'realme'],
])

// 版本号
export const versionList = new Map([
  ['1,0', '1.0'],
  ['2,0', '2.0'],
  ['3,0', '3.0'],
  ['4,0', '4.0'],
  ['5,0', '5.0'],
  ['6,0', '6.0'],
  ['7,0', '7.0'],
])

// 广告类型
export const adType = new Map([
  [0, '自渲染'],
  [1, '信息流'],
  [2, 'Banner'],
  [3, '开屏'],
  [4, '插屏广告'],
  [5, '激励视频'],
  [6, '全屏视频广告'],
  [7, 'draw信息流'],
])

// 快手
export const ksAdType = new Map([
  [1, '信息流'],
  [2, '激励视频'],
  [3, '全屏'],
  [4, '开屏'],
  [6, 'draw视频广告'],
  [13, '插屏'],
])

// 广告种类
export const adKind = new Map([
  [0, '应用内'],
  [1, '应用外'],
])

// 联盟类型
export const allianceType = new Map([
  [1, '穿山甲'],
  [2, '优量汇'],
  [3, '快手'],
])

// 广告开关状态
export const adSwitchStatus = new Map([
  [0, '关'],
  [1, '开'],
])

// 代码位类型
export const codeType = new Map([
  [1, '普通'],
  [2, '竞价'],
])

// 是否排他
export const isExclusion = new Map([
  [1, '排'],
  [2, '共享'],
])

// 代码位状态
export const codeSwitchStatus = new Map([
  [0, '关'],
  [1, '开'],
])

// 场景类型
export const sceneType = new Map([
  [1, '系统化'],
  [2, '场景化'],
  [3, '虚拟关闭'],
  [4, '交互引导'],
  [5, '承诺函'],
  [6, '分成告知'],
  [7, '广告联盟'],
])

// 场景状态
export const sceneStatus = new Map([
  [0, '关闭'],
  [1, '开启'],
  // [2, '屏蔽'],
])

// 时间插屏检查间隔开关
export const timeSceneTriggerStatus = new Map([
  [0, '关'],
  [1, '开'],
])

// 万能配置状态
export const confStatus = new Map([
  [0, '禁用'],
  [1, '正常'],
])

// 红包风控规则
export const packetRuleType = new Map([
  [1, '自动审核发放金额限制'],
  [2, '单个用户每日观看视频数上限'],
  [3, '单个用户每日获得金币上限'],
  [4, 'ecmp固定值'],
  [5, '人气值增加'],
  [6, '人气值减少'],
  [7, '刮刮卡金币【区】'], // 区间
  [8, '刮刮卡每日活动上限'],
  [9, '金猪随机发放金额【区】'], // 区间
  [10, '金币随机金币发放配【区】'], // 区间
  [11, '新人首次奖励'],
  [12, '普通签到【区】'], // 区间
  [13, '大转盘奖励金币【区】'], // 区间
  [14, '大转盘次数'],
  [15, '大转盘额外奖励1【区】'], // 区间
  [16, '大转盘额外奖励2【区】'], // 区间
  [17, '大转盘额外奖励3【区】'], // 区间
  [18, '刮刮卡奖励1'],
  [19, '刮刮卡奖励2'],
  [20, '刮刮卡奖励3'],

  [21, '每日任务奖励'],
  [22, '每日任务额外奖励1'],
  [23, '每日任务额外奖励2'],
  [24, '金币下限最小值'],
  [25, '红包雨时间段1【区】'], // 区间
  [26, '红包雨时间段2【区】'], // 区间
  [27, '红包雨单场金币上限值'],
  [28, '红包雨空红包概率'],
  [29, '红包雨红包个数'],
  [30, '红包雨单个红包数值区间【区】'], // 区间
  [31, '活动页固定金额1'],
  [32, '活动页固定金额2'],
  [33, '活动页固定金额3'],
  [34, '红包银行1【区】'], // 区间
  [35, '红包银行2【区】'], // 区间
  [36, '红包银行3【区】'], // 区间
  [37, '视频播放数第一档【区】'], // 区间
  [38, '视频播放数第二档【区】'], // 区间
  [39, '视频播放数第二档【区】'], // 区间
  [40, '离线奖励金币/分钟【固】'],
  [41, '平台收益金币发放系数【固】'],
  [42, '黄金以上会员收益/人【固】'],
  [43, '离线奖励限制【次数、金币上限制】'],
  [44, '铂金等级存钱罐多给的比例【固】'],
  [45, '钻石等级红包收益系数【固】'],
  [46, '积分领取机会【固】'],
  [47, '积分领取首次奖励区间值【区间】'],
])

// 记步风控规则
export const jibuRuleType = new Map([
  [1, '自动审核发放金额限制'],
  [2, '单个用户每日观看视频数上限'],
  [3, '单个用户每日获得金币上限'],
  [4, 'ecmp固定值'],
  [5, '刮刮卡每日活动上限'],
  [6, '新人首次奖励'],
  [7, '普通签到'], // 区间
  [8, '大转盘奖励金币'], // 区间
  [9, '大转盘次数'],
  [10, '大转盘额外奖励1'], // 区间 下限-次数 上限-金币数
  [11, '大转盘额外奖励2'], // 区间 下限-次数 上限-金币数
  [12, '大转盘额外奖励3'], // 区间 下限-次数 上限-金币数
  [13, '刮刮卡奖励1'],
  [14, '刮刮卡奖励2'],
  [15, '刮刮卡奖励3'],
  [16, '第1杯水'], // 区间 时间区间
  [17, '第2杯水'], // 区间 时间区间
  [18, '第3杯水'], // 区间 时间区间
  [19, '第4杯水'], // 区间 时间区间
  [20, '第5杯水'], // 区间 时间区间
  [21, '第6杯水'], // 区间 时间区间
  [22, '第7杯水'], // 区间 时间区间
  [23, '第8杯水'], // 区间 时间区间
  [24, '任务-看视频领金币'],
  [25, '步数-奖励档位1'],
  [26, '步数-奖励档位2'],
  [27, '步数-奖励档位3'],
  [28, '步数-奖励档位4'],
  [29, '步数-奖励档位5'],
])

// 风控规则状态
export const packetRuleStatus = new Map([
  [0, '未开启'],
  [1, '开启'],
])

// 直接激励视频
export const directIncentiveVideoStatus = new Map([
  [0, '否'],
  [1, '是'],
])

// 自动翻倍
export const isAutoDouble = new Map([
  [0, '否'],
  [1, '是'],
])

// 提现配置类型
export const packetCoinAssignConfigType = new Map([
  [1, '注册时间'],
  [2, '累计提现情况'],
  [3, '现有金币数'],
  [4, '人气值'],
  [5, '当前观看视频数'],
])

// 提现类型
export const withdrawConfigType = new Map([
  [1, '永久，依据次数判断'],
  [2, '非永久，根据间隔时间判断'],
])

// 提现配置状态
export const withdrawConfigStatus = new Map([
  [0, '未启用'],
  [1, '启用'],
])

// 提现状态
export const withdrawStatus = new Map([
  [0, '待审核'],
  [1, '待打款'],
  [2, '打款成功'],
  [3, '打款失败'],
  [4, '不予打款'],
  // [5, '注销中'],
  // [6, '已注销'],
  [7, '作废'],
])

// 风险等级
export const riskLevel = new Map([
  [0, '无风险'],
  [1, '无风险'],
  [2, '低风险'],
  [3, '中风险'],
  [4, '中高风险'],
  [5, '高风险'],
])

// 反作弊风险建议
export const antiCheating = new Map([
  [1, '已root'],
  [2, '已开启模拟定位'],
  [3, '使用模拟器'],
  [4, '无相机'],
  [5, '未插SIM卡'],
  [6, '已开启VPN'],
  [7, '已连接代理'],
  [8, '正在使用XPOSED'],
  [9, '正在使用双开应用'],
  [10, '已安装抓包证书'],
  [13, '未安装微信'],
])

// 风险建议标签
export const riskTags = new Map([
  [1, '无效用户'],
  [2, '刷子党'],
  [3, '测试设备'],
  [4, '黑产虚假用户'],
])

// AT 当前事件处理建议
export const atSuggestion = new Map([
  ['PASS', '通过'],
  ['REVIEW', '审核'],
  ['REJECT', '拒绝'],
])

// 金币操作类型
export const goldOperatorType = new Map([
  [1, '超过30天清零'],
  [2, '新人奖励'],
  [3, '首页悬浮金币'],
  [4, '提现成功'],
  [5, '注销账号'],
  [6, '刮刮卡'],
  [7, '大转盘'],
  [8, '首页-推荐观看视频'],
  [9, '普通签到'],
  [10, '高级签到（需要接收ecpm参数）'],
  [11, '累计签到'],
  [12, '翻倍奖励'],
  [13, '首页悬浮-继续抽奖励'],
  [14, '刮刮卡-继续抽奖励'],
  [15, '大转盘-继续抽奖励'],
  [16, '红包群奖励'],
  [17, '任务奖励'],
  [18, '每日总任务奖励'],
  [19, '存钱罐奖励金币'],
  [20, '红包雨奖励金币'],
  [21, '固定金额奖励'],
  [22, '储蓄扣除金币'],
  [23, '领取银行本金'],
  [24, '离线奖励'],
  [25, '黄金以上会员收益/人'],

  [26, '达标奖励'],
  [27, '穿插奖励大额金币'],
  [28, '穿插奖励沾喜气金币'],
  [29, '穿插奖励翻倍金币'],
])

// 记步金币操作类型
export const jibuGoldOperatorType = new Map([
  [1, '超过30天清零'],
  [2, '新人奖励'],
  [3, '首页悬浮金币'],
  [4, '提现成功'],
  [5, '注销账号'],
  [6, '刮刮卡奖励'],
  [7, '大转盘'],
  [8, '首页-推荐观看视频'],
  [9, '普通签到'],
  [10, '高级签到'],
  [11, '累计签到'],
  [12, '翻倍奖励(需要ecpm参数)'],
  [13, '在线奖励'],
  [14, '离线奖励'],
  [15, '随机奖励'],
  [16, '计步档位奖励'],
  [17, '任务-观看每日视频'],
  [18, '计步88奖励'],
  [19, '喝水奖励'],
  [20, '大转盘88奖励'],
])

// 用户状态
export const userStatus = new Map([
  [1, '正常'],
  [2, '注销待审核'],
  [3, '已注销'],
  [4, '封禁'],
])

// 是否自动审核
export const isAutoAudit = new Map([
  [0, '不审核'],
  [1, '人工审核'],
])

// 是否总收入
export const isSummaryStatus = new Map([
  [0, '否'],
  [1, '是'],
])

// 白名单开关
export const whiteClientStatus = new Map([
  [0, '关'],
  [1, '开'],
])

// app类型
export const appCategory = new Map([
  [1, '工具'],
  [2, '网赚'],
  [3, '快应用'],
  [4, '海外工具'],
])

// app 种类
export const appKind = new Map([
  [1, '软件'],
  [2, '游戏'],
])

const groupList = [
  // [1, '沐春'],
  // [2, '沐林'],
  // [3, '硕飞'],
  // [4, '意语'],
  // [5, '扶垚'],
  // [6, '沐宏'],
  // [10, '网赚'],
  [11, '快应用'],
  [12, '海外'],
]

// app 所属主体
export const appGroup = new Map(groupList)
// app 所属主体 带 全部
export const appGroupWithAll = new Map([[0, '全部']].concat(groupList))

// 授权主体
export const authMainIdList = new Map([
  [1, '云爱'],
  [2, '佳瑞'],
  [3, '意语'],
  [4, '沐林'],
  [5, '卓彩'],
  [6, '沐宏'],
  [7, '永昶'],
  [8, '快码加编'],
  [9, '镭穆'],
  [10, 'Speed Cleaner '],
])

// 授权主体对应的回调url
export const authMainIdUrl = new Map([
  [
    1,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1716459084419079&state=crowd_pack&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=i02h4maexl8',
  ],
  [
    2,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1717212765585448&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=n3y7syzcoy7',
  ],
  [
    3,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1717125173817352&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=p9gnppl9io8',
  ],
  [
    4,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1717093058509831&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=e3an1zuf1d',
  ],
  [
    5,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1719001644113934&state=crowd_pack&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=8ej1uldcv77',
  ],
  [
    6,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1727515897766013&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=y0fk7ll9nhn',
  ],
  [
    7,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1727529883492382&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=s4dno5zmj2',
  ],
  [
    8,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1747565880301576&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%2C8%2C9%2C11000000%2C12000000%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=z1g7bm10vi',
  ],
  [
    9,
    'https://open.oceanengine.com/audit/oauth.html?app_id=1749648283402283&state=your_custom_params&scope=%5B10000000%2C1%2C2%2C3%2C4%2C5%2C8%2C9%2C11000000%2C12000000%5D&material_auth=1&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=q84qtt00d0j',
  ],
  [
    10,
    'https://ads.tiktok.com/marketing_api/auth?app_id=7171659066018627586&state=your_custom_params&redirect_uri=http%3A%2F%2Fcms.fat.muchuntech.com%2F%23%2Flogin&rid=f02uwzoxpke',
  ],
])

// app 状态
export const appStatus = new Map([
  [0, '停用'],
  [1, '正常'],
])

// app 所属平台
export const appPlatform = new Map([
  [1, '安卓'],
  [2, 'IOS'],
])

// app 分类，key值不能修改
export const appClassification = new Map([
  [1, 'WiFi'],
  [2, '天气'],
  [3, '放大镜'],
  [4, '海外工具'],
  [5, '网赚'],
  [6, '相机'],
  [7, '手电筒'],
])

// 回传目标
export const returnTargetList = new Map([
  // [1, '激活'],
  // [2, '次留'],
  [3, '关键行为'],
  // [4, '激活+次留'],
])

// 回传档位对应的条件
export const returnGearCondition = {
  // ecpm
  ecpm: 1,
  // 激励视频
  rewardedVideo: 1 << 1,
  // 全屏视频
  fullScreen: 1 << 2,
}

// 回传条件
export const returnTypeList = [
  {
    id: 1,
    text: 'ecpm+激励视频',
    activityReturnType: 1,
    gear: returnGearCondition.ecpm | returnGearCondition.rewardedVideo,
    isSingle: false,
  },
  {
    id: 2,
    text: 'ecpm+全屏视频',
    activityReturnType: 1,
    gear: returnGearCondition.ecpm | returnGearCondition.fullScreen,
    isSingle: false,
  },
  {
    id: 3,
    text: 'ecpm+激励视频或全屏视频',
    activityReturnType: 1,
    gear:
      returnGearCondition.ecpm |
      returnGearCondition.rewardedVideo |
      returnGearCondition.fullScreen,
    isSingle: false,
  },
  {
    id: 4,
    text: '单ecpm',
    activityReturnType: 1,
    gear: returnGearCondition.ecpm,
    isSingle: true,
  },
  {
    id: 5,
    text: '单激励视频',
    activityReturnType: 2,
    gear: returnGearCondition.rewardedVideo,
    isSingle: true,
  },
]

// 激活回传条件
export const activityReturnTypeList = new Map([
  [1, '回传ecpm'],
  [2, 'aipu'],
])

// 入口类型
export const entranceTypeList = new Map([
  [1, '每日任务'],
  [2, '额外奖励1'],
  [3, '额外奖励2'],
  [4, '成就任务'],
])

// 任务类型
export const taskTypeList = new Map([
  [1, '签到'],
  [2, '大转盘'],
  [3, '红包群'],
  [4, '刮刮卡'],
  [5, '看视频'],
  [6, '首页悬浮现金币'],
  [7, '红包雨'],
  [110, '每日任务'],
])

// 提现显示类型
export const withdrawalLimitList = new Map([
  [1, '无限制'],
  [2, '任务完成要求'],
  [3, '视频观看次数要求'],
  // [4, '累计打卡限制'],
  [5, '累计签到限制'],
  // [6, '完成一次x元宝提现'],
])
// 累计签到限制

// ecpm 计算类型
export const ecpmCountTypeList = new Map([
  [1, '平均ecpm'],
  [2, '累计ecpm'],
  // [3, '前N次的和或者均值'],
  // [4, '最后N次的和或者均值'],
  // [5, '所有最低值'],
])

// 回传策略：适用类型
export const beApplicableTypeList = new Map([
  [0, '通用'],
  [1, '双出价'],
])

// 策略类型
export const strategyTypeList = new Map([
  [1, '激活'],
  [2, '次留'],
  [3, '关键行为'],
  [4, '衍生关键行为'],
  [5, '付费行为'],
])

export const con1List = new Map([
  ['page_show', '到首页'],
  ['device_ping', '心跳'],
  ['setup_complete', '键盘设置数'],
  ['stay_keyboard_day_sling', '键盘吊起数'],
])

export const con2List = new Map([
  ['advertise_exposure', '广告曝光次数'],
  ['exposure_ecpm', '广告曝光ECPM'],
  ['reward_exposure', '激励视频次数'],
  ['reward_ecpm', '激励视频ECPM'],
  ['key_ecpm', '关键ECPM'],
  ['key_avg_ecpm', '关键平均ECPM'],
  ['reward_ecpm_first', '首次激励视频ECPM '],
])

// 渠道
export const channelNameList = [
  'APK',
  '穿山甲',
  '优量汇',
  '快手',
  '百度',
  'tiktok',
  'google',
  'organic',
  'mint',
  'facebook',
  'huawei',
  'other',
]
export const channelNameListMap = new Map([
  [0, 'APK'],
  [1, '穿山甲'],
  [2, '优量汇'],
  [3, '快手'],
  [4, '百度'],
])

// 阿里云风险建议
export const alyRiskAdviceList = new Map([
  ['is_emulator', ['设备疑似模拟器', '⾼⻛险']],
  ['is_hooked', ['疑似注⼊式攻击', '⾼⻛险']],
  ['is_rooted', ['设备疑似root', '⾼⻛险']],
  ['is_virtual', ['疑似多开环境', '⾼⻛险']],
  ['is_deviceCluster_h', ['疑似群控设备⾼⻛险', '⾼⻛险']],
  ['is_autoOperation', ['疑似机器⾏为（⾃动化操作）', '⾼⻛险']],
  ['is_clickFarmDevice', ['刷量⼯作室设备', '⾼⻛险']],
  ['is_scriptControl', ['疑似脚本控制操作', '⾼⻛险']],
  ['is_systemVirtual', ['疑似系统多开（系统多开）', '⾼⻛险']],
  ['is_tampered', ['疑似篡改设备信息', '⾼⻛险']],
  [
    'is_existedHighRiskDevice',
    ['当前设备历史有被识别为⾼⻛险操作⾏为', '中⾼⻛险'],
  ],
  ['is_deviceCluster_m', ['疑似群控设备中⻛险', '中⾼⻛险']],
  ['is_openVpn', ['疑似开启VPN', '中⾼⻛险']],
  ['is_cloudIp', ['疑似云ip', '中⾼⻛险']],
  ['is_natIp', ['疑似出⼝IP', '中⾼⻛险']],
  ['is_idcIp', ['疑似IDC机房ip', '中⾼⻛险']],
  ['token_replay', ['oken重放', '中⻛险']],
  ['token_invalid', ['⽆效Token', '中⻛险']],
  ['time_over', ['Token超时', '中⻛险']],
])
