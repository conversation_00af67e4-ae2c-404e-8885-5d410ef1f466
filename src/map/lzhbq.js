/**
 * 乐赚红包群
 */

// 金币发放规则配置 - 阶段类型
export const levelTypeList = new Map([
  [1, 'ecpm区间数值衰减系数x'],
  [2, '激励视频个数区间衰减系数y'],
  [3, '原来风控管理，固定ecpm扩展系数z'],
])

// 金币发放规则配置 - 是否开启
export const coinGrantRuleStatus = new Map([
  [1, '启用'],
  [2, '不启用'],
])

// 金币发放规则配置 - 是否开启
export const userLevelConfigStatus = new Map([
  [1, '启用'],
  [2, '不启用'],
])

// 金币发放规则配置 - 积分衰减发放规则
export const integralLevelTypeList = new Map([
  [1, '剩余积分规则，ecpm固定扩展'],
  [2, '3000积分后，ecpm区间数值衰减系数X'],
  [3, '3000积分后，激励视频积分数随机系数Y'],
  [4, '3000积分后，固定ecpm扩展,系数z展'],
])

//  是否开启 -  积分衰减发放规则
export const integralGrantRuleStatus = new Map([
  [1, '启用'],
  [2, '不启用'],
])

// 是否需要审核 - 积分提现金额配置
export const needAuditList = new Map([
  [0, '不需要'],
  [1, '需要'],
])

// 状态 - 积分提现金额配置
export const integralWithdrawGrantRuleStatus = new Map([
  [0, '未启用'],
  [1, '启用'],
])

// 积分操作类型
export const operatorTypeList = new Map([
  [1, '首次奖励'],
  [2, '第二次奖励'],
  [3, '普通奖励'],
])

// 提现类型
export const categoryList = new Map([
  [1, '金币提现'],
  [2, '积分提现'],
])

// 风险建议标签
export const riskTags = new Map([
  [1, '安全测试设备'],
  [2, '流量欺诈用户'],
  [3, '品类刷子用户'],
  [4, 'ROOT/多开/环境伪造设备'],
  [5, '社交欺诈用户'],
])

// 入口类型
export const entranceTypeList = new Map([
  [1, '每日任务'],
  [2, '额外奖励1'],
  [3, '额外奖励2'],
])

// 任务类型
export const taskTypeList = new Map([
  [1, '签到'],
  [2, '大转盘'],
  [3, '红包群'],
  [4, '刮刮卡'],
  [5, '看视频'],
  [6, '首页悬浮现金币'],
  [7, '红包雨'],
  // [110, '每日任务'],
])
