// 回传配置状态
export const activateRulesStatus = new Map([
  [0, '禁用'],
  [1, '启用'],
])

// 回传方式
export const callbackCategoryList = new Map([
  [1, '双出价'],
  [2, '关键行为'],
  [3, '衍生行为'],
])

// 统计维度
export const statTypeList = new Map([
  [1, '规则'],
  [2, '策略'],
])

// 1 激活 2 次留 3 双出价，
export const callbackTypeList = new Map([
  [1, '激活'],
  [2, '次留'],
  [3, '双出价'],
])

// 次留类型
export const aliveTypeList = new Map([
  [1, 'APP留存'],
  [2, 'UI留存'],
])

// 激活条件
export const actTypeList = new Map([
  [1, '到首页'],
  [2, '心跳方式'],
  [3, '广告曝光次数'],
  [4, '广告点击次数'],
  [5, '观看时长'],
  // [6, '广告曝光ECPM'],
  // [7, 'GPT激活'],
  [8, '平均ECPM'],
])

// 事件类型
export const keyActionList = new Map([
  ['key_action1', '关键行为衍生事件一'],
  ['key_action2', '关键行为衍生事件二'],
  ['key_action3', '关键行为衍生事件三'],
  ['key_action4', '关键行为衍生事件四'],
  ['key_action5', '关键行为衍生事件五'],
])
