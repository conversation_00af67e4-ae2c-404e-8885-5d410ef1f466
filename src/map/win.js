/**
 * 谁是聚赢家
 */

export const taskStatusList = new Map([
  [0, '未启用'],
  [1, '启用'],
])

// 打卡形式
export const clockTypeList = new Map([[1, '累计打卡']])

// 打卡条件
export const clockConditionList = new Map([[1, '激励视频']])

// 是否需要审核
export const needAuditList = new Map([
  [0, '不需要'],
  [1, '需要'],
])

// 打款状态
export const makePaymentStatusList = new Map([
  [0, '待审核'],
  [1, '待打款'],
  [2, '打款成功'],
  [3, '打款失败'],
  [4, '不予打款'],
  [5, '注销中'],
  [6, '已注销'],
  [7, '作废'],
])

// 风险等级
export const riskLevelList = new Map([
  [1, '无风险'],
  [2, '低风险'],
  [3, '中风险'],
  [4, '中高风险'],
  [5, '高风险'],
  [6, '风控异常'],
])

// 元宝操作类型
export const operatorTypeList = new Map([
  [1, '元宝30天清零'],
  [2, '惊喜元宝'],
  [3, '看视频领元宝'],
  [4, '元宝提现成功'],
  [5, '元宝任务奖励'],
  [13, '转圈元宝'],
  // [21, '转圈红包固定金币'],
  [19, '存钱罐奖励'],
  [22, '转圈红包小额奖励'],
  [23, '转圈红包大额奖励'],
  [24, '宝箱奖励'],
  [25, '任务大额奖励'],
  [26, '直接打卡奖励'],
])

// 金币操作类型
export const goldOperatorType = new Map([
  [1, '金币30天清零'],
  [2, '新人奖励'],
  // [3, '首页悬浮金币'],
  [4, '金币提现成功'],
  [5, '注销账号'],
  // [6, '刮刮卡'],
  // [7, '大转盘'],
  // [8, '首页-推荐观看视频'],
  // [9, '普通签到'],
  // [10, '高级签到'],
  // [11, '累计签到'],
  // [12, '翻倍奖励'],
  [13, '转圈金币'],
  // [14, '刮刮卡-继续抽奖励'],
  // [15, '大转盘-继续抽奖励'],
  // [16, '红包群奖励'],
  [17, '金币任务奖励'],
  [18, '每日总任务奖励'],
  [19, '存钱罐奖励金币'],
  [20, '打卡奖励的金币'],
  [21, '转圈红包固定金币'],
  [22, '转圈红包小额奖励'],
  [23, '转圈红包大额奖励'],
  [24, '宝箱奖励'],
  [25, '任务大额奖励'],
])

// 提现分类
export const withdrawTypeList = new Map([
  [1, '金币提现'],
  [2, '元宝提现'],
])

// 任务列表 发放金币还是元宝
export const rewardTypeList = new Map([
  [1, '金币'],
  [2, '元宝'],
])

// 任务类型
export const taskTypeList = new Map([
  // [1, '签到'],
  // [2, '大转盘'],
  // [3, '红包群'],
  // [4, '刮刮卡'],
  [5, '看视频'],
  // [6, '首页悬浮现金币'],
  [7, '红包领取次数'],
  [8, '每日打卡'],
  [9, '惊喜礼包'],
  [22, '转圈红包激励视频'],
  [23, '宝箱视频'],
  [25, '每日领取金猪奖励次数'],
])

// 风控规则
export const packetRuleType = new Map([
  [1, '自动审核发放金额限制'],
  [2, '单个用户每日观看视频数上限'],
  [3, '单个用户每日获得金币上限'],
  [4, 'ecmp固定值'],
  [5, '人气值增加'],
  [6, '人气值减少'],
  [7, '刮刮卡金币'], // 区间
  // [8, '刮刮卡每日活动上限'],
  [9, '存钱罐比例系数'], // 区间
  [10, '金币随机金币发放配'], // 区间
  [11, '新人首次奖励'],
  // [12, '普通签到'], // 区间
  // [13, '大转盘奖励金币'], // 区间
  // [14, '大转盘次数'],
  // [15, '大转盘额外奖励1'], // 区间
  // [16, '大转盘额外奖励2'], // 区间
  // [17, '大转盘额外奖励3'], // 区间
  // [18, '刮刮卡奖励1'],
  // [19, '刮刮卡奖励2'],
  // [20, '刮刮卡奖励3'],
  [30, '转圈红包区间值'],
  [31, '转圈红包每日上限'],
  [32, '宝箱每日上限'],
  [33, '低保元宝奖励'],
])

// 元宝发放规则配置 - 阶段类型
export const levelTypeList = new Map([
  [1, 'ecpm区间数值衰减系数x'],
  [2, '激励视频个数区间衰减系数y'],
  [3, '原来风控管理，固定ecpm扩展系数z'],
])

// 元宝发放规则配置 - 是否启用
export const ingotGrantRuleEnabled = new Map([
  [1, '启用'],
  [2, '不启用'],
])

// 金币发放规则 阶段类型
export const coinGrantRuleLevelType = new Map([[1, '当前用户金额总数']])

// 金币发放规则 是否启用
export const coinGrantEnabled = new Map([
  [1, '启用'],
  [2, '不启用'],
])

// 提现显示类型
export const withdrawalLimitList = new Map([
  [1, '无限制'],
  [2, '任务完成要求'],
  [3, '视频观看次数要求'],
  [5, '累计打卡限制'],
  [6, '提现一次x元元宝'],
])
