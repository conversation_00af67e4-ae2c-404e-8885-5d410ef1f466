export const novelRecommendPositionMap = {
  // 推荐位置
  recommendPosition: new Map([
    [1, '书架头部'],
    [2, '我的书架'],
    [3, '书城热门'],
  ]),
}

export const novelBookInfoMap = {
  resource: new Map([[1, '追书']]),
}

//
export const comicRecommendPositionMap = {
  // 推荐位置
  recommendPosition: new Map([
    [1, '热门推荐区'],
    [2, '四格区'],
    [3, '书架推荐'],
    [4, '爱阅读书架'],
  ]),
}

export const ocrMap = {
  userStatus: new Map([
    [0, '非会员'],
    [1, '体验会员'],
    [2, '终身会员'],
    [3, '月度会员'],
  ]),
  handleStatus: new Map([
    [0, '无需处理'],
    [1, '处理中'],
    [2, '已处理'],
    [3, '待退款'],
    [4, '已撤销权益'],
  ]),
  payStatus: new Map([
    [1, '未付款'],
    [2, '已付款'],
    [3, '系统退款'],
    [4, '主动退款'],
  ]),
  payMethods: new Map([
    [1, '支付宝'],
    [2, '微信'],
  ])
}