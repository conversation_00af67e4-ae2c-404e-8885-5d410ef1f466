// 提现配置
export const withdrawalConfigMap = {
  typeList: new Map([
    [1, '常规'],
    [2, '新任'],
    [3, '提现鱼提现'],
  ]),
}

// 任务
export const taskConfigMap = {
  typeList: new Map([
    [1, '钓鱼数量'],
    [2, '钓指定的鱼 '],
    [3, '限时鱼群活动'],
  ]),
}

// 配置
export const drawConfigMap = {
  typeList: new Map([
    [1, '转盘'],
    [2, '骰子 '],
  ]),
}

export const userInfoMap = {
  userStatus: new Map([
    [1, '正常'],
    [2, '注销待审核'],
    [3, '已注销'],
    [4, '封禁'],
    [5, '微信未登录'],
  ]),
  // 风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险 6:风控校验失败（自定义）
  riskLevel: new Map([
    [1, '无风险'],
    [2, '低风险'],
    [3, '中风险'],
    [4, '中高风险'],
    [5, '高风险'],
    [6, '风控校验失败'],
  ]),
}

// 用户积分明细
export const userCoinRecordMap = {
  // 金币操作类型
  operateList: new Map([
    [1, '新人现金鱼奖励'],
    [2, '任务奖励'],
    [3, '现金奖励'],
    [4, '现金鱼奖励'],
    [5, '现金鱼翻倍奖励'],
    [6, '红包鱼奖励'],
    [7, '提现成功'],
  ]),
}

export const baseConfigMap = {
  unlockType: new Map([
    [0, '钓到解锁'],
    [1, '看视频解锁'],
    [2, '不可解锁'],
  ]),
  rewardType: new Map([
    [0, '无'],
    [1, '现金'],
    [2, '次数'],
    [3, '现金公式'],
  ]),
}

// 提现审核
export const auditMap = {
  // 风险建议标签
  riskTags: new Map([
    [1, '安全测试设备'],
    [2, '流量欺诈用户'],
    [3, '品类刷子用户'],
    [4, 'ROOT/多开/环境伪造设备'],
    [5, '社交欺诈用户'],
  ]),
}
