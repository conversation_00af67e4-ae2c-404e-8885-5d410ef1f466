// 视频问答

// 任务类型
export const taskTypeList = new Map([
  [1, '答题任务'],
  [2, '跳转任务'],
  [3, '单次任务'],
])

export const taskEnabledList = new Map([
  [0, '下线'],
  [1, '上线'],
])

// 任务跳转类型
export const jumpTypeList = new Map([
  [1, '答题页'],
  [2, 'H5'],
])

// 题库
export const questionMap = {
  // 分类
  categoryList: new Map([
    ['', '全部分类'],
    [2, '武侠'],
    [3, '神话'],
    [4, '传奇'],
    [5, '喜剧'],
    [6, '爱情'],
    [7, '战争'],
    // [8, '审核'],
    [9, '音乐 '],
    [10, '舞蹈 '],
    [98, '拍摄素材'],
    [99, '图片审核'],
    [100, '音频审核'],
  ]),
  // 状态
  statusList: new Map([
    [0, '下线'],
    [1, '上线'],
  ]),
  // 题目类型
  typeList: new Map([
    [1, '二选一'],
    [2, '四选一'],
  ]),
}

// 提现配置
export const withdrawalConfigMap = {
  // 提现类型
  typeList: new Map([
    [1, '一次性'],
    [2, '限时性'],
    [3, '每日刷新'],
    [4, '抽奖欺骗'],
  ]),
  // 状态
  statusList: new Map([
    [0, '下线'],
    [1, '上线'],
  ]),
  // 是否需要审核
  needAuditList: new Map([
    [0, '机审'],
    [1, '人审'],
  ]),
  // 提现限制
  withdrawalLimitList: new Map([
    [1, '无限制'],
    [2, '任务完成要求'],
    [3, '视频观看次数要求'],
    [5, '累计签到次数'],
  ]),
}

// 连队加成配置
export const rewardAdditionConfigMap = {
  enabledList: new Map([
    [1, '启用'],
    [2, '不启用'],
  ]),
}

// 用户等级
export const userLevelConfigMap = {
  enabledList: new Map([
    [1, '启用'],
    [2, '不启用'],
  ]),
}

// 提现限制
export const limitRuleMap = {
  ruleTypeList: new Map([
    [1, '自动审核发放金额限制'],
    [2, '单个用户每日观看视频数上限'],
    [3, '单个用户每日获得金币上限'],
    [4, 'ecmp固定值'],
    [5, '人气值增加'],
    [6, '人气值减少'],
    [7, '刮刮卡金币'],
    [8, '刮刮卡每日活动上限'],
    [9, '金猪随机发放金额'],
    [10, '金币随机金币发放配置'],
  ]),
  statusList: new Map([
    [0, '不启用'],
    [1, '启用'],
  ]),
}

export const userInfoMap = {
  userStatus: new Map([
    [1, '正常'],
    [2, '注销待审核'],
    [3, '已注销'],
    [4, '封禁'],
    [5, '微信未登录'],
  ]),
  // 风险等级：1：无风险，2：低风险，3:中风险，4：中高风险，5：高风险 6:风控校验失败（自定义）
  riskLevel: new Map([
    [1, '无风险'],
    [2, '低风险'],
    [3, '中风险'],
    [4, '中高风险'],
    [5, '高风险'],
    [6, '风控校验失败'],
  ]),
}
