{"name": "fast-vue", "version": "1.0.0", "private": true, "scripts": {"dev": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "dev:win": "vue-cli-service serve", "build2": "export NODE_OPTIONS=--openssl-legacy-provider && gulp", "build": "gulp", "build:win": "SET NODE_OPTIONS=--openssl-legacy-provider && gulp", "lint": "vue-cli-service lint", "postinstall": "patch-package", "build:fat": "npm run build --fat", "build:fat:win": "npm run build:win --fat", "preview": "serve ./dist"}, "dependencies": {"af-table-column": "^1.0.3", "async-validator": "^4.1.1", "await-to-js": "^3.0.0", "axios": "^0.21.1", "babel-plugin-component": "^1.1.1", "core-js": "^3.6.5", "dayjs": "^1.10.6", "decimal.js": "^10.5.0", "dom-to-image": "^2.6.0", "echarts": "^5.3.3", "element-ui": "^2.15.7", "file-saver": "^2.0.5", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-load-plugins": "^2.0.6", "gulp-replace": "^1.0.0", "gulp-shell": "^0.8.0", "lodash": "^4.17.21", "prismjs": "^1.24.1", "qs": "^6.12.1", "sass-loader": "^8.0.2", "serve": "^12.0.1", "sortablejs": "^1.15.2", "url-loader": "^4.1.1", "vue": "^2.6.11", "vue-cookie": "^1.1.4", "vue-echarts": "^6.1.0", "vue-json-editor": "^1.4.3", "vue-prism-editor": "^1.2.2", "vue-router": "^3.5.2", "vuex": "^3.4.0", "vxe-table": "3.5.8", "xe-utils": "^3.5.4", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.11", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/composition-api": "^1.6.2", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "fancy-log": "^1.3.3", "gulp-cli": "^2.3.0", "html-webpack-plugin": "^4.5.0", "patch-package": "^6.4.7", "postinstall-postinstall": "^2.1.0", "sass": "^1.49.8", "script-loader": "^0.7.2", "svg-sprite-loader": "^5.2.1", "vue-template-compiler": "^2.6.11", "webpack": "^4.44.0", "webpack-xlsx-loader": "^1.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "config": {"unsafe-perm": true}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}